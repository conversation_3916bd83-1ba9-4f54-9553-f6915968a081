import { http } from "@/utils/request/service";

/**
 * 工单池分页列表
 * @param {
 * {
 * pageNumber:1,
 * pageSize:10,
 * orderBy:排序字段,
 * engineerId:工程师id,
 * waitReceived: true-待接工单,
 * }
 * } params
 * @returns
 */
export async function getWorkOrderList(params) {
  let url = "/api/engineer/work-order/waitReceived";
  return await http.post(url, params);
}
// 指派给我的待接工单
/**
 *
 * @param {
 * {
 * pageNumber:1,
 * pageSize:10,
 * orderBy:排序字段,
 * engineerId:工程师id,
 * waitReceived: true-待接工单,
 * }} params
 * @returns
 */
export async function getWorkOrderListByMe(params) {
  let url = "/api/engineer/work-order/assignToMe";
  return await http.post(url, params);
}

/**
 * 获取工单详情
 * @param {id} id
 * @returns
 */
export async function getWorkOrderDetail(id) {
  let url = `/api/engineer/work-order/detail/${id}`;
  return await http.get(url);
}

// 工程师接单
/**
 *
 * @param {
 * {
 * id:工单id,
 * prospectArriveTime:预计上门时间,
 * fixPrice: 是否调整金额,false:保持原价,
 * additionalPrice: 加价金额,当选择为<加价>时使用,
 * discountPrice: 减免金额；当选择为<减免>时使用
 * }
 * } params
 * @returns
 */
export async function acceptWorkOrder(params) {
  let url = "/api/engineer/work-order/receive";
  return http.put(url, params);
}

// 工程师取消
export async function cancelWorkOrder(id) {
  let url = `/api/engineer/work-order/cancel`;
  return await http.put(url, id);
}
// 工程师同意取消
export async function cancelAcceptCancelOrder(id) {
  let url = `/api/engineer/work-order/acceptCancel`;
  return await http.put(url, id);
}

// 工程师出发
export async function goWorkOrder(params) {
  let url = `/api/engineer/work-order/departure`;
  return await http.put(url, params);
}

// 工程师确认到店
export async function arriveWorkOrder(params) {
  let url = `/api/engineer/work-order/arrive`;
  return await http.put(url, params);
}

// 工程师减免费用
export async function discountWorkOrder(params) {
  let url = `/api/work-order/fix-derate`;
  return await http.post(url, params);
}
// 工程师追加费用
export async function addWorkOrderFee(params) {
  let url = `/api/work-order/fix-additional`;
  return await http.post(url, params);
}
// 维修工程师可选列表
export async function engineerList(productId) {
  let url = `/api/engineer-info/enablePcList/${productId}`;
  return await http.get(url);
}
// 获取所有工程列表
export async function getAllEngineerList(id, data) {
  let url = `/api/dict-extend/member-page/${id}`;
  return await http.get(url, data);
}
// 换机、安装工单转让
export async function transferInstallWorkOrder(params) {
  let url = `/api/install-order/transfer`;
  return await http.post(url, params);
}
// 维修工单转让
export async function assignEngineer(params) {
  let url = `/api/work-order/transfer`;
  return await http.post(url, params);
}
// 工程师撤回维修报告
export async function recallWorkOrder(id) {
  let url = `/api/engineer/work-order/revoke/${id}`;
  return await http.put(url);
}
/**
 * 我的工单列表
 * @param {
 * {
 * pageNumber:1,
 * pageSize:10,
 * orderBy:排序字段,
 * engineerId:工程师id,
 * waitReceived: true-待接工单,
 * }} params
 * @returns
 */
// ==================================  维修工单  ==================================
// 工单列表
export async function workOrderList(params) {
  let url = "/api/work-order-pc/page";
  return await http.post(url, params);
}
// 分配工单
export async function assignWorkOrder(productId) {
  let url = "/api/engineer-info/enablePcList/" + productId;
  return await http.get(url);
}
// 确认分配工单
export async function confirmAssignWorkOrder(params) {
  let url = "/api/work-order-pc/changeEngineer";
  return await http.post(url, params);
}
// 维修工单
export async function myWorkOrderList(params) {
  let url = "/api/engineer/work-order/myWorkOrder";
  return await http.post(url, params);
}
// 历史维修记录
export async function historyWorkOrderList(deviceGroupId) {
  let url = `/api/engineer/work-order/hisRepair/${deviceGroupId}`;
  return await http.get(url);
}

// pm件跟换记录
export async function historyPmReplace(deviceGroupId) {
  let url = `/api/engineer/work-order/hisPmReplace/${deviceGroupId}`;
  return await http.get(url);
}
// 暂存报告
export async function savePopReport(params) {
  let url = `/api/engineer/work-order/stashReport`;
  return await http.post(url, params);
}
// 拉取暂存报告
export async function getPopReport(id) {
  let url = `/api/engineer/work-order/popReport/${id}`;
  return await http.get(url);
}

// 工程师填写报告
export async function submitReport(params) {
  let url = `/api/engineer/work-order/saveReport`;
  return await http.post(url, params);
}

// 字典树数据
export async function getDictTreeData(dictCode) {
  let url = `/api/magina/api/code/dict-item/${dictCode}/tree`;
  return await http.get(url);
}
// 字典树数据
export async function getIgnoreLocation() {
  let url = `/api/engineer/work-order/ignoreLocation`;
  return await http.get(url);
}

// 查看工程师当前机型的领用耗材
export async function getCurrentModelsConsumables(params) {
  let url = `/api/engineerItemStore/listByProductId`;
  return await http.post(url, params);
}

// 根据机型查看客户商城列表
export async function getCustomerItemStore(params) {
  let url = `/api/engineerItemStore/listCustomerItemStore`;
  return await http.post(url, params);
}

// 维修报告详情
export const reportDetailsApi = (id) =>
  http.get(`/api/engineer/work-order/getReport/${id}`);

// 根据零件id查询位置
export const getLocationByPartId = (partId) => {
  return http.get(`/api/engineerItemStore/queryPositionByPartId/${partId}`);
};

// 实时计算印量
export const calcPrintCountApi = (params) => {
  return http.post(`/api/work-order/calcPrintCount`, params);
};

// pm件更换记录
export const hisPmReplace = (id) =>
  http.get(`/api/engineer/work-order/hisPmReplace/${id}`, {});

// 字典项查询
export const dictTreeByCodeApi = (code) =>
  http.get(`/api/magina/api/code/dict-item/${code}/tree`);

// ==================================  安装工单  ==================================
// 安装工单列表
export async function myInstallWorkOrderList(params) {
  let url = "/api/install-order/page";
  return await http.post(url, params);
}
// 工单详情
export async function installWorkOrderDetail(id) {
  let url = `/api/install-order/${id}`;
  return await http.get(url);
}
// 工程师出发安装
export async function engineerArriveInstall(id) {
  let url = `/api/install-order/departure/${id}`;
  return await http.put(url);
}
// 填写安装报告
export async function submitInstallReport(params) {
  let url = `/api/install-order/complete`;
  return await http.put(url, params);
}

<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 旧版应用兼容颜色 -->
    <color name="colorPrimary">#007AFF</color>
    <color name="colorPrimaryDark">#0062CC</color>
    <color name="colorPrimaryLight">#4C9EFF</color>
    <color name="colorAccent">#FF9500</color>
    <color name="textColorPrimary">#000000</color>
    <color name="textColorSecondary">#666666</color>

    <!-- 新的颜色定义 -->
    <color name="primary">#007AFF</color>
    <color name="primary_dark">#0062CC</color>
    <color name="accent">#FF9500</color>
    <color name="accent_dark">#CC7800</color>
    <color name="success">#34C759</color>
    <color name="danger">#FF3B30</color>
    <color name="warning">#FFCC00</color>
    <color name="info">#5AC8FA</color>
    
    <color name="background_light">#F2F2F7</color>
    <color name="background_gray">#F0F0F0</color>
    <color name="background_image">#E5E5EA</color>
    <color name="background_color">#F5F5F5</color>
    
    <!-- 文字颜色 -->
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_tertiary">#BDBDBD</color>
    <color name="text_hint">#999999</color>
    <color name="text_label">#8E8E93</color>
    <color name="text_content">#333333</color>
    <color name="text_price">#FF3B30</color>
    <color name="divider">#D8D8D8</color>
    <color name="stroke_light">#E5E5EA</color>
    <color name="progress_track">#E5E5EA</color>
    <color name="white">#FFFFFF</color>
    <color name="price">#FF3B30</color>
    <color name="price_red">#FF3B30</color>
    
    <!-- 状态颜色 -->
    <color name="status_pending">#FF9500</color>
    <color name="status_processing">#007AFF</color>
    <color name="status_waiting">#5856D6</color>
    <color name="status_completed">#34C759</color>
    <color name="status_cancelled">#8E8E93</color>
    <color name="status_success">#34C759</color>
    <color name="status_warning">#FF9500</color>
    <color name="status_error">#FF3B30</color>
    <color name="status_primary">#007AFF</color>
    <color name="status_secondary">#8E8E93</color>
    <color name="status_info">#5AC8FA</color>
    <color name="black">#FF000000</color>

    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>

    <!-- 主题颜色 -->
    <color name="base_color">#fe3b0f</color>
    <color name="assist_color">#ff4e17</color>
    <color name="change_color">#fc603a</color>
    <color name="floor_color">#fafafa</color>
    <color name="price_color">#FF0000</color>
    
    <!-- 页面颜色 -->
    <color name="page_color_base">#f8f8f8</color>
    <color name="page_color_light">#f8f6fc</color>
    
    <!-- 文字颜色 -->
    <color name="font_color_dark">#303133</color>
    <color name="font_color_base">#606266</color>
    <color name="font_color_light">#909399</color>
    <color name="font_color_disabled">#C0C4CC</color>
    <color name="font_color_spec">#4399fc</color>
    
    <!-- 边框颜色 -->
    <color name="border_color_dark">#DCDFE6</color>
    <color name="border_color_base">#E4E7ED</color>
    <color name="border_color_light">#EBEEF5</color>

    <color name="colorText">#333333</color>
    
    <!-- 新增错误颜色 -->
    <color name="error">#FF3B30</color>
    
    <!-- 状态标签颜色 -->
    <color name="green">#4CAF50</color>
    <color name="light_green">#E8F5E9</color>
    <color name="red">#F44336</color>
    <color name="light_red">#FFEBEE</color>

    <!-- iOS风格颜色 -->
    <color name="ios_gray">#8E8E93</color>
    <color name="ios_light_gray">#C7C7CC</color>
    <color name="ios_ultralight_gray">#F2F2F7</color>
    <color name="ios_text_primary">#000000</color>
    <color name="ios_text_secondary">#3C3C43</color>
    <color name="ios_blue">#007AFF</color>
    <color name="ios_green">#34C759</color>
    <color name="ios_red">#FF3B30</color>
    <color name="ios_orange">#FF9500</color>
    <color name="ios_stroke">#E5E5EA</color>
    
    <!-- 新增的缺失颜色 -->
    <color name="stroke_dark">#D1D1D6</color>
    
    <!-- 苹果蓝色别名 -->
    <color name="apple_blue">#007AFF</color>
    <color name="apple_blue_light_background">#EBF5FF</color>
    
    <!-- 分割线颜色 -->
    <color name="divider_light">#E5E5EA</color>

    <!-- 主色调表面颜色 -->
    <color name="primary_surface">#EBF5FF</color>
    
    <!-- iOS风格背景颜色 -->
    <color name="background_secondary">#F2F2F7</color>
    <color name="background_tertiary">#F8F8F8</color>
    <color name="background_quaternary">#FAFAFA</color>
    
    <!-- 保活设置指南颜色 -->
    <color name="color_success">#34C759</color>
    <color name="color_warning">#FF9500</color>
</resources>
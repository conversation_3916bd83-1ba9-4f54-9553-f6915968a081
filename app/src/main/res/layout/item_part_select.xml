<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    card_view:cardCornerRadius="12dp"
    card_view:cardElevation="4dp"
    android:layout_marginTop="2dp"
    android:layout_marginBottom="2dp"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:clipToPadding="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:paddingEnd="0dp"
        android:paddingRight="0dp"
        android:background="@android:color/white">

        <!-- 复选框最左侧 -->
        <CheckBox
            android:id="@+id/cb_part_selected"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:padding="0dp"
            android:layout_marginEnd="4dp"/>

        <!-- 零件图片 -->
        <ImageView
            android:id="@+id/iv_part_pic"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_image_placeholder"
            android:background="@drawable/bg_image_round"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp" />

        <!-- 右侧信息区 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1">

            <!-- 主信息 -->
            <TextView
                android:id="@+id/tv_part_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="零件名称"
                android:textSize="16sp"
                android:textColor="#222222"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- 辅助信息 -->
            <TextView
                android:id="@+id/tv_part_oem"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="OEM: -"
                android:textSize="13sp"
                android:textColor="#888888"
                android:layout_marginTop="2dp" />

            <!-- 价格和规格同一行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp">
                <TextView
                    android:id="@+id/tv_part_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="￥-"
                    android:textSize="13sp"
                    android:textColor="#E53935" />
                <TextView
                    android:id="@+id/tv_part_spec"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="规格: -"
                    android:textSize="13sp"
                    android:textColor="#888888"
                    android:layout_marginStart="16dp" />
            </LinearLayout>

            <!-- 位置选择 -->
            <EditText
                android:id="@+id/et_selected_positions"
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:hint="请选择更换位置"
                android:focusable="false"
                android:clickable="true"
                android:inputType="none"
                android:textSize="13sp"
                android:background="@drawable/bg_edittext_round"
                android:textColor="#222222"
                android:layout_marginTop="2dp"
                android:paddingStart="6dp"
                android:paddingEnd="0dp"
                android:paddingRight="0dp"
                android:layout_marginEnd="6dp" />

        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 
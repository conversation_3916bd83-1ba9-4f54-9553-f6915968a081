<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="6dp">

    <ImageView
        android:id="@+id/ivItemImage"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="@drawable/bg_image_placeholder"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_image_placeholder"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tvItemName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivItemImage"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="防漏密封圈 O型环 修理包" />

    <TextView
        android:id="@+id/tvArticleCode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@id/tvItemName"
        app:layout_constraintTop_toBottomOf="@id/tvItemName"
        tools:text="物品编号: WPID240412000009" />

    <TextView
        android:id="@+id/tvItemOem"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@id/tvItemName"
        app:layout_constraintTop_toBottomOf="@id/tvArticleCode"
        tools:text="OEM编号: 5H0129620" />

    <TextView
        android:id="@+id/tvItemQuantity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/tvItemOem"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvItemOem"
        tools:text="申请：2" />

    <TextView
        android:id="@+id/tvItemSpec"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintEnd_toStartOf="@id/tvCancelNum"
        app:layout_constraintStart_toStartOf="@id/tvItemName"
        app:layout_constraintTop_toBottomOf="@id/tvItemOem"
        tools:text="规格: 原装, 颜色: 黄色" />

    <TextView
        android:id="@+id/tvCancelNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="取消: 0"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvItemSpec"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvItemSpec"
        tools:text="取消: 1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvItemPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/price_red"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="@id/tvItemName"
        app:layout_constraintTop_toBottomOf="@id/tvItemSpec"
        tools:text="¥25.00" />

    <TextView
        android:id="@+id/btnCancel"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:background="@drawable/bg_status_tag"
        android:backgroundTint="@color/primary"
        android:gravity="center"
        android:paddingHorizontal="8dp"
        android:text="取消"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvItemPrice"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvItemPrice"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout> 
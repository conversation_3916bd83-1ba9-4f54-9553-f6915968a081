<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/text_map_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        android:text="地图"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 统计信息面板 -->
    <LinearLayout
        android:id="@+id/panel_statistics"
        android:layout_width="@dimen/min_panel_width"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/bg_filter_panel"
        android:orientation="vertical"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:elevation="6dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 统计信息标题行 -->
        <LinearLayout
            android:id="@+id/panel_statistics_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingVertical="2dp"
            android:clickable="true"
            android:focusable="true"
            android:background="@null">

            <!-- 添加客户筛选图标 -->
            <ImageView
                android:id="@+id/statistics_filter_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_filter"
                android:contentDescription="客户筛选图标" />
                
            <TextView
                android:id="@+id/text_statistics_header"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="报修客户：0"
                android:textSize="14sp"
                android:textColor="#2196F3"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/statistics_expand_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_expand_more"
                android:contentDescription="展开/折叠统计信息" />
        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:id="@+id/statistics_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:visibility="gone" />

        <!-- 使用 ScrollView 包裹工单客户列表 -->
        <ScrollView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:maxHeight="400dp"
            android:fadeScrollbars="false"
            android:scrollbars="vertical"
            android:layout_gravity="start"
            android:nestedScrollingEnabled="true">
            
            <!-- 工单客户列表 -->
            <LinearLayout
                android:id="@+id/panel_statistics_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                
                <!-- 工单客户列表将在代码中动态填充 -->
                
            </LinearLayout>
        </ScrollView>
    </LinearLayout>

    <!-- 工程师列表面板 -->
    <LinearLayout
        android:id="@+id/panel_engineers"
        android:layout_width="@dimen/min_panel_width"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="60dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/bg_filter_panel"
        android:orientation="vertical"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:elevation="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 工程师列表标题行 -->
        <LinearLayout
            android:id="@+id/panel_engineers_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingVertical="2dp"
            android:clickable="true"
            android:focusable="true"
            android:background="@null">

            <!-- 添加工程师筛选图标 -->
            <ImageView
                android:id="@+id/engineers_filter_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_filter"
                android:contentDescription="工程师筛选图标" />
                
            <TextView
                android:id="@+id/text_engineers_header"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="工程师：0"
                android:textSize="14sp"
                android:textColor="#2196F3"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/engineers_expand_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_expand_more"
                android:contentDescription="展开/折叠工程师列表" />
        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:id="@+id/engineers_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:visibility="gone" />

        <!-- 使用 ScrollView 包裹工程师列表 -->
        <ScrollView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:maxHeight="300dp"
            android:fadeScrollbars="false"
            android:scrollbars="vertical"
            android:layout_gravity="start"
            android:nestedScrollingEnabled="true">
            
            <!-- 工程师列表 -->
            <LinearLayout
                android:id="@+id/panel_engineers_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                
                <!-- 工程师列表将在代码中动态填充 -->
                
            </LinearLayout>
        </ScrollView>
    </LinearLayout>

    <!-- 合并后的筛选面板 -->

    <com.tencent.tencentmap.mapsdk.maps.MapView
        android:id="@+id/map_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0" />

    <LinearLayout
        android:id="@+id/panel_filter"
        android:layout_width="170dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/bg_filter_panel"
        android:orientation="vertical"
        android:elevation="4dp"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 筛选面板标题行 -->
        <LinearLayout
            android:id="@+id/panel_filter_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingVertical="2dp"
            android:clickable="true"
            android:focusable="true"
            android:background="@null">

            <ImageView
                android:id="@+id/image_filter_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_filter"
                android:contentDescription="筛选图标" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="标签显示筛选"
                android:textSize="14sp"
                android:textColor="#2196F3"
                android:textStyle="bold"
                android:paddingStart="4dp"
                android:paddingEnd="4dp" />

            <ImageView
                android:id="@+id/expand_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_expand_more"
                android:contentDescription="展开/折叠" />
        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:id="@+id/filter_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:visibility="gone" />

        <!-- 筛选面板内容 -->
        <LinearLayout
            android:id="@+id/panel_filter_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <CheckBox
                android:id="@+id/checkbox_engineers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="显示所有工程师"
                android:textSize="12sp"
                android:textColor="#333333"
                android:ellipsize="end"
                android:minHeight="32dp"
                android:singleLine="true"
                android:background="@null"
                android:buttonTint="#2196F3" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="2dp"
                android:layout_marginBottom="2dp"
                android:background="#EEEEEE" />
                
            <CheckBox
                android:id="@+id/checkbox_customers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="显示所有客户"
                android:textSize="12sp"
                android:textColor="#333333"
                android:ellipsize="end"
                android:minHeight="32dp"
                android:singleLine="true"
                android:background="@null"
                android:buttonTint="#2196F3" />
                
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:background="#E0E0E0" />

            <CheckBox
                android:id="@+id/checkbox_idle_engineers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="false"
                android:text="仅显示空闲工程师"
                android:textSize="12sp"
                android:textColor="#333333"
                android:ellipsize="end"
                android:minHeight="32dp"
                android:singleLine="true"
                android:background="@null"
                android:buttonTint="#2196F3" />

            
                
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:background="#E0E0E0" />

            <CheckBox
                android:id="@+id/checkbox_overdue_customers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="false"
                android:text="仅显示超时客户"
                android:textSize="12sp"
                android:textColor="#333333"
                android:ellipsize="end"
                android:minHeight="32dp"
                android:singleLine="true"
                android:background="@null"
                android:buttonTint="#2196F3" />
        </LinearLayout>
    </LinearLayout>

    <!-- 地图控制按钮组（合并缩放、刷新和定位功能） -->
    <LinearLayout
        android:id="@+id/layout_map_controls"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="88dp"
        android:orientation="vertical"
        android:background="@drawable/bg_filter_panel"
        android:elevation="8dp"
        android:paddingVertical="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_bias="0.65">



         <!-- 放大按钮 -->
        <ImageView
            android:id="@+id/btn_zoom_in"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_add"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="放大地图"
            android:scaleType="centerInside"
            android:padding="8dp"
            android:tint="#2196F3" />

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="8dp"
            android:background="#E0E0E0" />

        <!-- 缩小按钮 -->
        <ImageView
            android:id="@+id/btn_zoom_out"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_remove"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="缩小地图"
            android:scaleType="centerInside"
            android:padding="8dp"
            android:tint="#2196F3" />

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="8dp"
            android:background="#E0E0E0" />

        <!-- 适应所有标记按钮 -->
        <ImageView
            android:id="@+id/btn_zoom_fit_all"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_map"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="适应所有标记"
            android:scaleType="centerInside"
            android:padding="8dp"
            android:tint="#2196F3" />

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="8dp"
            android:background="#E0E0E0" />


        <!-- 定位按钮 -->
        <ImageView
            android:id="@+id/btn_location"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/baseline_my_location_24"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="定位到当前位置"
            android:scaleType="centerInside"
            android:padding="8dp"
            android:tint="#2196F3" />

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="8dp"
            android:background="#E0E0E0" />

        <!-- 刷新按钮 -->
        <ImageView
            android:id="@+id/btn_refresh"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_popup_sync"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="刷新地图数据"
            android:scaleType="centerInside"
            android:padding="8dp"
            android:tint="#2196F3" />
    </LinearLayout>

    <ProgressBar
        android:id="@+id/progress_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 
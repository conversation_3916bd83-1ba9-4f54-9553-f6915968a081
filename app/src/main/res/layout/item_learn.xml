<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="2dp"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="1dp"
    app:strokeWidth="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="12dp"
        android:paddingTop="12dp"
        android:paddingEnd="8dp"
        android:paddingBottom="12dp">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium"
            android:maxLines="2"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="喷墨打印机墨盒安装及维护指南" />

        <!-- 知识库类型 -->
        <LinearLayout
            android:id="@+id/layout_type"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="知识库类型"
                android:textColor="@color/text_secondary"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tv_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@drawable/bg_chip_rounded"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                tools:text="报代码" />

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:background="@color/stroke_light"
            app:layout_constraintTop_toBottomOf="@+id/layout_type" />

        <!-- 适用机型 -->
        <LinearLayout
            android:id="@+id/layout_models"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toStartOf="@+id/layout_content_types"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="适用机型"
                android:textColor="@color/text_secondary"
                android:textSize="13sp" />

            <HorizontalScrollView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:scrollbars="none">

                <LinearLayout
                    android:id="@+id/layout_models_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- 动态添加机型TextViews -->

                </LinearLayout>

            </HorizontalScrollView>

        </LinearLayout>

        <!-- 内容类型图标区域 -->
        <LinearLayout
            android:id="@+id/layout_content_types"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toStartOf="@+id/iv_arrow"
            app:layout_constraintTop_toTopOf="@+id/layout_models"
            app:layout_constraintBottom_toBottomOf="@+id/layout_models">

            <ImageView
                android:id="@+id/iv_code_explain"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="6dp"
                android:contentDescription="代码说明"
                android:src="@drawable/ic_code"
                android:tint="@color/text_secondary"
                android:visibility="gone"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_text"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="6dp"
                android:contentDescription="文字说明"
                android:src="@drawable/ic_text"
                android:tint="@color/text_secondary"
                android:visibility="gone"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_video"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:contentDescription="视频说明"
                android:src="@drawable/ic_video"
                android:tint="@color/text_secondary"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>



        <!-- 箭头图标 -->
        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_arrow_right"
            android:tint="@color/ios_light_gray"
            app:layout_constraintBottom_toBottomOf="@+id/layout_models"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/layout_models" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView> 
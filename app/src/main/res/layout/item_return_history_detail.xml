<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="6dp">

    <ImageView
        android:id="@+id/iv_product"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="@drawable/bg_image_placeholder"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:src="@drawable/ic_placeholder" />

    <TextView
        android:id="@+id/tv_product_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_product"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="防漏密封圈 O型环 修理包" />

    <TextView
        android:id="@+id/tv_article_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@id/tv_product_name"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        tools:text="物品编号: WPID240412000009" />

    <TextView
        android:id="@+id/tv_oem_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@id/tv_product_name"
        app:layout_constraintTop_toBottomOf="@id/tv_article_code"
        tools:text="OEM编号: 5H0129620" />

    <TextView
        android:id="@+id/tv_spec"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_product_name"
        app:layout_constraintTop_toBottomOf="@id/tv_oem_number"
        tools:text="规格: 原装, 颜色: 黄色" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/price_red"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="@id/tv_product_name"
        app:layout_constraintTop_toBottomOf="@id/tv_spec"
        tools:text="¥25.00" />

    <TextView
        android:id="@+id/tv_quantity_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/tv_price"
        app:layout_constraintTop_toTopOf="@id/tv_price"
        tools:text="申请: 2 入库: 2" />

</androidx.constraintlayout.widget.ConstraintLayout> 
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingVertical="12dp"
    android:paddingHorizontal="8dp">

    <TextView
        android:id="@+id/tv_brand"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="品牌名称"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"/>

    <TextView
        android:id="@+id/tv_serial"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:text="系列名称"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"/>

    <TextView
        android:id="@+id/tv_skill_level"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="能力等级"
        android:textColor="@color/primary"
        android:textSize="14sp"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"/>

</LinearLayout> 
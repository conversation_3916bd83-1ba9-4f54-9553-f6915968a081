<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/bg_dialog_rounded">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="添加标签"
        android:textSize="20sp"
        android:textColor="@color/text_primary"
        android:fontFamily="sans-serif-medium"
        android:layout_marginBottom="20dp"
        android:gravity="center" />

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="标签名称"
        app:boxCornerRadiusBottomEnd="12dp"
        app:boxCornerRadiusBottomStart="12dp"
        app:boxCornerRadiusTopEnd="12dp"
        app:boxCornerRadiusTopStart="12dp"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary"
        app:boxBackgroundMode="outline">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etTagName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:textSize="16sp"
            android:fontFamily="sans-serif" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:hint="标签颜色 (如 #FF4081)"
        app:boxCornerRadiusBottomEnd="12dp"
        app:boxCornerRadiusBottomStart="12dp"
        app:boxCornerRadiusTopEnd="12dp"
        app:boxCornerRadiusTopStart="12dp"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary"
        app:boxBackgroundMode="outline">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etTagColor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:textSize="16sp"
            android:fontFamily="sans-serif"
            android:text="#FF4081" />

    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="预览颜色: "
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:fontFamily="sans-serif" />

        <com.google.android.material.card.MaterialCardView
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginStart="12dp"
            app:cardCornerRadius="18dp"
            app:cardElevation="0dp">
            
            <View
                android:id="@+id/colorPreview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#FF4081" />
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCancel"
            style="@style/Widget.App.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp"
            android:text="取消"
            android:textSize="15sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnConfirm"
            style="@style/Widget.App.Button.Filled"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:text="确认"
            android:textSize="15sp" />
    </LinearLayout>
</LinearLayout> 
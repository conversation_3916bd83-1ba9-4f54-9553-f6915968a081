<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="1dp"
    app:strokeWidth="0.5dp"
    app:strokeColor="@color/stroke_light">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="17sp"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="姓名：张三" />

        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:background="@drawable/bg_status_tag"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:text="在职"
            android:textColor="@color/green"
            android:textSize="12sp"
            android:fontFamily="sans-serif"
            app:layout_constraintStart_toEndOf="@+id/tvName"
            app:layout_constraintTop_toTopOf="@+id/tvName"
            app:layout_constraintBottom_toBottomOf="@+id/tvName" />

        <TextView
            android:id="@+id/tvRole"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:textSize="15sp"
            android:textColor="@color/text_secondary"
            android:fontFamily="sans-serif"
            app:layout_constraintEnd_toStartOf="@+id/btnDelete"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvName"
            tools:text="角色：店长" />

        <TextView
            android:id="@+id/tvPhone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="15sp"
            android:textColor="@color/text_tertiary"
            android:fontFamily="sans-serif"
            app:layout_constraintEnd_toStartOf="@+id/btnDelete"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvRole"
            tools:text="电话：13800138000" />

        <ImageView
            android:id="@+id/btnDelete"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="删除"
            android:padding="10dp"
            android:src="@drawable/delete_24dp"
            app:tint="@color/danger"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView> 
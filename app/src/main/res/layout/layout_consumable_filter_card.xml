<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/filter_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="筛选条件"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="6dp"/>

        <!-- 物品编号 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="物品编号"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_code"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- 物品名称 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="物品名称"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_name"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- OEM编号 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_oem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="OEM编号"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_oem"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- 制造商物品编号 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_manufacturer_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="制造商物品编号"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_manufacturer_code"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- 制造商物品名称 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_manufacturer_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="制造商物品名称"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_manufacturer_name"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- 筛选按钮区域，水平排列 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp">

            <!-- 重置按钮 -->
            <Button
                android:id="@+id/btn_reset"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="重置"
                android:textSize="13sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"/>

            <!-- 搜索按钮 -->
            <Button
                android:id="@+id/btn_search"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="搜索"
                android:textSize="13sp"/>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView> 
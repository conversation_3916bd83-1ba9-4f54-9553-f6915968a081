<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="10dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <!-- 工程师头像 -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_engineer_avatar"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:scaleType="centerCrop"
            android:src="@android:drawable/ic_menu_myplaces"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/CircleImageView" />

        <!-- 工程师信息区域 -->
        <LinearLayout
            android:id="@+id/layout_engineer_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@id/iv_engineer_avatar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_engineer_avatar"
            app:layout_constraintTop_toTopOf="@id/iv_engineer_avatar">

            <TextView
                android:id="@+id/tv_engineer_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="张工程师"
                android:textColor="#000000"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_engineer_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:text="13800138000"
                android:textColor="#757575"
                android:textSize="13sp" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_call_engineer"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="拨打电话"
            android:src="@android:drawable/ic_menu_call"
            android:tint="#00A40C"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/layout_engineer_info"
            app:layout_constraintBottom_toBottomOf="@id/layout_engineer_info"
            android:layout_marginEnd="22dp" />

        <!-- 分隔线 -->
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1.2dp"
            android:layout_marginTop="4dp"
            android:background="#F0F0F0"
            app:layout_constraintTop_toBottomOf="@id/iv_engineer_avatar" />

        <!-- 工单统计信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@id/divider">

            <!-- 剩余工单 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_received_orders"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3"
                    android:textColor="#2196F3"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="剩余工单"
                    android:textColor="#757575"
                    android:textSize="11sp" />
            </LinearLayout>

            <!-- 今日完成 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_completed_orders"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="8"
                    android:textColor="#4CAF50"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="今日完成"
                    android:textColor="#757575"
                    android:textSize="11sp" />
            </LinearLayout>

            <!-- 指派工单 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_pending_orders"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="5"
                    android:textColor="#FF9800"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="指派工单"
                    android:textColor="#757575"
                    android:textSize="11sp" />
            </LinearLayout>

            <!-- 总工单数 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_today_orders"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="12"
                    android:textColor="#FF9800"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="总工单数"
                    android:textColor="#757575"
                    android:textSize="11sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 
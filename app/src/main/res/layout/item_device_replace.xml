<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="6dp"
    app:cardElevation="1dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- 新格式内容区域 -->
        <LinearLayout
            android:id="@+id/layout_new_format"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="vertical"
            android:visibility="visible">

            <!-- 物品名称和来源 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_article_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="R610/5200中辊轴承（原装）" />

                <!-- 来源 -->
                <TextView
                    android:id="@+id/tv_data_source"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/bg_tag_ios"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="13sp"
                    tools:text="自修" />

            </LinearLayout>

            <!-- 物品编号和数量 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_article_code"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="13sp"
                    tools:text="PID231210000151" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="数量: "
                    android:textColor="@color/text_secondary"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tv_num"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    tools:text="2" />

            </LinearLayout>

            <!-- OEM编号和更换人 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_number_oem"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="13sp"
                    tools:text="OEM: AE045065" />

                <!-- 更换人 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="更换人："
                        android:textColor="@color/text_secondary"
                        android:textSize="13sp" />

                    <TextView
                        android:id="@+id/tv_replacer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_secondary"
                        android:textSize="13sp"
                        tools:text="张三" />

                </LinearLayout>

            </LinearLayout>

            <!-- 印量信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:orientation="horizontal">

                <!-- 黑白印量 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="黑白计数器: "
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:textSize="13sp" />

                    <TextView
                        android:id="@+id/tv_black_white"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        tools:text="0" />

                </LinearLayout>

                <!-- 彩色印量 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="end">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="彩色计数器: "
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:textSize="13sp" />

                    <TextView
                        android:id="@+id/tv_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:textSize="13sp"
                        tools:text="0" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- 旧格式内容区域 -->
        <LinearLayout
            android:id="@+id/layout_legacy_format"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- 换件单号 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="换件单号: "
                    android:textColor="@color/text_secondary"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tv_replace_code"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_primary"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    tools:text="HJ250416000003" />

            </LinearLayout>

            <!-- 故障描述 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="故障: "
                    android:textColor="@color/text_secondary"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tv_exc_desc"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/text_primary"
                    android:textSize="13sp"
                    tools:text="设备故障需要更换零件" />

            </LinearLayout>

            <!-- 解决方案 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="方案: "
                    android:textColor="@color/text_secondary"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tv_resolve_desc_legacy"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/text_primary"
                    android:textSize="13sp"
                    tools:text="更换相关零件" />

            </LinearLayout>

            <!-- 换件列表容器 -->
            <LinearLayout
                android:id="@+id/container_replace_items"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:orientation="vertical" />

        </LinearLayout>

        <!-- 头部信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                
                <!-- 工单编号 -->
                <TextView
                    android:id="@+id/tv_work_order_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/primary"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    tools:text="WXGD250330000004" />

            </LinearLayout>

            <!-- 时间和箭头 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="13sp"
                    tools:text="03-31 14:30" />

                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_marginStart="6dp"
                    android:src="@drawable/ic_arrow_right"
                    android:tint="@color/text_secondary" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 
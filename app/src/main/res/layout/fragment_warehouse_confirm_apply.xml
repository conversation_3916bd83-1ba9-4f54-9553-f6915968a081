<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_gray">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:title="确认申领清单"
        app:titleTextColor="@color/black" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cardHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/tvTotalCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="申请数量：10" />
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cardList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="80dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cardHeader">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="8dp"
            tools:listitem="@layout/item_warehouse_confirm" />
    </androidx.cardview.widget.CardView>

    <Button
        android:id="@+id/btnConfirmApply"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/bg_button_primary"
        android:text="确认申领"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 
<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="100dp"
    android:layout_height="100dp"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <FrameLayout
        android:id="@+id/addImageButton"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:id="@+id/btn_add_image"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_add"
            android:contentDescription="添加图片" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="28dp"
            android:text="添加图片"
            android:textSize="12sp"
            android:textColor="@color/text_secondary" />
    </FrameLayout>
</androidx.cardview.widget.CardView> 
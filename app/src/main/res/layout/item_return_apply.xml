<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- 复选框 - 使用View替代CheckBox减小占用区域 -->
        <View
            android:id="@+id/checkbox"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:background="@drawable/checkbox_normal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- 物品图片 -->
        <ImageView
            android:id="@+id/image_view"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginStart="12dp"
            android:scaleType="centerCrop"
            android:background="@drawable/bg_image_placeholder"
            android:src="@drawable/ic_image_placeholder"
            app:layout_constraintStart_toEndOf="@id/checkbox"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- 物品名称 -->
        <TextView
            android:id="@+id/tv_item_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/image_view"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="电路板 PCB-A123" />

        <!-- 物品编码 -->
        <TextView
            android:id="@+id/tv_article_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@id/tv_item_name"
            app:layout_constraintStart_toStartOf="@id/tv_item_name"
            app:layout_constraintTop_toBottomOf="@id/tv_item_name"
            tools:text="物品编码: WPID240412000009" />

        <!-- OEM编号 -->
        <TextView
            android:id="@+id/tv_oem_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@id/tv_item_name"
            app:layout_constraintStart_toStartOf="@id/tv_item_name"
            app:layout_constraintTop_toBottomOf="@id/tv_article_code"
            tools:text="OEM编号: AJK123456789" />

        <!-- 规格描述 -->
        <TextView
            android:id="@+id/tv_specification"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="@id/tv_item_name"
            app:layout_constraintStart_toStartOf="@id/tv_item_name"
            app:layout_constraintTop_toBottomOf="@id/tv_oem_number"
            tools:text="原装, 黑色" />

        <!-- 价格 -->
        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/price_red"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@id/tv_item_name"
            app:layout_constraintTop_toBottomOf="@id/tv_specification"
            tools:text="￥123.45" />

        <!-- 可退数量信息 -->
        <TextView
            android:id="@+id/tv_available_quantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/tv_price"
            app:layout_constraintTop_toTopOf="@id/tv_price"
            app:layout_constraintBottom_toBottomOf="@id/tv_price"
            tools:text="可退: 5" />

        <!-- 数量控制区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_price"
            app:layout_constraintBottom_toBottomOf="@id/tv_price">

            <!-- 减少按钮 -->
            <ImageButton
                android:id="@+id/btn_decrease"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:background="@drawable/bg_quantity_button"
                android:src="@drawable/ic_remove"
                android:contentDescription="减少数量" />

            <!-- 数量显示 -->
            <TextView
                android:id="@+id/tv_quantity"
                android:layout_width="28dp"
                android:layout_height="22dp"
                android:gravity="center"
                android:background="@drawable/bg_quantity_text"
                android:textSize="14sp"
                android:textColor="@color/text_primary"
                tools:text="1" />

            <!-- 增加按钮 -->
            <ImageButton
                android:id="@+id/btn_increase"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:background="@drawable/bg_quantity_button"
                android:src="@drawable/ic_add"
                android:contentDescription="增加数量" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView> 
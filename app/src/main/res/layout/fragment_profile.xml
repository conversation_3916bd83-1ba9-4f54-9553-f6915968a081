<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:title="首页"
            app:titleTextAppearance="@style/TextAppearance.App.Headline"
            app:titleTextColor="@color/text_primary">
            
            <ImageButton
                android:id="@+id/btn_settings"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="end"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="设置"
                android:padding="12dp"
                android:src="@drawable/ic_settings"
                android:tint="@color/text_primary" />
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="16dp"
            android:paddingBottom="24dp">

            <!-- 个人信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/profile_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="1dp"
                app:strokeWidth="0dp"
                android:clickable="true"
                android:focusable="true"
                app:rippleColor="@color/colorPrimaryLight">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/image_avatar"
                        android:layout_width="58dp"
                        android:layout_height="58dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_profile"
                        app:shapeAppearanceOverlay="@style/CircleImageView"
                        app:strokeColor="@color/stroke_light"
                        app:strokeWidth="1dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/text_user_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="工程师A"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toEndOf="@id/image_avatar"
                        app:layout_constraintTop_toTopOf="@id/image_avatar"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <TextView
                        android:id="@+id/text_user_id"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="4dp"
                        android:text="工号：10086"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        app:layout_constraintStart_toEndOf="@id/image_avatar"
                        app:layout_constraintTop_toBottomOf="@id/text_user_name"
                        app:layout_constraintEnd_toEndOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- 功能模块标题 -->
            <TextView
                android:id="@+id/text_functions_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="功能菜单"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 主要功能卡片组 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="1dp"
                app:strokeWidth="0dp">

                <!-- 添加加载指示器 -->
                <ProgressBar
                    android:id="@+id/functions_loading_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="32dp"
                    android:visibility="visible"
                    android:indeterminateTint="@color/primary"/>

                <GridLayout
                    android:id="@+id/main_functions_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:columnCount="3"
                    android:padding="8dp"
                    android:visibility="invisible">

<!--                    &lt;!&ndash; 功能按钮将在代码中动态添加，这里展示样例 &ndash;&gt;-->
<!--                    <include layout="@layout/item_function_grid" />-->
<!--                    <include layout="@layout/item_function_grid" />-->
<!--                    <include layout="@layout/item_function_grid" />-->
<!--                    <include layout="@layout/item_function_grid" />-->
<!--                    <include layout="@layout/item_function_grid" />-->
<!--                    <include layout="@layout/item_function_grid" />-->
                </GridLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout> 
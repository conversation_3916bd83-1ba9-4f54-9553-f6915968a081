<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/filter_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="筛选条件"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="6dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_product"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="适用机型"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <AutoCompleteTextView
                android:id="@+id/auto_complete_product"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="text"
                android:imeOptions="actionDone" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_category"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="零件分类"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <AutoCompleteTextView
                android:id="@+id/auto_complete_category"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="none"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_part_category"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="物品分类"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <AutoCompleteTextView
                android:id="@+id/auto_complete_part_category"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="none"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_unit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="所属单元"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <AutoCompleteTextView
                android:id="@+id/auto_complete_unit"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="none"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_oem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:hint="OEM编号"
            app:boxStrokeWidth="1dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/input_oem"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textSize="13sp"
                android:inputType="text"/>
        </com.google.android.material.textfield.TextInputLayout>

        <!-- 筛选按钮区域，水平排列 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp">

            <!-- 重置按钮 -->
            <Button
                android:id="@+id/btn_reset"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="重置"
                android:textSize="13sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"/>

            <!-- 搜索按钮 -->
            <Button
                android:id="@+id/btn_search"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="搜索"
                android:textSize="13sp"/>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView> 
<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@android:id/text1"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:textAppearance="?android:attr/textAppearanceListItemSmall"
    android:textColor="#333333"
    android:background="#FFFFFF"
    android:paddingTop="12dp"
    android:paddingBottom="12dp"
    android:paddingLeft="16dp"
    android:paddingStart="16dp"
    android:paddingRight="16dp"
    android:paddingEnd="16dp"
    android:gravity="center_vertical"
    android:singleLine="true"
    android:textSize="14sp"
    android:ellipsize="marquee" /> 
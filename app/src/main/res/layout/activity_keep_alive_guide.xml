<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/text_device_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="检测设备中..."
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/text_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="为确保应用能够在后台稳定运行，请按照以下步骤进行设置："
                android:textSize="14sp"
                android:textColor="?android:attr/textColorSecondary" />

            <TextView
                android:id="@+id/text_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_warning_rounded"
                android:padding="12dp"
                android:text=""
                android:textSize="14sp"
                android:textColor="@color/color_warning"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="注意：\n• 不同厂商ROM的设置界面可能有差异\n• 设置完成后可返回应用检查状态\n• 如遇问题可联系技术支持"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout> 
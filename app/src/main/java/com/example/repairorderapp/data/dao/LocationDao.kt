package com.example.repairorderapp.data.dao

import androidx.room.*
import com.example.repairorderapp.data.model.LocationPoint

/**
 * 位置数据点的数据访问对象 (DAO)
 */
@Dao
interface LocationDao {

    /**
     * 插入一个位置数据点
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLocation(location: LocationPoint)

    /**
     * 插入多个位置数据点
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(locations: List<LocationPoint>)

    /**
     * 获取所有未上传的位置数据点，按时间戳升序排序
     * @param limit 获取数量的上限
     * @return 未上传的位置数据点列表
     */
    @Query("SELECT * FROM location_points WHERE isUploaded = 0 ORDER BY timestamp ASC LIMIT :limit")
    suspend fun getUnuploadedLocations(limit: Int): List<LocationPoint>

    /**
     * 将一批位置数据点标记为已上传
     * @param ids 要标记的记录的ID列表
     */
    @Query("UPDATE location_points SET isUploaded = 1 WHERE id IN (:ids)")
    suspend fun markAsUploaded(ids: List<Long>)

    /**
     * 删除早于指定时间戳的已上传记录
     * @param timestamp 清理该时间戳之前的数据
     */
    @Query("DELETE FROM location_points WHERE isUploaded = 1 AND timestamp < :timestamp")
    suspend fun deleteUploadedBefore(timestamp: Long)

    /**
     * 获取数据库中总的记录数
     */
    @Query("SELECT COUNT(*) FROM location_points")
    suspend fun count(): Int
    
    /**
     * 删除所有位置数据点（用于测试或清理）
     */
    @Query("DELETE FROM location_points")
    suspend fun clearAll()
    
    /**
     * 保持数据库中只有最新的指定数量的记录
     * 删除除了最新N条记录之外的所有记录
     */
    @Query("DELETE FROM location_points WHERE id NOT IN (SELECT id FROM location_points ORDER BY timestamp DESC LIMIT :keepCount)")
    suspend fun keepLatestRecords(keepCount: Int)
}

package com.example.repairorderapp.data.api.interceptor

import android.content.Intent
import android.util.Log
import com.example.repairorderapp.RepairOrderApp
import com.example.repairorderapp.network.ApiClient
import com.example.repairorderapp.data.api.LoginService
import com.example.repairorderapp.data.api.RefreshTokenResponse
import com.example.repairorderapp.ui.login.LoginActivity
import com.example.repairorderapp.utils.PreferenceUtils
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.locks.ReentrantLock

class TokenInterceptor : Interceptor {
    private val TAG = "TokenInterceptor"
    
    // 添加锁，防止多个请求同时刷新token
    private val refreshTokenLock = ReentrantLock()
    
    // 标记token是否正在刷新
    private var isRefreshing = false
    
    // 添加token过期检查的时间阈值，默认20分钟
    private val TOKEN_EXPIRE_THRESHOLD = 20 * 60 * 1000
    
    // 上次刷新token的时间
    private var lastRefreshTime = 0L
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        try {
            val original = chain.request()
            
            // 添加通用请求头，如登录令牌等
            val requestBuilder = original.newBuilder()
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                
            // 从token_pref获取令牌，使用accessToken键名与UNI-APP保持一致
            val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
            val token = tokenPrefs.getString("accessToken", "")
            val userId = tokenPrefs.getString("userId", "")
            val refreshToken = tokenPrefs.getString("refreshToken", "")
            
            // 使用X-Auth-Token而不是Authorization，与UNI-APP保持一致
            if (token?.isNotEmpty() == true) {
                requestBuilder.header("X-Auth-Token", token)
                Log.d(TAG, "添加令牌头 X-Auth-Token: $token")
                
                // 检查token是否需要主动刷新（距离上次刷新超过阈值时间）
                val currentTime = System.currentTimeMillis()
                if (!refreshToken.isNullOrEmpty() && 
                    (currentTime - lastRefreshTime > TOKEN_EXPIRE_THRESHOLD) && 
                    !original.url.toString().contains("/refresh-token") &&
                    !original.url.toString().contains("/login")) {
                    
                    // 进行主动刷新token
                    Log.d(TAG, "主动刷新令牌（保活机制）")
                    refreshTokenProactively(refreshToken)
                }
            }
            
            // 如果有用户ID，添加X-CUSTOMER-ID头
            if (userId?.isNotEmpty() == true) {
                requestBuilder.header("X-CUSTOMER-ID", userId)
                Log.d(TAG, "添加用户ID头 X-CUSTOMER-ID: $userId")
            }
            
            val request = requestBuilder.build()
            
            // 处理响应
            var response = chain.proceed(request)
            
            // 检查响应状态码，处理401未授权错误（令牌过期）
            if (response.code == 401) {
                Log.e(TAG, "会话已过期(401)，尝试刷新令牌")
                
                // 尝试刷新token
                response.close()
                
                // 获取refreshToken
                val refreshToken = tokenPrefs.getString("refreshToken", "")
                
                if (refreshToken.isNullOrEmpty()) {
                    // 没有refreshToken，直接跳转登录页面
                    Log.e(TAG, "没有刷新令牌，需要重新登录")
                    navigateToLogin()
                    throw IOException("会话已过期，请重新登录")
                }
                
                // 有refreshToken，尝试刷新token
                val newTokens = refreshToken(refreshToken, request)
                if (newTokens != null) {
                    // 刷新成功，使用新token重试请求
                    val newRequest = request.newBuilder()
                        .header("X-Auth-Token", newTokens.first)
                        .build()
                    
                    return chain.proceed(newRequest)
                } else {
                    // 刷新失败，跳转登录页面
                    navigateToLogin()
                    throw IOException("刷新令牌失败，请重新登录")
                }
            }
            
            return response
            
        } catch (e: Exception) {
            Log.e(TAG, "Network error: ${e.message}")
            
            // 处理不同类型的网络错误
            val errorMessage = when (e) {
                is SocketTimeoutException -> "请求超时，请检查网络连接"
                is UnknownHostException -> "无法连接到服务器，请检查网络连接"
                is ConnectException -> "连接服务器失败，请检查网络设置"
                else -> "网络错误: ${e.message}"
            }
            
            throw IOException(errorMessage, e)
        }
    }
    
    /**
     * 主动刷新token，保持会话活跃
     */
    private fun refreshTokenProactively(refreshToken: String) {
        // 使用后台线程刷新token，不影响当前请求
        Thread {
            try {
                // 使用锁防止多个请求同时刷新token
                if (refreshTokenLock.tryLock()) {
                    try {
                        Log.d(TAG, "开始主动刷新token（保活）")
                        
                        // 创建LoginService实例
                        val loginService = ApiClient.createService(LoginService::class.java)
                        
                        // 使用挂起函数刷新token
                        val response = runBlocking {
                            loginService.refreshToken(refreshToken)
                        }
                        
                        if (response.isSuccessful && response.body()?.code == 200) {
                            val tokenResponse = response.body()?.data
                            
                            if (tokenResponse != null) {
                                // 保存新token
                                val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
                                tokenPrefs.edit().apply {
                                    putString("accessToken", tokenResponse.token)
                                    putString("refreshToken", tokenResponse.refreshToken)
                                    apply()
                                }
                                
                                // 更新最后刷新时间
                                lastRefreshTime = System.currentTimeMillis()
                                
                                Log.d(TAG, "主动刷新token成功（保活）")
                            }
                        } else {
                            Log.d(TAG, "主动刷新token失败: ${response.code()}")
                        }
                    } finally {
                        refreshTokenLock.unlock()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "主动刷新token异常: ${e.message}")
            }
        }.start()
    }
    
    /**
     * 刷新token
     * @return Pair<String, String>? 第一个元素是新的accessToken，第二个元素是新的refreshToken，null表示刷新失败
     */
    private fun refreshToken(refreshToken: String, request: Request): Pair<String, String>? {
        try {
            // 使用锁确保同一时间只有一个请求在刷新token
            refreshTokenLock.lock()
            
            // 避免多个请求都触发刷新
            if (isRefreshing) {
                // 等待其他请求完成刷新
                Thread.sleep(500)
                
                // 再次检查token是否已刷新
                val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
                val newToken = tokenPrefs.getString("accessToken", "")
                val newRefreshToken = tokenPrefs.getString("refreshToken", "")
                
                if (!newToken.isNullOrEmpty() && !newRefreshToken.isNullOrEmpty()) {
                    return Pair(newToken, newRefreshToken)
                }
                
                return null
            }
            
            isRefreshing = true
            
            // 创建LoginService实例
            val loginService = ApiClient.createService(LoginService::class.java)
            
            // 使用挂起函数刷新token
            val response = runBlocking {
                loginService.refreshToken(refreshToken)
            }
            
            if (response.isSuccessful && response.body()?.code == 200) {
                val tokenResponse = response.body()?.data
                
                if (tokenResponse != null) {
                    // 保存新token
                    val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
                    tokenPrefs.edit().apply {
                        putString("accessToken", tokenResponse.token)
                        putString("refreshToken", tokenResponse.refreshToken)
                        apply()
                    }
                    
                    // 更新最后刷新时间
                    lastRefreshTime = System.currentTimeMillis()
                    
                    Log.d(TAG, "成功刷新令牌")
                    return Pair(tokenResponse.token, tokenResponse.refreshToken)
                }
            }
            
            Log.e(TAG, "刷新令牌失败: ${response.code()}")
            return null
        } catch (e: Exception) {
            Log.e(TAG, "刷新令牌异常: ${e.message}")
            return null
        } finally {
            isRefreshing = false
            refreshTokenLock.unlock()
        }
    }
    
    /**
     * 导航到登录页面
     */
    private fun navigateToLogin() {
        // 清除令牌
        val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
        tokenPrefs.edit().apply {
            remove("accessToken")
            remove("refreshToken")
            apply()
        }
        
        // 使用新线程启动登录页面，避免在非UI线程修改UI的问题
        android.os.Handler(android.os.Looper.getMainLooper()).post {
            val intent = Intent(RepairOrderApp.instance, LoginActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            RepairOrderApp.instance.startActivity(intent)
        }
    }
} 
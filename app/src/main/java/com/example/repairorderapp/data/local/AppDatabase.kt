package com.example.repairorderapp.data.local

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.example.repairorderapp.data.model.LocationPoint
import com.example.repairorderapp.data.model.LogConfig
import com.example.repairorderapp.data.model.LogEntry
import com.example.repairorderapp.data.model.DeviceInfo
import com.example.repairorderapp.data.model.CrashInfo
import com.example.repairorderapp.data.dao.LocationDao
import com.example.repairorderapp.data.dao.LogConfigDao
import com.example.repairorderapp.data.dao.LogEntryDao
import com.example.repairorderapp.data.dao.DeviceInfoDao
import com.example.repairorderapp.data.dao.CrashInfoDao

/**
 * 应用的Room数据库主类。
 * 采用单例模式确保全局只有一个数据库实例。
 */
@Database(
    entities = [
        LocationPoint::class,
        LogConfig::class,
        LogEntry::class,
        DeviceInfo::class,
        CrashInfo::class
    ],
    version = 8, // 更新版本号以支持DeviceInfo添加权限和配置信息字段
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {

    abstract fun locationDao(): LocationDao
    abstract fun logConfigDao(): LogConfigDao
    abstract fun logEntryDao(): LogEntryDao
    abstract fun deviceInfoDao(): DeviceInfoDao
    abstract fun crashInfoDao(): CrashInfoDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "repair_order_database"
                )
                .fallbackToDestructiveMigration() // 在版本升级时销毁并重新创建数据库
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
} 
package com.example.repairorderapp.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 位置数据点实体类，用于Room数据库存储
 *
 * @property id 自增主键
 * @property latitude 纬度
 * @property longitude 经度
 * @property accuracy 精度（米）
 * @property timestamp 时间戳（毫秒）
 * @property speed 速度（米/秒）
 * @property bearing 方向（度）
 * @property provider 提供者（如 "gps", "network"）
 * @property isUploaded 数据是否已上传
 */
@Entity(tableName = "location_points")
data class LocationPoint(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val latitude: Double,
    val longitude: Double,
    val accuracy: Float,
    val timestamp: Long,
    val speed: Float,
    val bearing: Float,
    val provider: String,
    var isUploaded: Boolean = false
) 
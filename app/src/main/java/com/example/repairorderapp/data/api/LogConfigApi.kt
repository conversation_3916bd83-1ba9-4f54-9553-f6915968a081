package com.example.repairorderapp.data.api

import com.example.repairorderapp.data.model.*
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.*

/**
 * 日志配置相关API接口 - 精简版
 * 保留核心的日志上传功能，移除统计分析和测试诊断功能
 */
interface LogConfigApi {
    
    /**
     * 获取日志配置（支持用户/设备定向）
     * 注意：根据后端修复，请求头参数已改为可选
     * 🔧 修复：返回类型应该是包装在ApiResponse中的数据
     */
    @GET("/api/logcontrol/config/get")
    suspend fun getLogConfig(
        @Header("X-User-Id") userId: String? = null,
        @Header("X-Device-Id") deviceId: String? = null,
        @Header("X-App-Version") appVersion: String? = null
    ): Response<ApiResponse<LogConfigResponse>>

    /**
     * 上传日志数据
     */
    @POST("/api/logcontrol/log/upload")
    suspend fun uploadLogs(
        @Header("Content-Type") contentType: String = "application/json",
        @Body request: LogUploadRequest
    ): Response<LogUploadResponse>

    // ========== 设备信息管理接口 ==========

    /**
     * 上传设备信息
     */
    @POST("/api/logcontrol/device/upload")
    suspend fun uploadDeviceInfo(
        @Body deviceInfo: com.example.repairorderapp.data.model.DeviceInfoUploadDto
    ): Response<ApiResponse<Void>>

    // ========== 崩溃信息管理接口 ==========

    /**
     * 上传崩溃信息
     */
    @POST("/api/logcontrol/crash/upload")
    suspend fun uploadCrashInfo(
        @Body request: com.example.repairorderapp.data.model.CrashInfoUploadRequest
    ): Response<ApiResponse<Void>>
}

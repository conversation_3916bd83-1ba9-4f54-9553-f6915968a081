package com.example.repairorderapp.data.dao

import androidx.room.*
import com.example.repairorderapp.data.model.CrashInfo

/**
 * 崩溃信息数据访问对象
 */
@Dao
interface CrashInfoDao {
    
    /**
     * 插入崩溃信息
     */
    @Insert
    suspend fun insertCrash(crashInfo: CrashInfo): Long
    
    /**
     * 批量插入崩溃信息
     */
    @Insert
    suspend fun insertCrashes(crashes: List<CrashInfo>): List<Long>
    
    /**
     * 根据ID获取崩溃信息
     */
    @Query("SELECT * FROM crash_info WHERE id = :crashId")
    suspend fun getCrashById(crashId: Long): CrashInfo?
    
    /**
     * 根据设备ID获取崩溃列表
     */
    @Query("SELECT * FROM crash_info WHERE deviceId = :deviceId ORDER BY crashTime DESC LIMIT :limit")
    suspend fun getCrashesByDevice(deviceId: String, limit: Int = 50): List<CrashInfo>
    
    /**
     * 获取未上传的崩溃信息
     */
    @Query("SELECT * FROM crash_info WHERE isUploaded = 0 ORDER BY crashTime DESC LIMIT :limit")
    suspend fun getUnuploadedCrashes(limit: Int): List<CrashInfo>
    
    /**
     * 根据异常类型获取崩溃列表
     */
    @Query("SELECT * FROM crash_info WHERE exceptionType = :exceptionType ORDER BY crashTime DESC LIMIT :limit")
    suspend fun getCrashesByExceptionType(exceptionType: String, limit: Int): List<CrashInfo>
    
    /**
     * 获取指定时间范围内的崩溃
     */
    @Query("SELECT * FROM crash_info WHERE crashTime BETWEEN :startTime AND :endTime ORDER BY crashTime DESC")
    suspend fun getCrashesByTimeRange(startTime: Long, endTime: Long): List<CrashInfo>
    
    /**
     * 获取最近的崩溃列表
     */
    @Query("SELECT * FROM crash_info ORDER BY crashTime DESC LIMIT :limit")
    suspend fun getRecentCrashes(limit: Int): List<CrashInfo>
    
    /**
     * 获取内存相关崩溃
     */
    @Query("SELECT * FROM crash_info WHERE exceptionType LIKE '%OutOfMemoryError%' ORDER BY crashTime DESC LIMIT :limit")
    suspend fun getMemoryRelatedCrashes(limit: Int): List<CrashInfo>
    
    /**
     * 获取网络相关崩溃
     */
    @Query("SELECT * FROM crash_info WHERE exceptionType LIKE '%NetworkException%' OR exceptionType LIKE '%SocketTimeoutException%' OR exceptionType LIKE '%ConnectException%' ORDER BY crashTime DESC LIMIT :limit")
    suspend fun getNetworkRelatedCrashes(limit: Int): List<CrashInfo>
    
    /**
     * 标记崩溃为已上传
     */
    @Query("UPDATE crash_info SET isUploaded = 1 WHERE id IN (:crashIds)")
    suspend fun markAsUploaded(crashIds: List<Long>)
    
    /**
     * 获取崩溃统计信息
     */
    @Query("SELECT exceptionType, COUNT(*) as count FROM crash_info GROUP BY exceptionType ORDER BY count DESC")
    suspend fun getCrashStatistics(): List<CrashStatistic>
    
    /**
     * 获取设备崩溃统计
     */
    @Query("""
        SELECT ci.deviceId, di.brand, di.model, di.osVersion, COUNT(*) as crashCount 
        FROM crash_info ci 
        LEFT JOIN device_info di ON ci.deviceId = di.deviceId 
        GROUP BY ci.deviceId 
        ORDER BY crashCount DESC 
        LIMIT :limit
    """)
    suspend fun getDeviceCrashStatistics(limit: Int): List<DeviceCrashStatistic>
    
    /**
     * 获取时间段内崩溃统计
     */
    @Query("""
        SELECT DATE(crashTime/1000, 'unixepoch') as date, COUNT(*) as count 
        FROM crash_info 
        WHERE crashTime BETWEEN :startTime AND :endTime 
        GROUP BY DATE(crashTime/1000, 'unixepoch') 
        ORDER BY date DESC
    """)
    suspend fun getCrashTrendStatistics(startTime: Long, endTime: Long): List<CrashTrendStatistic>
    
    /**
     * 获取崩溃总数
     */
    @Query("SELECT COUNT(*) FROM crash_info")
    suspend fun getCrashCount(): Int
    
    /**
     * 获取未上传崩溃总数
     */
    @Query("SELECT COUNT(*) FROM crash_info WHERE isUploaded = 0")
    suspend fun getUnuploadedCrashCount(): Int
    
    /**
     * 获取指定设备的崩溃总数
     */
    @Query("SELECT COUNT(*) FROM crash_info WHERE deviceId = :deviceId")
    suspend fun getCrashCountByDevice(deviceId: String): Int
    
    /**
     * 删除指定时间之前的崩溃记录
     */
    @Query("DELETE FROM crash_info WHERE crashTime < :cutoffTime")
    suspend fun deleteCrashesBeforeTime(cutoffTime: Long)
    
    /**
     * 删除已上传的崩溃记录
     */
    @Query("DELETE FROM crash_info WHERE isUploaded = 1")
    suspend fun deleteUploadedCrashes()
    
    /**
     * 清理旧崩溃记录，保留最新的指定数量
     */
    @Query("DELETE FROM crash_info WHERE id NOT IN (SELECT id FROM crash_info ORDER BY crashTime DESC LIMIT :keepCount)")
    suspend fun cleanupOldCrashes(keepCount: Int)
    
    /**
     * 删除所有崩溃记录
     */
    @Query("DELETE FROM crash_info")
    suspend fun deleteAllCrashes()
}

/**
 * 崩溃统计数据类
 */
data class CrashStatistic(
    val exceptionType: String,
    val count: Int
)

/**
 * 设备崩溃统计数据类
 */
data class DeviceCrashStatistic(
    val deviceId: String,
    val brand: String?,
    val model: String?,
    val osVersion: String?,
    val crashCount: Int
)

/**
 * 崩溃趋势统计数据类
 */
data class CrashTrendStatistic(
    val date: String,
    val count: Int
)

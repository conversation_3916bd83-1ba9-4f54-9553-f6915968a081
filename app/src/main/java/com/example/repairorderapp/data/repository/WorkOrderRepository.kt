package com.example.repairorderapp.data.repository

import android.util.Log
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.model.ReportResponse
import com.google.gson.Gson
import com.google.gson.JsonObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.IOException
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

class WorkOrderRepository(private val apiService: WorkOrderApi) {
    
    private val TAG = "WorkOrderRepository"
    private val gson = Gson()
    
    /**
     * 获取维修报告详情（协程方式）
     */
    suspend fun getRepairReportSuspend(workOrderId: String): Result<ReportResponse> {
        return suspendCoroutine { continuation ->
            apiService.getRepairReport(workOrderId).enqueue(object : Callback<ApiResponse<Any>> {
                override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                    if (response.isSuccessful) {
                        val apiResponse = response.body()
                        if (apiResponse?.code == 200 && apiResponse.data != null) {
                            try {
                                // 解析数据为ReportResponse对象
                                val jsonString = gson.toJson(apiResponse.data)
                                val reportResponse = gson.fromJson(jsonString, ReportResponse::class.java)
                                continuation.resume(Result.success(reportResponse))
                            } catch (e: Exception) {
                                Log.e(TAG, "解析维修报告数据失败", e)
                                continuation.resume(Result.failure(e))
                            }
                        } else {
                            val error = Exception("API返回错误: ${apiResponse?.msg ?: "未知错误"}")
                            Log.e(TAG, error.message.toString())
                            continuation.resume(Result.failure(error))
                        }
                    } else {
                        try {
                            val errorBody = response.errorBody()?.string() ?: ""
                            val error = IOException("HTTP ${response.code()}: ${response.message()}, 错误详情: $errorBody")
                            Log.e(TAG, "获取维修报告失败", error)
                            continuation.resume(Result.failure(error))
                        } catch (e: Exception) {
                            continuation.resume(Result.failure(e))
                        }
                    }
                }

                override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                    Log.e(TAG, "网络请求失败", t)
                    continuation.resume(Result.failure(t))
                }
            })
        }
    }
    
    /**
     * 获取维修报告详情（回调方式）
     */
    fun getRepairReportWithCallback(workOrderId: String, callback: Callback<ApiResponse<Any>>) {
        apiService.getRepairReport(workOrderId).enqueue(callback)
    }
    
    /**
     * 直接获取维修报告数据（不使用回调）
     */
    fun getRepairReport(workOrderId: String): Call<ApiResponse<Any>> {
        return apiService.getRepairReport(workOrderId)
    }
} 
package com.example.repairorderapp.data.api

import com.example.repairorderapp.model.qrcode.QrCodeScanResult
import com.example.repairorderapp.model.qrcode.QrCodeBindResult
import com.example.repairorderapp.model.customer.CustomerListItem
import com.example.repairorderapp.model.common.ApiResponse
import retrofit2.Response
import retrofit2.http.*

/**
 * 二维码扫描相关API接口
 */
interface QrCodeApi {
    
    /**
     * 扫描二维码结果验证
     * @param uid 二维码中的uid
     * @return 扫描结果
     */
    @GET("/api/qr-app/scanQr")
    suspend fun scanQrCode(
        @Query("uid") uid: String
    ): Response<ApiResponse<QrCodeScanResult>>
    
    /**
     * 选择客户店铺绑定设备
     * @param customerId 客户ID
     * @param uid 二维码中的uid
     * @return 绑定结果
     */
    @GET("/api/qr-app/selectStore")
    suspend fun selectStore(
        @Query("customerId") customerId: String,
        @Query("uid") uid: String
    ): Response<ApiResponse<QrCodeBindResult>>
    
    /**
     * 获取可绑定的客户列表
     * @return 客户列表
     */
    @GET("/api/wechat/customer")
    suspend fun getBindableCustomers(): Response<ApiResponse<List<CustomerListItem>>>
} 
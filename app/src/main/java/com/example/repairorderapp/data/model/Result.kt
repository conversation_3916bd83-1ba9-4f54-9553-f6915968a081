package com.example.repairorderapp.data.model

/**
 * 通用结果封装类，用于处理网络请求或其他异步操作的结果
 */
sealed class Result<out T> {
    /**
     * 操作成功，包含结果数据
     */
    data class Success<T>(val data: T) : Result<T>()
    
    /**
     * 操作失败，包含异常信息
     */
    data class Error(val exception: Exception) : Result<Nothing>()
    
    /**
     * 操作进行中
     */
    object Loading : Result<Nothing>()
    
    /**
     * 将结果转换为另一种类型
     * @param transform 数据转换函数
     * @return 转换后的结果
     */
    fun <R> map(transform: (T) -> R): Result<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Error -> this
            is Loading -> this
        }
    }
    
    /**
     * 检查结果是否成功
     * @return 如果结果是Success则返回true，否则返回false
     */
    fun isSuccess(): Boolean {
        return this is Success
    }
    
    /**
     * 检查结果是否错误
     * @return 如果结果是Error则返回true，否则返回false
     */
    fun isError(): Boolean {
        return this is Error
    }
    
    /**
     * 检查结果是否加载中
     * @return 如果结果是Loading则返回true，否则返回false
     */
    fun isLoading(): Boolean {
        return this is Loading
    }
    
    /**
     * 获取成功结果的数据，如果不是成功结果则返回null
     * @return 成功结果的数据或null
     */
    fun getDataOrNull(): T? {
        return if (this is Success) data else null
    }
    
    /**
     * 获取错误结果的异常，如果不是错误结果则返回null
     * @return 错误结果的异常或null
     */
    fun getExceptionOrNull(): Exception? {
        return if (this is Error) exception else null
    }
} 
package com.example.repairorderapp.data.repository

import com.example.repairorderapp.network.ApiClient
import com.example.repairorderapp.model.*
import com.example.repairorderapp.util.SharedPrefsManager
import kotlinx.coroutines.delay

/**
 * 知识库数据仓库
 */
class LearnRepository(
    private val sharedPreferencesManager: SharedPrefsManager
) {
    
    companion object {
        private const val KEY_LEARN_KEYWORDS = "learn_keywords"
        private const val KEY_LEARN_FILTER_PARAMS = "learn_filter_params"
        private const val SEARCH_DEBOUNCE_DELAY = 300L
        
        // 缓存标志和数据
        private var isKnowledgeTypesCached = false
        private var isDeviceTreeCached = false
        private var cachedKnowledgeTypes: List<KnowledgeType> = emptyList()
        private var cachedDeviceTree: List<DeviceTreeNode> = emptyList()
    }
    
    private val learnService = ApiClient.learnService
    
    /**
     * 获取知识库分页数据
     */
    suspend fun getLearnPage(params: LearnSearchParams): Result<PageData<LearnItem>> {
        return try {
            val response = learnService.getLearnPage(params)
            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!
                if (apiResponse.isSuccess()) {
                    Result.success(apiResponse.data ?: PageData(emptyList(), 0))
                } else {
                    Result.failure(Exception(apiResponse.getErrorMessage()))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取热词搜索建议
     */
    suspend fun getHotWords(keyword: String): Result<List<LearnHotWord>> {
        return try {
            // 添加防抖延迟
            delay(SEARCH_DEBOUNCE_DELAY)
            val response = learnService.getHotWords(keyword)
            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!
                if (apiResponse.isSuccess()) {
                    Result.success(apiResponse.data ?: emptyList())
                } else {
                    Result.failure(Exception(apiResponse.getErrorMessage()))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取知识库详情
     */
    suspend fun getLearnDetail(id: String): Result<LearnDetailResponse> {
        return try {
            val response = learnService.getLearnDetail(id)
            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!
                if (apiResponse.isSuccess()) {
                    Result.success(apiResponse.data!!)
                } else {
                    Result.failure(Exception(apiResponse.getErrorMessage()))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取当前用户适配机型
     */
    suspend fun getCurrentModel(): Result<List<String>> {
        return try {
            val response = learnService.getCurrentModel()
            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!
                if (apiResponse.isSuccess()) {
                    Result.success(apiResponse.data ?: emptyList())
                } else {
                    Result.failure(Exception(apiResponse.getErrorMessage()))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 记录页面访问统计
     */
    suspend fun recordPageView(path: String, params: String? = null) {
        try {
            val previousId = sharedPreferencesManager.getString("previousId", null)
            val pageViewParams = mapOf(
                "params" to (params ?: ""),
                "path" to path,
                "previousId" to (previousId ?: "")
            )
            val response = learnService.recordPageView(pageViewParams)
            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!
                if (apiResponse.isSuccess()) {
                    apiResponse.data?.let { newPreviousId ->
                        sharedPreferencesManager.putString("previousId", newPreviousId)
                    }
                }
            }
        } catch (e: Exception) {
            // 静默处理统计错误，不影响主要功能
        }
    }
    
    /**
     * 获取保存的搜索关键词
     */
    fun getSavedSearchKeywords(): String? {
        return sharedPreferencesManager.getString(KEY_LEARN_KEYWORDS, null)
    }
    
    /**
     * 保存搜索关键词
     */
    fun saveSearchKeywords(keywords: String) {
        sharedPreferencesManager.putString(KEY_LEARN_KEYWORDS, keywords)
    }
    
    /**
     * 清除搜索关键词
     */
    private fun clearSearchKeywords() {
        sharedPreferencesManager.remove(KEY_LEARN_KEYWORDS)
    }
    
    /**
     * 保存筛选参数
     */
    fun saveFilterParams(productList: List<String>, typeList: List<String>) {
        val paramsString = "${productList.joinToString(",")}|${typeList.joinToString(",")}"
        sharedPreferencesManager.putString(KEY_LEARN_FILTER_PARAMS, paramsString)
    }
    
    /**
     * 获取保存的筛选参数
     */
    fun getSavedFilterParams(): Pair<List<String>, List<String>> {
        val paramsString = sharedPreferencesManager.getString(KEY_LEARN_FILTER_PARAMS, "")
        return if (paramsString.isNullOrEmpty()) {
            Pair(emptyList(), emptyList())
        } else {
            try {
                val parts = paramsString.split("|")
                val productList = if (parts[0].isNotEmpty()) parts[0].split(",") else emptyList()
                val typeList = if (parts.size > 1 && parts[1].isNotEmpty()) parts[1].split(",") else emptyList()
                Pair(productList, typeList)
            } catch (e: Exception) {
                Pair(emptyList(), emptyList())
            }
        }
    }
    
    /**
     * 清除筛选参数
     */
    private fun clearFilterParams() {
        sharedPreferencesManager.remove(KEY_LEARN_FILTER_PARAMS)
    }
    
    /**
     * 获取案例详情
     */
    suspend fun getCaseDetail(id: String): Result<RepairCase> {
        return try {
            val response = learnService.getCaseDetail(id)
            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!
                if (apiResponse.isSuccess()) {
                    Result.success(apiResponse.data!!)
                } else {
                    Result.failure(Exception(apiResponse.getErrorMessage()))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取当前机型（筛选用）- 旧接口，将被替换
     */
    suspend fun getCurrentModelForFilter(): Result<List<BrandModel>> {
        return try {
            val response = learnService.getCurrentModelForFilter()
            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!
                if (apiResponse.isSuccess()) {
                    Result.success(apiResponse.data ?: emptyList())
                } else {
                    Result.failure(Exception(apiResponse.getErrorMessage()))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取知识库类型（筛选用）
     */
    suspend fun getKnowledgeTypes(forceRefresh: Boolean = false): Result<List<KnowledgeType>> {
        // 如果有缓存且不强制刷新，直接返回缓存数据
        if (isKnowledgeTypesCached && !forceRefresh && cachedKnowledgeTypes.isNotEmpty()) {
            return Result.success(cachedKnowledgeTypes)
        }
        
        return try {
            val response = learnService.getKnowledgeTypes()
            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!
                if (apiResponse.isSuccess()) {
                    val types = apiResponse.data ?: emptyList()
                    // 更新缓存
                    cachedKnowledgeTypes = types
                    isKnowledgeTypesCached = true
                    Result.success(types)
                } else {
                    Result.failure(Exception(apiResponse.getErrorMessage()))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取设备树形结构（多级联动筛选用）- 新接口
     */
    suspend fun getDeviceTree(forceRefresh: Boolean = false): Result<List<DeviceTreeNode>> {
        // 如果有缓存且不强制刷新，直接返回缓存数据
        if (isDeviceTreeCached && !forceRefresh && cachedDeviceTree.isNotEmpty()) {
            return Result.success(cachedDeviceTree)
        }
        
        return try {
            val response = learnService.getDeviceTree()
            if (response.isSuccessful && response.body() != null) {
                val deviceTreeResponse = response.body()!!
                if (deviceTreeResponse.code == 200) {
                    val deviceTree = deviceTreeResponse.data
                    // 更新缓存
                    cachedDeviceTree = deviceTree
                    isDeviceTreeCached = true
                    Result.success(deviceTree)
                } else {
                    Result.failure(Exception(deviceTreeResponse.message))
                }
            } else {
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 清除所有缓存数据
     */
    private fun clearCache() {
        isKnowledgeTypesCached = false
        isDeviceTreeCached = false
        cachedKnowledgeTypes = emptyList()
        cachedDeviceTree = emptyList()
    }
    
    /**
     * 清理所有知识库相关的状态，包括缓存和 SharedPreferences
     */
    fun clearAllKnowledgeBaseState() {
        // 清除内存缓存
        clearCache()
        // 清除 SharedPreferences 中的筛选参数
        clearFilterParams()
        // 清除 SharedPreferences 中的搜索历史
        clearSearchKeywords()
    }
} 
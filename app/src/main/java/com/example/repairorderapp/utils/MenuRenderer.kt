package com.example.repairorderapp.utils

import android.util.LruCache
import android.util.Log
import com.example.repairorderapp.model.MenuItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 菜单渲染器
 * 负责将权限数据转换为菜单项并缓存
 */
class MenuRenderer private constructor() {
    
    companion object {
        private const val TAG = "MenuRenderer"
        private const val CACHE_SIZE = 50
        
        // 单例模式
        @Volatile
        private var instance: MenuRenderer? = null
        
        fun getInstance(): MenuRenderer {
            return instance ?: synchronized(this) {
                instance ?: MenuRenderer().also { instance = it }
            }
        }
    }
    
    // 菜单缓存
    private val menuCache = LruCache<String, List<MenuItem>>(CACHE_SIZE)
    
    // 异步渲染器
    private val renderScope = CoroutineScope(Dispatchers.Default)
    
    /**
     * 异步渲染菜单
     */
    fun renderMenuAsync(
        permissions: List<PermissionItem>,
        onComplete: (List<MenuItem>) -> Unit
    ) {
        renderScope.launch {
            try {
                val menuItems = renderMenu(permissions)
                withContext(Dispatchers.Main) {
                    onComplete(menuItems)
                }
            } catch (e: Exception) {
                Log.e(TAG, "菜单渲染失败: ${e.message}", e)
                withContext(Dispatchers.Main) {
                    // 在出错时返回空列表，确保UI不会崩溃
                    onComplete(emptyList())
                }
            }
        }
    }
    
    /**
     * 渲染菜单
     */
    fun renderMenu(permissions: List<PermissionItem>): List<MenuItem> {
        try {
            val cachedMenu = menuCache.get("menu")
            if (cachedMenu != null) {
                return cachedMenu
            }
            
            val menuItems = permissions.mapNotNull { 
                try {
                    it.toMenuItem()
                } catch (e: Exception) {
                    Log.e(TAG, "转换权限项到菜单项失败: ${e.message}", e)
                    null // 跳过有问题的项
                }
            }
            menuCache.put("menu", menuItems)
            return menuItems
        } catch (e: Exception) {
            Log.e(TAG, "渲染整个菜单失败: ${e.message}", e)
            return emptyList() // 返回空列表而不是抛出异常
        }
    }
    
    /**
     * 清除菜单缓存
     */
    fun clearCache() {
        menuCache.evictAll()
        Log.d(TAG, "菜单缓存已清除")
    }
    
    /**
     * 将权限项转换为菜单项
     */
    private fun PermissionItem.toMenuItem(): MenuItem {
        val id = try {
            id.toIntOrNull() ?: 0
        } catch (e: Exception) {
            Log.w(TAG, "权限ID解析失败: $id, 使用默认值0", e)
            0
        }
        
        return MenuItem(
            id = id,
            title = name,
            icon = getIconResourceForPermission(code),
            screenId = getScreenIdForPermission(code),
            permission = code
        )
    }
    
    /**
     * 根据权限代码获取对应的图标资源
     */
    private fun getIconResourceForPermission(permission: String): Int {
        // TODO: 实现权限代码到图标资源的映射
        return 0
    }
    
    /**
     * 根据权限代码获取对应的屏幕ID
     */
    private fun getScreenIdForPermission(permission: String): Int {
        // TODO: 实现权限代码到屏幕ID的映射
        return 0
    }
} 
package com.example.repairorderapp.network.service

import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Url

interface LoginService {
    
    /**
     * 获取验证码 - 使用POST方法，与UNI-APP保持一致
     */
    @POST("/api/magina/anno/captcha")
    fun getCaptcha(): Call<ResponseBody>
    
    /**
     * 获取加密密钥 - 使用POST方法，与UNI-APP保持一致
     */
    @POST("/api/magina/anno/key")
    fun getEncryptionKey(): Call<ResponseBody>
    
    /**
     * 用户登录
     */
    @POST("/api/wechat/staff/app/login")
    fun login(@Body params: Map<String, String>): Call<ResponseBody>
    
    /**
     * 获取用户菜单资源
     */
    @GET("/api/magina/system/resources")
    fun getResources(): Call<ResponseBody>
    
    /**
     * 用户登出
     */
    @POST("/api/wechat/staff/logout")
    fun logout(): Call<ResponseBody>
    
    /**
     * 刷新Token
     */
    @POST("/tiny-shop/v1/site/refresh")
    fun refreshToken(@Body params: Map<String, String>): Call<ResponseBody>
    
    /**
     * 验证令牌有效性
     * 使用工单统计接口验证令牌，一箭双雕既能验证令牌有效性，又能获取工单统计数据
     * @param token 待验证的令牌
     * @return 验证结果
     */
    @GET("/api/engineer/work-order/sumaryCount")
    fun verifyToken(@Header("X-Auth-Token") token: String): Call<ResponseBody>
} 
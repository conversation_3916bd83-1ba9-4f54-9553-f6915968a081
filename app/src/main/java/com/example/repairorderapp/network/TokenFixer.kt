package com.example.repairorderapp.network

import android.content.Context
import android.util.Log
import com.example.repairorderapp.util.SharedPrefsManager
import com.example.repairorderapp.utils.PreferenceUtils
import com.example.repairorderapp.network.TokenManager

/**
 * 令牌修复工具
 * 用于检测和修复令牌问题，确保令牌在不同存储位置之间同步
 */
class TokenFixer private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "TokenFixer"
        
        @Volatile
        private var instance: TokenFixer? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): TokenFixer {
            return instance ?: synchronized(this) {
                instance ?: TokenFixer(context.applicationContext).also { instance = it }
            }
        }
    }
    
    /**
     * 检查并修复令牌问题
     * @return 修复后是否有有效令牌
     */
    fun fixTokenIfNeeded(): Boolean {
        try {
            Log.d(TAG, "开始检查令牌状态...")
            
            // 从token_pref获取令牌（唯一存储点）
            val tokenPrefs = context.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            var token = tokenPrefs.getString("accessToken", "")
            
            // 检查令牌是否为空
            if (token.isNullOrEmpty()) {
                Log.e(TAG, "令牌为空，需要重新登录")
                return false
            } else {
                Log.d(TAG, "令牌有效")
                return true
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查令牌状态时出错", e)
            return false
        }
    }
    
    /**
     * 将令牌同步到所有存储位置
     */
    private fun syncTokenToAllStorages(token: String) {
        try {
            if (token.isEmpty()) {
                Log.e(TAG, "无法同步空令牌")
                return
            }
            
            Log.d(TAG, "同步令牌到所有存储位置: ${token.take(10)}...")
            
            // 1. 保存到token_pref（唯一存储点）
            val tokenPrefs = context.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            tokenPrefs.edit().putString("accessToken", token).apply()
            
            // 验证同步结果
            val savedToken = tokenPrefs.getString("accessToken", "")
            
            if (savedToken != token) {
                Log.e(TAG, "令牌同步失败")
            } else {
                Log.d(TAG, "令牌成功同步到token_pref")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "同步令牌到存储位置时出错", e)
        }
    }
    
    /**
     * 清除所有存储位置的令牌
     */
    fun clearAllTokens() {
        try {
            Log.d(TAG, "清除token_pref中的令牌")
            
            // 清除token_pref（唯一存储点）
            val tokenPrefs = context.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            tokenPrefs.edit().remove("accessToken").remove("refreshToken").apply()
            
        } catch (e: Exception) {
            Log.e(TAG, "清除令牌时出错", e)
        }
    }

    /**
     * 验证令牌是否有效
     * 代理到TokenManager.verifyToken方法
     */
    fun verifyToken(token: String, onResult: (Boolean) -> Unit) {
        try {
            Log.d(TAG, "验证令牌有效性: ${token.take(10)}...")
            TokenManager.getInstance(context).verifyToken(token, onResult)
        } catch (e: Exception) {
            Log.e(TAG, "验证令牌时出错", e)
            onResult(false)
        }
    }
}
package com.example.repairorderapp.viewmodel.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.model.PagedData
import com.example.repairorderapp.model.customer.CustomerVisit
import com.example.repairorderapp.util.Event
import kotlinx.coroutines.launch

/**
 * 客户拜访记录管理ViewModel
 */
class CustomerVisitViewModel(
    private val customerRepository: CustomerRepository
) : ViewModel() {
    
    private val _visitList = MutableLiveData<List<CustomerVisit>>()
    val visitList: LiveData<List<CustomerVisit>> = _visitList
    
    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean> = _loading
    
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    private val _operationSuccess = MutableLiveData<Event<String>>()
    val operationSuccess: LiveData<Event<String>> = _operationSuccess
    
    private val _total = MutableLiveData<Int>()
    val total: LiveData<Int> = _total
    
    private var currentPage = 1
    private val pageSize = 10
    
    /**
     * 获取客户拜访记录列表
     */
    fun getCustomerVisitRecords(customerId: String, refresh: Boolean = false) {
        if (refresh) {
            currentPage = 1
            _visitList.value = emptyList()
        }
        
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.getCustomerVisitRecords(customerId, currentPage, pageSize)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { pagedData ->
                            val currentList = if (currentPage == 1) {
                                pagedData.rows ?: emptyList()
                            } else {
                                (_visitList.value ?: emptyList()) + (pagedData.rows ?: emptyList())
                            }
                            _visitList.value = currentList
                            _total.value = pagedData.total
                            currentPage++
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "获取拜访记录失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "获取拜访记录失败"
            }
        }
    }
    
    /**
     * 添加拜访记录
     */
    fun addCustomerVisitRecord(visit: CustomerVisit) {
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.addCustomerVisitRecord(visit)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { newVisit ->
                            val currentList = _visitList.value?.toMutableList() ?: mutableListOf()
                            currentList.add(0, newVisit)
                            _visitList.value = currentList
                            _operationSuccess.value = Event("添加拜访记录成功")
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "添加拜访记录失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "添加拜访记录失败"
            }
        }
    }
    
    /**
     * 加载更多拜访记录
     */
    fun loadMoreVisits(customerId: String) {
        if (_loading.value == true) return
        getCustomerVisitRecords(customerId, false)
    }
} 
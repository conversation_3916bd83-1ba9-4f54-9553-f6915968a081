package com.example.repairorderapp.viewmodel.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.model.customer.CustomerStaff
import com.example.repairorderapp.model.customer.CustomerOption
import com.example.repairorderapp.util.Event
import kotlinx.coroutines.launch

/**
 * 客户员工管理ViewModel
 */
class CustomerStaffViewModel(
    private val customerRepository: CustomerRepository
) : ViewModel() {
    
    private val _staffList = MutableLiveData<List<CustomerStaff>>()
    val staffList: LiveData<List<CustomerStaff>> = _staffList
    
    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean> = _loading
    
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    private val _operationSuccess = MutableLiveData<Event<String>>()
    val operationSuccess: LiveData<Event<String>> = _operationSuccess
    
    private val _roleOptions = MutableLiveData<List<CustomerOption>>()
    val roleOptions: LiveData<List<CustomerOption>> = _roleOptions
    
    /**
     * 获取客户员工列表
     * @param customerId 客户ID
     * @param pageNumber 页码
     * @param pageSize 每页大小
     */
    fun getCustomerStaffList(customerId: String, pageNumber: Int = 1, pageSize: Int = 10) {
        android.util.Log.d("CustomerStaffViewModel", "开始获取员工列表，客户ID: $customerId")
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.getCustomerStaffList(customerId, pageNumber, pageSize)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { staffList ->
                            android.util.Log.d("CustomerStaffViewModel", "获取员工列表成功，员工数量: ${staffList.size}")
                            _staffList.value = staffList
                        },
                        onFailure = { e ->
                            android.util.Log.e("CustomerStaffViewModel", "获取员工列表失败: ${e.message}")
                            _error.value = e.message ?: "获取员工列表失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                android.util.Log.e("CustomerStaffViewModel", "获取员工列表异常", e)
                _error.value = e.message ?: "获取员工列表失败"
            }
        }
    }
    
    /**
     * 添加客户员工
     */
    fun addCustomerStaff(staff: CustomerStaff) {
        android.util.Log.d("CustomerStaffViewModel", "开始添加员工: ${staff.name}")
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.addCustomerStaff(staff)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { _ ->
                            android.util.Log.d("CustomerStaffViewModel", "添加员工成功，触发成功事件")
                            _operationSuccess.value = Event("添加员工成功")
                        },
                        onFailure = { e ->
                            android.util.Log.e("CustomerStaffViewModel", "添加员工失败: ${e.message}")
                            _error.value = e.message ?: "添加员工失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                android.util.Log.e("CustomerStaffViewModel", "添加员工异常", e)
                _error.value = e.message ?: "添加员工失败"
            }
        }
    }
    
    /**
     * 更新客户员工信息
     */
    fun updateCustomerStaff(staff: CustomerStaff) {
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.updateCustomerStaff(staff)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { _ ->
                            _operationSuccess.value = Event("更新员工信息成功")
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "更新员工信息失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "更新员工信息失败"
            }
        }
    }
    
    /**
     * 删除客户员工
     */
    fun deleteCustomerStaff(staffId: String) {
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.deleteCustomerStaff(staffId)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { _ ->
                            _operationSuccess.value = Event("删除员工成功")
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "删除员工失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "删除员工失败"
            }
        }
    }
    
    /**
     * 获取角色列表
     * @param code 角色代码
     */
    fun getDictTreeByCode(code: String) {
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.getDictTreeByCode(code)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { options ->
                            _roleOptions.value = options
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "获取角色列表失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "获取角色列表失败"
            }
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = ""
    }
} 
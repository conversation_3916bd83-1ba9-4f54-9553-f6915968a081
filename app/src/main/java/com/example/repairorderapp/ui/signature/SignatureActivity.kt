package com.example.repairorderapp.ui.signature

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.repairorderapp.R
import com.github.gcacace.signaturepad.views.SignaturePad
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.*

class SignatureActivity : AppCompatActivity() {

    companion object {
        const val RESULT_OK = Activity.RESULT_OK
        const val EXTRA_SIGNATURE_PATH = "signature_path"
    }

    private lateinit var signaturePad: SignaturePad
    private lateinit var btnClear: Button
    private lateinit var btnSave: Button
    private lateinit var btnCancel: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_signature)

        initViews()
        setupListeners()
    }

    private fun initViews() {
        signaturePad = findViewById(R.id.signature_pad)
        btnClear = findViewById(R.id.btn_clear)
        btnSave = findViewById(R.id.btn_confirm)
        btnCancel = findViewById(R.id.btn_cancel)
    }

    private fun setupListeners() {
        signaturePad.setOnSignedListener(object : SignaturePad.OnSignedListener {
            override fun onStartSigning() {
                // 开始签名时的处理
            }

            override fun onSigned() {
                // 签名后的处理
                btnSave.isEnabled = true
                btnClear.isEnabled = true
            }

            override fun onClear() {
                // 清除签名后的处理
                btnSave.isEnabled = false
                btnClear.isEnabled = false
            }
        })

        btnClear.setOnClickListener {
            signaturePad.clear()
        }

        btnSave.setOnClickListener {
            try {
                // 保存签名为图片
                val signatureBitmap = signaturePad.signatureBitmap
                val file = saveBitmapToFile(signatureBitmap)
                
                // 返回签名图片路径给调用方
                val resultIntent = Intent()
                resultIntent.putExtra(EXTRA_SIGNATURE_PATH, file.absolutePath)
                setResult(RESULT_OK, resultIntent)
                finish()
            } catch (e: IOException) {
                Toast.makeText(this, "保存签名失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }

        btnCancel.setOnClickListener {
            setResult(Activity.RESULT_CANCELED)
            finish()
        }
    }

    private fun saveBitmapToFile(bitmap: android.graphics.Bitmap): File {
        val file = File(this.cacheDir, "signature_${System.currentTimeMillis()}.png")
        FileOutputStream(file).use { out ->
            bitmap.compress(android.graphics.Bitmap.CompressFormat.PNG, 100, out)
        }
        return file
    }
} 
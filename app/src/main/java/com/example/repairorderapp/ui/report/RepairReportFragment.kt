package com.example.repairorderapp.ui.report

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.databinding.FragmentRepairReportBinding
import com.example.repairorderapp.model.RepairOrder
import com.example.repairorderapp.model.ReplacedPart
import com.example.repairorderapp.ui.signature.SignatureActivity
import com.example.repairorderapp.ui.report.ReplacedPartsAdapter
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.*

class RepairReportFragment : Fragment() {

    private var _binding: FragmentRepairReportBinding? = null
    private val binding get() = _binding!!
    
    private var orderId: String = ""
    private val workOrderApi = ApiClient.createService(WorkOrderApi::class.java)
    
    private lateinit var repairOrder: RepairOrder
    private val replacedParts = ArrayList<ReplacedPart>()
    private lateinit var partsAdapter: ReplacedPartsAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            orderId = it.getString("orderId") ?: ""
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRepairReportBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        if (orderId.isEmpty()) {
            Toast.makeText(requireContext(), "工单ID不能为空", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
            return
        }
        
        setupUI()
        loadOrderDetails()
    }
    
    private fun setupUI() {
        // 设置返回按钮
        binding.btnBack.setOnClickListener {
            findNavController().navigateUp()
        }
        
        // 设置保存草稿按钮
        binding.btnSaveDraft.setOnClickListener {
            saveDraftReport()
        }
        
        // 设置零件列表
        binding.recyclerParts.layoutManager = LinearLayoutManager(requireContext())
        partsAdapter = ReplacedPartsAdapter(replacedParts) { position -> 
            replacedParts.removeAt(position)
            partsAdapter.notifyItemRemoved(position)
        }
        binding.recyclerParts.adapter = partsAdapter
        
        // 设置添加零件按钮
        binding.btnAddPart.setOnClickListener {
            showAddPartDialog()
        }
        
        // 设置签名区域点击事件
        binding.imgSignature.setOnClickListener {
            captureSignature()
        }
        
        // 设置预览按钮
        binding.btnPreview.setOnClickListener {
            previewReport()
        }
        
        // 设置提交按钮
        binding.btnSubmit.setOnClickListener {
            submitReport()
        }
    }
    
    private fun loadOrderDetails() {
        // 显示加载中视图
        binding.loadingView.visibility = View.VISIBLE
        
        // 使用Retrofit回调方式获取工单详情
        workOrderApi.getWorkOrderDetail(orderId).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                // 隐藏加载中视图
                binding.loadingView.visibility = View.GONE
                
                if (response.isSuccessful) {
                    val apiResponse = response.body()
                    if (apiResponse?.code == 200 && apiResponse.data != null) {
                        try {
                            // 转换API返回的工单数据为RepairOrder对象
                            val workOrderItem = parseWorkOrderItem(apiResponse.data)
                            repairOrder = mapToRepairOrder(workOrderItem)
                            updateOrderInfo()
                        } catch (e: Exception) {
                            Toast.makeText(requireContext(), "数据解析错误: ${e.message}", Toast.LENGTH_SHORT).show()
                        }
                    } else if (apiResponse?.code == 401) {
                        // 处理401会话过期错误
                        handleSessionExpired()
                    } else {
                        Toast.makeText(requireContext(), "获取工单详情失败: ${apiResponse?.msg ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                    }
                } else if (response.code() == 401) {
                    // 处理401会话过期错误
                    handleSessionExpired()
                } else {
                    Toast.makeText(requireContext(), "获取工单详情失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                binding.loadingView.visibility = View.GONE
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    private fun parseWorkOrderItem(data: Any): WorkOrderItem {
        // 不能直接转换data为WorkOrderItem
        // 解析data（可能是Map或JsonObject）成WorkOrderItem
        if (data is Map<*, *>) {
            try {
                Log.d("RepairReport", "数据解析中，Map格式")
                // 从Map中提取数据
                val id = data["id"]?.toString() ?: ""
                val code = data["code"]?.toString() ?: ""
                val customerId = data["customerId"]?.toString()
                val customerName = data["customerName"]?.toString()
                val deviceNumber = data["deviceNumber"]?.toString()
                val brand = data["brand"]?.toString()
                val machine = data["machine"]?.toString()
                val excDesc = data["excDesc"]?.toString()
                val createdAt = data["createdAt"]?.toString() ?: ""
                val beginTime = data["beginTime"]?.toString()
                val endTime = data["endTime"]?.toString()
                val productId = data["productId"]?.toString()
                
                // 解析嵌套对象
                val customer = parseCustomer(data["customer"])
                val engineerId = parseEngineer(data["engineerId"])
                val latitude = data["latitude"]?.toString()?.toDoubleOrNull() ?: 0.0
                val longitude = data["longitude"]?.toString()?.toDoubleOrNull() ?: 0.0
                val deviceGroup = parseDictItem(data["deviceGroup"])
                val serType = parseDictItem(data["serType"])
                val status = parseDictItem(data["status"])
                
                Log.d("RepairReport", "数据解析成功: $id, $customerName")
                return WorkOrderItem(
                    id = id,
                    code = code,
                    customerId = customerId,
                    customerName = customerName,
                    customer = customer,
                    engineerId = engineerId,
                    deviceNumber = deviceNumber,
                    brand = brand,
                    machine = machine,
                    deviceGroup = deviceGroup,
                    latitude = latitude,
                    longitude = longitude,
                    serType = serType,
                    status = status,
                    excDesc = excDesc,
                    createdAt = createdAt,
                    beginTime = beginTime,
                    endTime = endTime,
                    productId = productId,
                    arriveTime = data["arriveTime"]?.let { it as Date },
                    waitConfirmTime = data["waitConfirmTime"]?.let { it as Date },
                    finishTime = data["finishTime"]?.let { it as Date },
                    orderReceiveTime = data["orderReceiveTime"]?.toString(),
                    departureTime = data["departureTime"]?.toString(),
                    actualArriveTime = data["actualArriveTime"]?.toString(),
                    sendReportTime = data["sendReportTime"]?.toString(),
                    confirmReportTime = data["confirmReportTime"]?.toString(),
                    completedAt = data["completedAt"]?.toString()
                )
            } catch (e: Exception) {
                Log.e("RepairReport", "解析异常: ${e.message}", e)
                throw Exception("数据格式解析错误: ${e.message}")
            }
        } else {
            Log.e("RepairReport", "未知数据类型: ${data.javaClass.name}")
            throw Exception("未支持的数据格式类型")
        }
    }
    
    private fun parseCustomer(data: Any?): Customer? {
        if (data == null) return null
        if (data is Map<*, *>) {
            val id = data["id"]?.toString() ?: ""
            val name = data["name"]?.toString() ?: ""
            val address = data["address"]?.toString() ?: ""
            return Customer(id, name, address)
        }
        return null
    }
    
    private fun parseEngineer(data: Any?): EngineerInfo? {
        if (data == null) return null
        if (data is Map<*, *>) {
            val id = data["id"]?.toString() ?: ""
            val name = data["name"]?.toString() ?: ""
            return EngineerInfo(id, name)
        }
        return null
    }
    
    private fun parseDictItem(data: Any?): DictItem? {
        if (data == null) return null
        if (data is Map<*, *>) {
            val value = data["value"]?.toString() ?: ""
            val label = data["label"]?.toString() ?: ""
            return DictItem(value, label)
        }
        return null
    }
    
    private fun mapToRepairOrder(item: WorkOrderItem): RepairOrder {
        return RepairOrder(
            id = item.id,
            code = item.code,
            customerId = item.customerId ?: "",
            customerName = item.customerName ?: "",
            address = item.customer?.address ?: "",
            latitude = item.latitude ?: 0.0,
            longitude = item.longitude ?: 0.0,
            engineerId = item.engineerId?.id,
            engineerName = item.engineerId?.name,
            status = item.status?.label ?: mapStatusLabel(item.status?.value),
            createTime = parseDateTime(item.createdAt),
            startTime = if (item.beginTime != null) parseDateTime(item.beginTime) else null,
            completedAt = if (item.endTime != null) parseDateTime(item.endTime) else null,
            arriveTime = item.arriveTime,
            waitConfirmTime = item.waitConfirmTime,
            finishTime = item.finishTime,
            // 工单状态时间字段
            receiveTime = if (item.orderReceiveTime != null) parseDateTime(item.orderReceiveTime) else null,
            departureTime = if (item.departureTime != null) parseDateTime(item.departureTime) else null,
            actualArriveTime = if (item.actualArriveTime != null) parseDateTime(item.actualArriveTime) else null,
            sendReportTime = if (item.sendReportTime != null) parseDateTime(item.sendReportTime) else null,
            confirmReportTime = if (item.confirmReportTime != null) parseDateTime(item.confirmReportTime) else null,
            machineNumber = item.deviceNumber ?: "",
            machineModel = when {
                !item.brand.isNullOrEmpty() && !item.machine.isNullOrEmpty() -> "${item.brand}/${item.machine}"
                !item.brand.isNullOrEmpty() -> item.brand
                !item.machine.isNullOrEmpty() -> item.machine
                else -> ""
            },
            deviceGroup = item.deviceGroup?.label ?: "",
            serviceType = item.serType?.label ?: "",
            colorType = "黑白机", // 默认值，实际应从API获取
            problemDesc = item.excDesc ?: "",
            bwCounter = 0,  // 默认值
            colorCounter = 0, // 默认值
            fifthColorCounter = 0 // 默认值
        )
    }
    
    private fun mapStatusLabel(statusValue: String?): String {
        return when (statusValue) {
            "pending_orders" -> "待接单"
            "engineer_receive" -> "已分配"
            "engineer_departure" -> "路途中"
            "engineer_arrive" -> "维修中"
            "wait_confirmed_report" -> "等待确认"
            "completed" -> "已完成"
            "close" -> "已关闭"
            else -> "待处理"
        }
    }
    
    private fun parseDateTime(dateTimeString: String?): Date {
        return try {
            // 简单处理，实际应用中应该使用适当的日期解析
            Date()
        } catch (e: Exception) {
            Date()
        }
    }
    
    private fun updateOrderInfo() {
        // 更新工单信息显示
        binding.tvReportOrderId.text = "工单号：${repairOrder.id}"
        binding.tvReportCustomerName.text = "客户名称：${repairOrder.customerName}"
        binding.tvReportMachineModel.text = "设备型号：${repairOrder.machineModel}"
    }
    
    private fun showAddPartDialog() {
        // 显示添加零件对话框
        val dialog = AddPartDialog(requireContext()) { part ->
            replacedParts.add(part)
            partsAdapter.notifyItemInserted(replacedParts.size - 1)
        }
        dialog.show()
    }
    
    private fun captureSignature() {
        // 启动签名捕获界面
        val intent = Intent(requireContext(), SignatureActivity::class.java)
        startActivityForResult(intent, REQUEST_SIGNATURE)
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_SIGNATURE && resultCode == SignatureActivity.RESULT_OK) {
            // 获取签名图片路径并显示
            val signaturePath = data?.getStringExtra(SignatureActivity.EXTRA_SIGNATURE_PATH)
            if (signaturePath != null) {
                // 显示签名图片并隐藏提示文字
                binding.imgSignature.setImageBitmap(null) // 从路径加载图片
                binding.tvSignaturePlaceholder.visibility = View.GONE
            }
        }
    }
    
    private fun saveDraftReport() {
        // 保存报告草稿
        val faultReason = binding.etFaultReason.text.toString()
        val solution = binding.etSolution.text.toString()
        
        // 保存草稿逻辑...
        
        Toast.makeText(requireContext(), "报告已保存为草稿", Toast.LENGTH_SHORT).show()
    }
    
    private fun previewReport() {
        // 验证报告数据是否完整
        if (!validateReportData()) {
            return
        }
        
        // 跳转到报告预览页面
        val faultReason = binding.etFaultReason.text.toString()
        val solution = binding.etSolution.text.toString()
        
        val bundle = Bundle().apply {
            putString("orderId", repairOrder.id)
            putString("faultReason", faultReason)
            putString("solution", solution)
        }
        findNavController().navigate(R.id.action_repairReportFragment_to_reportPreviewFragment, bundle)
    }
    
    private fun submitReport() {
        // 验证报告数据是否完整
        if (!validateReportData()) {
            return
        }
        
        // 显示加载中视图
        binding.loadingView.visibility = View.VISIBLE
        
        val faultReason = binding.etFaultReason.text.toString()
        val solution = binding.etSolution.text.toString()
        
        // 准备提交数据
        val params = HashMap<String, String>()
        params["orderId"] = repairOrder.id
        params["faultReason"] = faultReason
        params["solution"] = solution
        
        // 添加更换的零件信息（作为JSON字符串）
        val partsJson = StringBuilder("[")
        replacedParts.forEachIndexed { index, part ->
            if (index > 0) partsJson.append(",")
            partsJson.append("{\"name\":\"${part.name}\",\"code\":\"${part.code}\",\"quantity\":\"${part.quantity}\"}")
        }
        partsJson.append("]")
        params["parts"] = partsJson.toString()
        
        // 添加签名图片路径
        // params["signaturePath"] = signaturePath ?: ""
        
        // 使用Retrofit回调方式提交报告
        workOrderApi.submitReport(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                binding.loadingView.visibility = View.GONE
                
                if (response.isSuccessful) {
                    val apiResponse = response.body()
                    if (apiResponse?.code == 200) {
                        Toast.makeText(requireContext(), "报告提交成功", Toast.LENGTH_SHORT).show()
                        // 返回工单详情页面
                        findNavController().navigateUp()
                    } else {
                        val message = apiResponse?.msg ?: "未知错误"
                        Toast.makeText(requireContext(), "提交失败: $message", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(requireContext(), "提交失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                binding.loadingView.visibility = View.GONE
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    private fun validateReportData(): Boolean {
        // 验证报告填写是否完整
        val faultReason = binding.etFaultReason.text.toString()
        val solution = binding.etSolution.text.toString()
        
        if (faultReason.isBlank()) {
            Toast.makeText(requireContext(), "请填写故障原因", Toast.LENGTH_SHORT).show()
            return false
        }
        
        if (solution.isBlank()) {
            Toast.makeText(requireContext(), "请填写处理方法", Toast.LENGTH_SHORT).show()
            return false
        }
        
        if (binding.tvSignaturePlaceholder.visibility == View.VISIBLE) {
            Toast.makeText(requireContext(), "请获取客户签名", Toast.LENGTH_SHORT).show()
            return false
        }
        
        return true
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    
    companion object {
        private const val REQUEST_SIGNATURE = 100
    }
    
    // 工单项API数据结构
    data class WorkOrderItem(
        val id: String,
        val code: String,
        val customerId: String?,
        val customerName: String?,
        val customer: Customer?,
        val engineerId: EngineerInfo?,
        val deviceNumber: String?,
        val brand: String?,
        val machine: String?,
        val deviceGroup: DictItem?,
        val latitude: Double?,
        val longitude: Double?,
        val serType: DictItem?,
        val status: DictItem?,
        val excDesc: String?,
        val createdAt: String?,
        val beginTime: String?,
        val endTime: String?,
        val productId: String?,
        val arriveTime: Date? = null,
        val waitConfirmTime: Date? = null,
        val finishTime: Date? = null,
        val orderReceiveTime: String? = null,
        val departureTime: String? = null,
        val actualArriveTime: String? = null,
        val sendReportTime: String? = null,
        val confirmReportTime: String? = null,
        val completedAt: String? = null
    )
    
    data class EngineerInfo(
        val id: String?,
        val name: String?
    )
    
    data class DictItem(
        val value: String?,
        val label: String?
    )
    
    data class Customer(
        val id: String,
        val name: String,
        val address: String
    )
    
    /**
     * 处理会话过期
     */
    private fun handleSessionExpired() {
        Log.e("RepairReport", "会话已过期，将由TokenInterceptor处理刷新令牌")
        Toast.makeText(requireContext(), "会话已过期，正在尝试刷新...", Toast.LENGTH_SHORT).show()
        
        // 在组件中不需要处理令牌刷新，TokenInterceptor会自动处理
        // 只需重新加载数据
        Handler(Looper.getMainLooper()).postDelayed({
            // 延迟一秒后刷新数据，给TokenInterceptor时间刷新令牌
            loadOrderDetails()
        }, 1000)
    }
} 
package com.example.repairorderapp.ui.warehouse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.example.repairorderapp.R
import com.example.repairorderapp.databinding.ItemWarehouseConfirmBinding
import com.example.repairorderapp.ui.common.ImageViewerDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 确认申请领料适配器
 */
class WarehouseConfirmAdapter : RecyclerView.Adapter<WarehouseConfirmAdapter.ViewHolder>() {

    private var items = mutableListOf<WarehouseApplyFragment.SelectedItem>()
    private var onItemDeleteListener: ((Int) -> Unit)? = null
    private var onQuantityChangeListener: ((Int, Int) -> Unit)? = null
    
    // 图片加载选项 - 全局共享避免重复创建
    private val glideOptions = RequestOptions()
        .diskCacheStrategy(DiskCacheStrategy.ALL)
        .placeholder(R.drawable.ic_image_placeholder)
        .error(R.drawable.ic_image_placeholder)

    // 设置删除监听器
    fun setOnItemDeleteListener(listener: (Int) -> Unit) {
        onItemDeleteListener = listener
    }

    // 设置数量变更监听器
    fun setOnQuantityChangeListener(listener: (Int, Int) -> Unit) {
        onQuantityChangeListener = listener
    }

    // 提交可变列表 - 使用DiffUtil计算差异
    fun submitList(newItems: List<WarehouseApplyFragment.SelectedItem>) {
        // 使用协程在后台线程计算差异
        CoroutineScope(Dispatchers.IO).launch {
            val diffCallback = ItemDiffCallback(items, newItems)
            val diffResult = DiffUtil.calculateDiff(diffCallback)
            
            // 在主线程更新UI
            withContext(Dispatchers.Main) {
                items.clear()
                items.addAll(newItems)
                diffResult.dispatchUpdatesTo(this@WarehouseConfirmAdapter)
            }
        }
    }

    // 删除指定位置的物品 - 优化通知逻辑
    fun removeItem(position: Int) {
        if (position in 0 until items.size) {
            items.removeAt(position)
            notifyItemRemoved(position)
            
            // 只通知位置变化的项目，不需要刷新内容
            if (position < items.size) {
                notifyItemRangeChanged(position, items.size - position, PAYLOAD_POSITION_CHANGED)
            }
        }
    }

    // 更新指定位置的物品数量
    fun updateItemQuantity(position: Int, newQuantity: Int) {
        if (position in 0 until items.size) {
            items[position].quantity = newQuantity
            // 使用Payload减少重绘
            notifyItemChanged(position, PAYLOAD_QUANTITY_CHANGED)
        }
    }

    // 获取所有选中项
    fun getItems(): List<WarehouseApplyFragment.SelectedItem> = items.toList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemWarehouseConfirmBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.bind(item, position + 1)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int, payloads: List<Any>) {
        if (payloads.isEmpty()) {
            // 完整绑定
            onBindViewHolder(holder, position)
        } else {
            // 部分更新
            payloads.forEach { payload ->
                when (payload) {
                    PAYLOAD_POSITION_CHANGED -> {
                        // 只更新序号
                        holder.updateIndex(position + 1)
                    }
                    PAYLOAD_QUANTITY_CHANGED -> {
                        // 只更新数量
                        holder.updateQuantity(items[position].quantity)
                    }
                }
            }
        }
    }

    override fun getItemCount() = items.size

    inner class ViewHolder(private val binding: ItemWarehouseConfirmBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        // 更新索引编号
        fun updateIndex(index: Int) {
            binding.tvIndex.text = index.toString()
        }
        
        // 更新数量
        fun updateQuantity(quantity: Int) {
            binding.tvQuantity.text = quantity.toString()
        }
        
        fun bind(item: WarehouseApplyFragment.SelectedItem, index: Int) {
            binding.tvIndex.text = index.toString()
            binding.tvItemName.text = item.itemName
            binding.tvQuantity.text = item.quantity.toString()
            
            // 设置物品编码（如果有）
            if (!item.articleCode.isNullOrEmpty()) {
                binding.tvArticleCode.visibility = View.VISIBLE
                binding.tvArticleCode.text = "物品编码: ${item.articleCode}"
            } else {
                binding.tvArticleCode.visibility = View.GONE
            }
            
            // 设置OEM编号（如果有）
            if (item.oemNumber.isNullOrEmpty()) {
                binding.tvOemNumber.text = "OEM: --"
            } else {
                binding.tvOemNumber.text = "OEM: ${item.oemNumber}"
            }
            
            // 设置规格属性（如果有）
            if (!item.specification.isNullOrEmpty()) {
                binding.tvSpecification.visibility = View.VISIBLE
                binding.tvSpecification.text = "${item.specification}"
            } else {
                binding.tvSpecification.visibility = View.GONE
            }
            
            // 设置价格
            if (item.unitPrice > 0) {
                binding.tvPrice.visibility = View.VISIBLE
                binding.tvPrice.text = "¥${String.format("%.2f", item.unitPrice)}"
            } else {
                binding.tvPrice.visibility = View.GONE
            }
                
            // 优化图片加载 - 使用全局共享的选项
            if (!item.picUrl.isNullOrEmpty()) {
                binding.ivItemPic.visibility = View.VISIBLE
                
                // 使用View.post延迟图片加载，确保RecyclerView已完成测量和布局
                binding.ivItemPic.post {
                    if (!binding.root.isAttachedToWindow) return@post
                    
                    Glide.with(binding.root.context)
                        .load(item.picUrl)
                        .placeholder(R.drawable.ic_image_placeholder)
                        .error(R.drawable.ic_image_placeholder)
                        .into(binding.ivItemPic)
                }
                
                // 设置图片点击事件，显示大图
                binding.ivItemPic.setOnClickListener {
                    item.picUrl?.let { url ->
                        // 在点击时再创建对话框，避免提前创建占用资源
                        val imageUrls = listOf(url)
                        val dialog = ImageViewerDialog(
                            binding.root.context,
                            imageUrls
                        )
                        dialog.show()
                    }
                }
            } else {
                binding.ivItemPic.visibility = View.VISIBLE
                binding.ivItemPic.setImageResource(R.drawable.ic_image_placeholder)
            }
            
            // 设置按钮点击监听器 - 使用View.post确保不会阻塞主线程
            binding.btnDelete.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemDeleteListener?.invoke(position)
                }
            }
            
            // 设置减少数量按钮点击事件
            binding.btnMinus.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val currentQuantity = items[position].quantity
                    if (currentQuantity > 1) {
                        val newQuantity = currentQuantity - 1
                        onQuantityChangeListener?.invoke(position, newQuantity)
                    }
                }
            }
            
            // 设置增加数量按钮点击事件
            binding.btnPlus.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val newQuantity = items[position].quantity + 1
                    onQuantityChangeListener?.invoke(position, newQuantity)
                }
            }
        }
    }
    
    // DiffUtil回调类 - 高效计算列表差异
    private class ItemDiffCallback(
        private val oldList: List<WarehouseApplyFragment.SelectedItem>,
        private val newList: List<WarehouseApplyFragment.SelectedItem>
    ) : DiffUtil.Callback() {
        
        override fun getOldListSize() = oldList.size
        override fun getNewListSize() = newList.size
        
        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].skuId == newList[newItemPosition].skuId
        }
        
        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            return oldItem.quantity == newItem.quantity && 
                   oldItem.itemName == newItem.itemName
        }
        
        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            
            return when {
                oldItem.quantity != newItem.quantity -> PAYLOAD_QUANTITY_CHANGED
                else -> null // 默认完全刷新
            }
        }
    }
    
    companion object {
        // 更新类型标记
        private const val PAYLOAD_POSITION_CHANGED = "position_changed"
        private const val PAYLOAD_QUANTITY_CHANGED = "quantity_changed"
    }
} 
package com.example.repairorderapp.ui.warehouse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.repairorderapp.R
import com.example.repairorderapp.model.warehouse.WarehouseItem

class WarehouseAdapter : ListAdapter<WarehouseItem, WarehouseAdapter.ViewHolder>(WarehouseDiffCallback()) {

    private var onItemClickListener: ((WarehouseItem) -> Unit)? = null
    private var onImageClickListener: ((String) -> Unit)? = null

    fun setOnItemClickListener(listener: (WarehouseItem) -> Unit) {
        onItemClickListener = listener
    }

    fun setOnImageClickListener(listener: (String) -> Unit) {
        onImageClickListener = listener
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val imgItem: ImageView = view.findViewById(R.id.image_item)
        val txtName: TextView = view.findViewById(R.id.text_item_name)
        val txtOem: TextView = view.findViewById(R.id.text_oem)
        val txtArticleCode: TextView = view.findViewById(R.id.text_article_code)
        val txtCategory: TextView = view.findViewById(R.id.text_attr)
        val txtPrice: TextView = view.findViewById(R.id.text_price)
        val txtQuantity: TextView = view.findViewById(R.id.text_stock)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_warehouse, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = getItem(position)
        
        // 设置名称 - 优先使用SKU的invSkuName
        val itemName = when {
            item.skuList?.isNotEmpty() == true && !item.skuList[0].invSkuName.isNullOrEmpty() -> 
                item.skuList[0].invSkuName
            else -> item.itemName
        }
        holder.txtName.text = itemName
        
        // 设置OEM编号 - 优先使用SKU的invSkuOem
        val oemNumber = when {
            item.skuList?.isNotEmpty() == true -> item.skuList[0].oemNumber
            else -> item.oemNumber
        }
        holder.txtOem.text = "OEM编号: ${oemNumber ?: ""}"
        
        // 设置物品编码 - 直接使用SKU的articleCode
        val articleCode = when {
            item.skuList?.isNotEmpty() == true -> item.skuList[0].articleCode
            else -> item.articleCode
        }
        holder.txtArticleCode.text = "物品编码: ${articleCode ?: ""}"
        
        // 设置规格属性
        if (item.skuInfo != null && item.skuInfo.saleAttrVals != null && item.skuInfo.saleAttrVals.isNotEmpty()) {
            val attrText = item.skuInfo.saleAttrVals.joinToString(", ") { 
                "${it.name}: ${it.`val`}" 
            }
            holder.txtCategory.text = "$attrText"
        } else {
            holder.txtCategory.text = "${item.categoryName ?: "未知分类"}"
        }
        
        // 设置价格 - 确保格式正确
        holder.txtPrice.text = "¥${String.format("%.2f", item.saleUnitPrice)}"
        
        // 设置数量
        holder.txtQuantity.text = "库存: ${item.availableNum}"
        
        // 加载图片 - 安全处理可能为null的集合
        val imageUrl = when {
            item.skuInfo != null && item.skuInfo.picUrl != null && item.skuInfo.picUrl.isNotEmpty() -> item.skuInfo.picUrl[0].url
            item.picUrl != null && item.picUrl!!.isNotEmpty() -> item.picUrl!![0].url
            item.article != null && item.article.imageFiles != null && item.article.imageFiles.isNotEmpty() -> item.article.imageFiles[0].url
            else -> null
        }
        
        if (imageUrl != null) {
            Glide.with(holder.imgItem.context)
                .load(imageUrl)
                .placeholder(R.drawable.ic_image_placeholder)
                .error(R.drawable.ic_image_placeholder)
                .into(holder.imgItem)
        } else {
            holder.imgItem.setImageResource(R.drawable.ic_image_placeholder)
        }
        
        // 设置点击事件
        holder.imgItem.setOnClickListener {
            imageUrl?.let { url -> onImageClickListener?.invoke(url) }
        }
        // 移除itemView点击事件
        holder.itemView.setOnClickListener(null)
    }
    
    /**
     * 添加新数据（用于分页加载）
     */
    fun addItems(newItems: List<WarehouseItem>) {
        val currentList = currentList.toMutableList()
        currentList.addAll(newItems)
        submitList(currentList)
    }
    
    /**
     * 差异比较回调
     */
    private class WarehouseDiffCallback : DiffUtil.ItemCallback<WarehouseItem>() {
        override fun areItemsTheSame(oldItem: WarehouseItem, newItem: WarehouseItem): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: WarehouseItem, newItem: WarehouseItem): Boolean {
            return oldItem == newItem
        }
    }
} 
package com.example.repairorderapp.ui.customer

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import com.example.repairorderapp.databinding.DialogSelfRepairDetailBinding
import com.example.repairorderapp.model.customer.SelfRepairDetail
import java.text.SimpleDateFormat
import java.util.*

/**
 * 自修详情弹窗
 */
class SelfRepairDetailDialogFragment : DialogFragment() {

    private var _binding: DialogSelfRepairDetailBinding? = null
    private val binding get() = _binding!!
    
    private var selfRepairDetail: SelfRepairDetail? = null

    companion object {
        private const val ARG_SELF_REPAIR_DETAIL = "self_repair_detail"

        fun newInstance(selfRepairDetail: SelfRepairDetail): SelfRepairDetailDialogFragment {
            val fragment = SelfRepairDetailDialogFragment()
            val args = Bundle()
            args.putSerializable(ARG_SELF_REPAIR_DETAIL, selfRepairDetail)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selfRepairDetail = it.getSerializable(ARG_SELF_REPAIR_DETAIL) as? SelfRepairDetail
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogSelfRepairDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupViews()
        bindData()
    }

    private fun setupViews() {
        // 关闭按钮
        binding.ivClose.setOnClickListener {
            dismiss()
        }
        
        // 确定按钮
        binding.btnConfirm.setOnClickListener {
            dismiss()
        }
    }

    private fun bindData() {
        selfRepairDetail?.let { detail ->
            // 自修单号
            binding.tvCode.text = detail.code
            
            // 客户编号
            binding.tvCustomerSeqId.text = detail.customerSeqId
            
            // 店铺名称
            binding.tvCustomerName.text = detail.customerName
            
            // 设备组名称
            binding.tvDeviceGroup.text = detail.customerDeviceGroup?.deviceGroup?.label ?: "未知"
            
            // 品牌/型号
            val brandMachine = "${detail.brand}/${detail.machine}"
            binding.tvBrandMachine.text = brandMachine
            
            // 维修时间
            binding.tvCreatedAt.text = formatDateTime(detail.createdAt)
            
            // 维修描述
            binding.tvExcDesc.text = detail.excDesc.ifEmpty { "无维修描述" }
        }
    }
    
    /**
     * 格式化日期时间
     */
    private fun formatDateTime(dateTimeStr: String): String {
        return try {
            if (dateTimeStr.isEmpty()) {
                "未知时间"
            } else {
                // 尝试解析ISO格式或其他常见格式
                val inputFormat = when {
                    dateTimeStr.contains("T") -> SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
                    dateTimeStr.length > 10 -> SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                    else -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                }
                val outputFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                
                val date = inputFormat.parse(dateTimeStr)
                date?.let { outputFormat.format(it) } ?: dateTimeStr
            }
        } catch (e: Exception) {
            // 如果解析失败，直接返回原字符串
            dateTimeStr.ifEmpty { "未知时间" }
        }
    }

    override fun onStart() {
        super.onStart()
        // 设置弹窗宽度为屏幕宽度的90%
        val dialog = dialog
        if (dialog != null) {
            val width = (resources.displayMetrics.widthPixels * 0.9).toInt()
            dialog.window?.setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 
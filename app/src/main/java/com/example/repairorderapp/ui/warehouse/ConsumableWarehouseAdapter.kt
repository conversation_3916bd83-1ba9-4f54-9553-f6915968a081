package com.example.repairorderapp.ui.warehouse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.repairorderapp.R
import com.example.repairorderapp.model.warehouse.WarehouseItem

/**
 * 耗材仓库适配器 - 处理耗材仓库特定的数据结构
 */
class ConsumableWarehouseAdapter : ListAdapter<WarehouseItem, ConsumableWarehouseAdapter.ViewHolder>(WarehouseDiffCallback()) {

    private var onItemClickListener: ((WarehouseItem) -> Unit)? = null
    private var onImageClickListener: ((String) -> Unit)? = null

    fun setOnItemClickListener(listener: (WarehouseItem) -> Unit) {
        onItemClickListener = listener
    }

    fun setOnImageClickListener(listener: (String) -> Unit) {
        onImageClickListener = listener
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val imgItem: ImageView = view.findViewById(R.id.image_item)
        val txtName: TextView = view.findViewById(R.id.text_item_name)
        val txtArticleCode: TextView = view.findViewById(R.id.text_article_code)
        val txtOem: TextView = view.findViewById(R.id.text_oem)
        val txtCategory: TextView = view.findViewById(R.id.text_attr)
        val txtPrice: TextView = view.findViewById(R.id.text_price)
        val txtQuantity: TextView = view.findViewById(R.id.text_stock)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_warehouse, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = getItem(position)
        
        // 名称和编号拆分显示，不再组合
        holder.txtName.text = item.itemName
        // 设置物品编码
        holder.txtArticleCode.text = "物品编码: ${item.code ?: ""}"
        
        // 设置物品编号
        holder.txtOem.text = "OEM编号: ${item.oemNumber ?: ""}"
        
        // 设置属性信息 - 组合制造商信息和分类
        val manufacturerInfo = StringBuilder()
        
        // 添加OEM编号
        if (item.oemNumber?.isNotEmpty() == true) {
            manufacturerInfo.append("OEM编号: ${item.oemNumber}\n")
        }
        
        // 添加制造商物品信息
        if (item.article?.manufacturerGoodsName?.isNotEmpty() == true) {
            manufacturerInfo.append("制造商物品: ${item.article.manufacturerGoodsName}\n")
        }
        
        // 添加制造商编号信息
        if (item.article?.manufacturerGoodsCode?.isNotEmpty() == true) {
            manufacturerInfo.append("制造商编号: ${item.article.manufacturerGoodsCode}\n")
        }
        
        // 添加制造渠道
        if (item.article?.manufacturerChannel?.label?.isNotEmpty() == true) {
            manufacturerInfo.append("制造渠道: ${item.article.manufacturerChannel.label}")
        }
        
        // 设置详细属性文本
        holder.txtCategory.text = manufacturerInfo.toString()
        
        // 设置储位信息 - 无论是否为空都显示
        val locationText = if (item.location?.isNotEmpty() == true) {
            "储位: ${item.location}"
        } else {
            "储位: 未填写储位"
        }
        holder.txtPrice.text = locationText
        
        // 设置库存数量
        holder.txtQuantity.text = "库存: ${item.sumWarehouseNumber ?: 0}"
        
        // 加载图片 - 从article中获取
        var imageUrl: String? = null
        
        // 尝试从article的imageFiles获取图片
        if (item.article?.imageFiles != null && item.article.imageFiles.isNotEmpty()) {
            imageUrl = item.article.imageFiles[0].url
        }
        
        if (imageUrl != null) {
            Glide.with(holder.imgItem.context)
                .load(imageUrl)
                .placeholder(R.drawable.ic_image_placeholder)
                .error(R.drawable.ic_image_placeholder)
                .into(holder.imgItem)
        } else {
            holder.imgItem.setImageResource(R.drawable.ic_image_placeholder)
        }
        
        // 设置点击事件
        holder.imgItem.setOnClickListener {
            imageUrl?.let { url -> onImageClickListener?.invoke(url) }
        }
        // 设置整个项目的点击事件
        holder.itemView.setOnClickListener {
            onItemClickListener?.invoke(item)
        }
    }
    
    /**
     * 添加新数据（用于分页加载）
     */
    fun addItems(newItems: List<WarehouseItem>) {
        val currentList = currentList.toMutableList()
        currentList.addAll(newItems)
        submitList(currentList)
    }
    
    /**
     * 差异比较回调
     */
    private class WarehouseDiffCallback : DiffUtil.ItemCallback<WarehouseItem>() {
        override fun areItemsTheSame(oldItem: WarehouseItem, newItem: WarehouseItem): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: WarehouseItem, newItem: WarehouseItem): Boolean {
            return oldItem == newItem
        }
    }
} 
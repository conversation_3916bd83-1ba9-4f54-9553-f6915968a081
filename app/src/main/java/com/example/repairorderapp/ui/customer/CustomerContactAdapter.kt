package com.example.repairorderapp.ui.customer

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.repairorderapp.databinding.ItemCustomerContactBinding
import com.example.repairorderapp.model.customer.CustomerContact

/**
 * 客户联系人列表适配器
 */
class CustomerContactAdapter(
    private val onCallClick: (CustomerContact) -> Unit,
    private val onEditClick: (CustomerContact) -> Unit,
    private val onDeleteClick: (CustomerContact) -> Unit
) : ListAdapter<CustomerContact, CustomerContactAdapter.ContactViewHolder>(ContactDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ContactViewHolder {
        val binding = ItemCustomerContactBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ContactViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ContactViewHolder, position: Int) {
        val contact = getItem(position)
        holder.bind(contact)
    }

    inner class ContactViewHolder(private val binding: ItemCustomerContactBinding) : RecyclerView.ViewHolder(binding.root) {
        
        init {
            binding.btnCall.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onCallClick(getItem(position))
                }
            }
            
            binding.btnEdit.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onEditClick(getItem(position))
                }
            }
            
            binding.btnDelete.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onDeleteClick(getItem(position))
                }
            }
        }
        
        fun bind(contact: CustomerContact) {
            // 设置联系人名称，如果是默认联系人，添加标记
            binding.tvContactName.text = if (contact.isDefault) {
                "${contact.name} (默认)"
            } else {
                contact.name
            }
            
            // 设置职位
            binding.tvPosition.text = contact.position ?: "未设置职位"
            
            // 设置电话
            binding.tvPhone.text = contact.phone ?: "未设置电话"

        }
    }

    private class ContactDiffCallback : DiffUtil.ItemCallback<CustomerContact>() {
        override fun areItemsTheSame(oldItem: CustomerContact, newItem: CustomerContact): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: CustomerContact, newItem: CustomerContact): Boolean {
            return oldItem == newItem
        }
    }
} 
package com.example.repairorderapp.ui.warehouse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.repairorderapp.R
import com.example.repairorderapp.model.warehouse.ReturnHistoryDetail

/**
 * 退料记录详情适配器
 */
class ReturnHistoryDetailAdapter : RecyclerView.Adapter<ReturnHistoryDetailAdapter.ViewHolder>() {

    private var detailList: List<ReturnHistoryDetail> = emptyList()
    private var onImageClickListener: ((String) -> Unit)? = null
    
    fun submitList(list: List<ReturnHistoryDetail>) {
        this.detailList = list
        notifyDataSetChanged()
    }
    
    fun setOnImageClickListener(listener: (String) -> Unit) {
        this.onImageClickListener = listener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_return_history_detail, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val detail = detailList[position]
        holder.bind(detail)
    }

    override fun getItemCount(): Int = detailList.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivProduct: ImageView = itemView.findViewById(R.id.iv_product)
        private val tvProductName: TextView = itemView.findViewById(R.id.tv_product_name)
        private val tvArticleCode: TextView = itemView.findViewById(R.id.tv_article_code)
        private val tvOemNumber: TextView = itemView.findViewById(R.id.tv_oem_number)
        private val tvSpec: TextView = itemView.findViewById(R.id.tv_spec)
        private val tvPrice: TextView = itemView.findViewById(R.id.tv_price)
        private val tvQuantityInfo: TextView = itemView.findViewById(R.id.tv_quantity_info)

        fun bind(detail: ReturnHistoryDetail) {
            detail.item?.let { item ->
                tvProductName.text = item.itemName
                
                // 设置物品编号
                val articleCode = item.articleCode ?: "未知"
                tvArticleCode.text = "物品编号: $articleCode"
                
                tvOemNumber.text = "OEM编号: ${item.oemNumber}"
                
                // 规格信息 - 只显示属性值，不显示属性名
                val specText = item.skuInfo?.saleAttrVals?.joinToString(", ") { it.`val` } ?: "未知"
                tvSpec.text = specText
                
                // 价格
                tvPrice.text = "¥${item.saleUnitPrice}"
                
                // 数量信息
                tvQuantityInfo.text = "申请: ${detail.num}  审核: ${detail.auditNum}"
                
                // 图片 - 添加安全的null检查
                val imageUrl = item.skuInfo?.picUrl?.firstOrNull()?.url
                    ?: item.picUrl?.firstOrNull()?.url
                
                if (imageUrl != null) {
                    Glide.with(ivProduct.context)
                        .load(imageUrl)
                        .placeholder(R.drawable.ic_placeholder)
                        .error(R.drawable.ic_placeholder)
                        .into(ivProduct)
                    
                    // 设置图片点击事件
                    ivProduct.setOnClickListener {
                        onImageClickListener?.invoke(imageUrl)
                    }
                } else {
                    Glide.with(ivProduct.context)
                        .load(R.drawable.ic_placeholder)
                        .into(ivProduct)
                    
                    ivProduct.setOnClickListener(null)
                }
            }
        }
    }
} 
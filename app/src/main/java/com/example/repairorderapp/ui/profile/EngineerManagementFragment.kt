package com.example.repairorderapp.ui.profile

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.RetrofitClient
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.model.Engineer
import com.example.repairorderapp.model.EngineerWorkData
import com.example.repairorderapp.ui.login.LoginActivity
import com.example.repairorderapp.ui.orders.EngineerAdapter
import com.example.repairorderapp.ui.orders.EngineerListResponse
import com.example.repairorderapp.ui.orders.EngineerPageData
import com.example.repairorderapp.util.SharedPrefsManager
import com.google.android.material.chip.Chip
import com.google.android.material.chip.ChipGroup
import com.google.gson.Gson
import com.google.gson.JsonObject
import org.json.JSONObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

class EngineerManagementFragment : Fragment() {
    
    private lateinit var recyclerView: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var tvEmpty: TextView
    private lateinit var engineerAdapter: EngineerAdapter
    private lateinit var sharedPrefsManager: SharedPrefsManager
    private lateinit var etSearch: EditText
    private lateinit var layoutEmptyView: LinearLayout
    private lateinit var tvTotalCount: TextView
    private lateinit var tvActiveCount: TextView
    private lateinit var tvAvailableCount: TextView
    private lateinit var layoutTotalEngineers: LinearLayout
    private lateinit var layoutActiveEngineers: LinearLayout
    private lateinit var layoutAvailableEngineers: LinearLayout
    
    // 当前筛选模式
    private enum class FilterMode {
        ALL, ACTIVE, AVAILABLE
    }
    
    private var currentFilterMode = FilterMode.ALL
    
    // 存储工程师ID与工单概要数据的映射
    private val engineerWorkDataMap = ConcurrentHashMap<String, EngineerWorkData>()
    
    // 工程师列表
    private var engineerList = mutableListOf<Engineer>()
    
    // 使用带认证的Retrofit实例创建API接口
    private val workOrderApi by lazy {
        val tokenPrefs = requireContext().getSharedPreferences("token_pref", Context.MODE_PRIVATE)
        val token = tokenPrefs.getString("accessToken", "") ?: ""
        if (token.isNotEmpty()) {
            RetrofitClient.createWithAuth(token).create(WorkOrderApi::class.java)
        } else {
            RetrofitClient.getInstance().create(WorkOrderApi::class.java)
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_engineer_management, container, false)
        
        // 初始化视图
        recyclerView = view.findViewById(R.id.recycler_engineers)
        progressBar = view.findViewById(R.id.progress_bar)
        tvEmpty = view.findViewById(R.id.tv_empty)
        etSearch = view.findViewById(R.id.et_search)
        layoutEmptyView = view.findViewById(R.id.layout_empty_view)
        tvTotalCount = view.findViewById(R.id.tv_total_count)
        tvActiveCount = view.findViewById(R.id.tv_active_count)
        tvAvailableCount = view.findViewById(R.id.tv_available_count)
        layoutTotalEngineers = view.findViewById(R.id.layout_total_engineers)
        layoutActiveEngineers = view.findViewById(R.id.layout_active_engineers)
        layoutAvailableEngineers = view.findViewById(R.id.layout_available_engineers)
        
        // 初始化SharedPrefsManager
        sharedPrefsManager = SharedPrefsManager(requireContext())
        
        setupRecyclerView()
        setupListeners()

        // 设置初始高亮状态（默认显示全部工程师）
        highlightSelectedFilter()

        loadEngineers()
        
        return view
    }
    
    private fun setupRecyclerView() {
        engineerAdapter = EngineerAdapter(emptyList()) { engineer ->
            // 点击工程师项跳转到工程师工单列表
            navigateToEngineerOrderList(engineer)
        }
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        recyclerView.adapter = engineerAdapter
    }
    
    private fun setupListeners() {
        // 搜索框文本变化监听
        etSearch.setOnEditorActionListener { _, _, _ ->
            val query = etSearch.text.toString().trim()
            if (query.isNotEmpty()) {
                filterEngineersByQuery(query)
            } else {
                // 如果搜索框为空，根据当前筛选模式显示
                applyCurrentFilterMode()
            }
            true
        }

        // 总工程师点击事件
        layoutTotalEngineers.setOnClickListener {
            if (currentFilterMode != FilterMode.ALL) {
                currentFilterMode = FilterMode.ALL
                updateEngineerList(engineerList)
                highlightSelectedFilter()
                Toast.makeText(requireContext(), "显示全部工程师", Toast.LENGTH_SHORT).show()
            }
        }

        // 活跃工程师点击事件
        layoutActiveEngineers.setOnClickListener {
            if (currentFilterMode != FilterMode.ACTIVE) {
                currentFilterMode = FilterMode.ACTIVE
                filterActiveEngineers()
                highlightSelectedFilter()
                Toast.makeText(requireContext(), "已筛选活跃工程师", Toast.LENGTH_SHORT).show()
            } else {
                currentFilterMode = FilterMode.ALL
                updateEngineerList(engineerList)
                highlightSelectedFilter()
                Toast.makeText(requireContext(), "显示全部工程师", Toast.LENGTH_SHORT).show()
            }
        }
        
        // 空闲工程师点击事件
        layoutAvailableEngineers.setOnClickListener {
            if (currentFilterMode != FilterMode.AVAILABLE) {
                currentFilterMode = FilterMode.AVAILABLE
                filterAvailableEngineers()
                highlightSelectedFilter()
                Toast.makeText(requireContext(), "已筛选空闲工程师", Toast.LENGTH_SHORT).show()
            } else {
                currentFilterMode = FilterMode.ALL
                updateEngineerList(engineerList)
                highlightSelectedFilter()
                Toast.makeText(requireContext(), "显示全部工程师", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun highlightSelectedFilter() {
        // 重置所有过滤器的背景
        layoutTotalEngineers.setBackgroundResource(android.R.color.transparent)
        layoutActiveEngineers.setBackgroundResource(android.R.color.transparent)
        layoutAvailableEngineers.setBackgroundResource(android.R.color.transparent)
        
        // 设置选中的过滤器背景
        when (currentFilterMode) {
            FilterMode.ALL -> layoutTotalEngineers.setBackgroundResource(R.drawable.bg_selected_filter)
            FilterMode.ACTIVE -> layoutActiveEngineers.setBackgroundResource(R.drawable.bg_selected_filter)
            FilterMode.AVAILABLE -> layoutAvailableEngineers.setBackgroundResource(R.drawable.bg_selected_filter)
            else -> {} // 不需要高亮
        }
    }
    
    private fun applyCurrentFilterMode() {
        when (currentFilterMode) {
            FilterMode.ACTIVE -> filterActiveEngineers()
            FilterMode.AVAILABLE -> filterAvailableEngineers()
            FilterMode.ALL -> updateEngineerList(engineerList)
        }
    }
    
    private fun filterEngineersByQuery(query: String) {
        val filteredList = engineerList.filter { engineer ->
            engineer.name.contains(query, ignoreCase = true) || 
            (engineer.phone?.contains(query, ignoreCase = true) ?: false)
        }
        updateEngineerList(filteredList)
    }
    
    private fun filterActiveEngineers() {
        val activeEngineers = engineerList.filter { engineer ->
            val workData = engineerWorkDataMap[engineer.id]
            workData?.todayWorkNum ?: 0 > 0
        }
        updateEngineerList(activeEngineers)
    }
    
    private fun filterAvailableEngineers() {
        val availableEngineers = engineerList.filter { engineer ->
            val workData = engineerWorkDataMap[engineer.id]
            workData?.notReceiveNum ?: 0 == 0 && (workData?.receiveNum ?: 0) == 0
        }
        updateEngineerList(availableEngineers)
    }
    
    private fun updateEngineerList(engineers: List<Engineer>) {
        val adapter = EngineerAdapter(engineers) { engineer ->
            // 点击工程师项跳转到工程师工单列表
            navigateToEngineerOrderList(engineer)
        }
        
        // 将工程师的工单数据设置到适配器
        engineerWorkDataMap.forEach { (engineerId, workData) ->
            adapter.updateEngineerWorkData(engineerId, workData)
        }
        
        recyclerView.adapter = adapter
        
        // 更新空视图状态
        if (engineers.isEmpty()) {
            layoutEmptyView.visibility = View.VISIBLE
        } else {
            layoutEmptyView.visibility = View.GONE
        }
    }
    
    private fun loadEngineers() {
        // 显示进度条
        progressBar.visibility = View.VISIBLE
        
        // 隐藏内容区域，直到所有数据加载完成
        hideContentViews()
        
        // 准备查询参数
        val params = mapOf(
            "pageNumber" to "1",
            "pageSize" to "1000"
        )
        
        // 调用API获取工程师列表
        val call = workOrderApi.getAllEngineerList("1002", params)
        call.enqueue(object : Callback<EngineerListResponse> {
            override fun onResponse(call: Call<EngineerListResponse>, response: Response<EngineerListResponse>) {
                if (!isAdded) return // 如果Fragment已分离，则返回
                
                if (response.isSuccessful) {
                    val engineerResponse = response.body()
                    val code = engineerResponse?.code ?: -1
                    
                    if (code == 200) {
                        try {
                            // 日志记录响应内容，方便调试
                            Log.d("EngineerManagement", "获取到响应: $engineerResponse")
                            
                            // 处理服务器返回的特定格式
                            engineerList.clear()
                            
                            // 从data.rows获取工程师列表
                            val dataMap = engineerResponse?.data as? Map<*, *>
                            if (dataMap != null) {
                                val rows = dataMap["rows"] as? List<*>
                                
                                if (!rows.isNullOrEmpty()) {
                                    for (item in rows) {
                                        if (item is Map<*, *>) {
                                            val id = item["id"]?.toString() ?: ""
                                            val name = item["name"]?.toString() ?: ""
                                            val phone = item["mobileNumber"]?.toString()
                                            
                                            if (id.isNotBlank()) {
                                                engineerList.add(Engineer(
                                                    id = id,
                                                    name = name,
                                                    phone = phone
                                                ))
                                            }
                                        }
                                    }
                                }
                            }
                            
                            if (engineerList.isNotEmpty()) {
                                Log.d("EngineerManagement", "解析到 ${engineerList.size} 个工程师")
                                
                                // 清空工单概要数据映射
                                engineerWorkDataMap.clear()
                                
                                // 计数器，用于跟踪已加载的工程师工单概要数量
                                val loadCounter = AtomicInteger(0)
                                val totalEngineers = engineerList.size
                                
                                // 逐个获取工程师工单概要信息
                                engineerList.forEach { engineer ->
                                    loadEngineerWorkSummary(engineer.id) { engineerId, workData ->
                                        if (workData != null) {
                                            // 保存工单概要数据
                                            engineerWorkDataMap[engineerId] = workData
                                        }
                                        
                                        // 计数器增加
                                        val loaded = loadCounter.incrementAndGet()
                                        
                                        // 如果所有工程师工单概要都已加载完毕，进行排序并显示
                                        if (loaded == totalEngineers) {
                                            sortEngineersByTodayWorkNum()
                                            // 所有数据加载完成，显示内容
                                            showContentViews()
                                        }
                                    }
                                }
                            } else {
                                // 无数据，隐藏进度条，显示空视图
                                progressBar.visibility = View.GONE
                                layoutEmptyView.visibility = View.VISIBLE
                            }
                        } catch (e: Exception) {
                            Log.e("EngineerManagement", "解析工程师数据异常", e)
                            handleLoadingError("数据解析错误: ${e.message}")
                        }
                    } else if (code == 401) {
                        // 认证失败，需要重新登录
                        handleSessionExpired()
                    } else {
                        val message = engineerResponse?.msg ?: engineerResponse?.message ?: "获取工程师列表失败"
                        handleLoadingError(message)
                    }
                } else if (response.code() == 401) {
                    // 认证失败，需要重新登录
                    handleSessionExpired()
                } else {
                    handleLoadingError("网络请求失败: ${response.code()}")
                }
            }
            
            override fun onFailure(call: Call<EngineerListResponse>, t: Throwable) {
                if (!isAdded) return
                
                Log.e("EngineerManagement", "获取工程师列表失败", t)
                handleLoadingError("网络请求错误: ${t.message}")
            }
        })
    }
    
    /**
     * 隐藏内容区域，仅显示加载指示器
     */
    private fun hideContentViews() {
        // 获取统计卡片引用
        val cardSummary = view?.findViewById<androidx.cardview.widget.CardView>(R.id.card_summary)
        
        // 隐藏内容视图
        cardSummary?.visibility = View.GONE
        recyclerView.visibility = View.GONE
        layoutEmptyView.visibility = View.GONE
    }
    
    /**
     * 显示内容区域，隐藏加载指示器
     */
    private fun showContentViews() {
        if (!isAdded) return // 如果Fragment已分离，则返回
        
        activity?.runOnUiThread {
            // 获取统计卡片引用
            val cardSummary = view?.findViewById<androidx.cardview.widget.CardView>(R.id.card_summary)
            
            // 隐藏进度条
            progressBar.visibility = View.GONE
            
            // 显示内容视图
            cardSummary?.visibility = View.VISIBLE
            recyclerView.visibility = View.VISIBLE
            
            // 根据当前筛选模式显示内容
            applyCurrentFilterMode()
        }
    }
    
    /**
     * 处理加载错误
     */
    private fun handleLoadingError(message: String) {
        if (!isAdded) return
        
        activity?.runOnUiThread {
            progressBar.visibility = View.GONE
            tvEmpty.text = message
            layoutEmptyView.visibility = View.VISIBLE
        }
    }
    
    /**
     * 根据剩余工单数数量对工程师列表进行倒序排序并刷新显示
     */
    private fun sortEngineersByTodayWorkNum() {
        if (!isAdded) return // 如果Fragment已分离，不执行排序
        
        // 对工程师列表按照剩余工单数数量进行排序（倒序）
        val sortedEngineers = engineerList.sortedByDescending { engineer ->
            engineerWorkDataMap[engineer.id]?.receiveNum ?: 0
        }
        
        // 更新排序后的列表
        engineerList.clear()
        engineerList.addAll(sortedEngineers)
        
        // 日志输出排序结果
        Log.d("EngineerManagement", "按剩余工单数排序后的工程师列表:")
        engineerList.forEachIndexed { index, engineer ->
            val workNum = engineerWorkDataMap[engineer.id]?.receiveNum ?: 0
            Log.d("EngineerManagement", "排序位置 $index: ${engineer.name}, 剩余工单数: $workNum")
        }
        
        // 更新统计数据
        updateStatistics()
        
        // 更新适配器数据
        activity?.runOnUiThread {
            val adapter = EngineerAdapter(engineerList) { engineer ->
                // 点击工程师项跳转到工程师工单列表
                navigateToEngineerOrderList(engineer)
            }
            
            // 将工程师的工单数据设置到适配器
            engineerWorkDataMap.forEach { (engineerId, workData) ->
                adapter.updateEngineerWorkData(engineerId, workData)
            }
            
            recyclerView.adapter = adapter
            
            // 更新空视图状态
            if (engineerList.isEmpty()) {
                layoutEmptyView.visibility = View.VISIBLE
            } else {
                layoutEmptyView.visibility = View.GONE
            }
            
            Toast.makeText(requireContext(), "已按剩余工单数排序", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 更新统计信息
     */
    private fun updateStatistics() {
        if (!isAdded) return
        
        val totalEngineers = engineerList.size
        
        // 统计活跃工程师数量（今日有工单的工程师）
        val activeEngineers = engineerList.count { engineer ->
            val workData = engineerWorkDataMap[engineer.id]
            workData?.todayWorkNum ?: 0 > 0
        }
        
        // 统计空闲工程师数量（没有待处理工单的工程师）
        val availableEngineers = engineerList.count { engineer ->
            val workData = engineerWorkDataMap[engineer.id]
            workData?.notReceiveNum ?: 0 == 0 && (workData?.receiveNum ?: 0) == 0
        }
        
        // 更新UI
        activity?.runOnUiThread {
            tvTotalCount.text = "$totalEngineers"
            tvActiveCount.text = "$activeEngineers"
            tvAvailableCount.text = "$availableEngineers"
        }
    }
    
    private fun loadEngineerWorkSummary(engineerId: String, callback: (String, EngineerWorkData?) -> Unit) {
        val call = workOrderApi.getEngineerWorkSummary(engineerId)
        call.enqueue(object : Callback<ApiResponse<EngineerWorkData>> {
            override fun onResponse(call: Call<ApiResponse<EngineerWorkData>>, response: Response<ApiResponse<EngineerWorkData>>) {
                if (response.isSuccessful) {
                    val summaryResponse = response.body()
                    val code = summaryResponse?.code ?: -1
                    
                    if (code == 200) {
                        // 获取工程师工单概要数据
                        val workData = summaryResponse?.data
                        callback(engineerId, workData)
                        
                        // 更新当前适配器中的数据
                        if (workData != null) {
                            (recyclerView.adapter as? EngineerAdapter)?.updateEngineerWorkData(engineerId, workData)
                        }
                    } else if (code == 401) {
                        // 认证失败，需要重新登录
                        handleSessionExpired()
                        callback(engineerId, null)
                    } else {
                        callback(engineerId, null)
                    }
                } else if (response.code() == 401) {
                    // 认证失败，需要重新登录
                    handleSessionExpired()
                    callback(engineerId, null)
                } else {
                    callback(engineerId, null)
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<EngineerWorkData>>, t: Throwable) {
                Log.e("EngineerManagement", "获取工程师工单概要失败: $engineerId", t)
                callback(engineerId, null)
            }
        })
    }
    
    /**
     * 处理会话过期，跳转到登录页面
     */
    private fun handleSessionExpired() {
        // 避免重复处理
        if (!isAdded) return
        
        Toast.makeText(requireContext(), "会话已过期，请重新登录", Toast.LENGTH_LONG).show()
        
        // 清除认证信息
        sharedPrefsManager.clearUserData()
        
        // 清除token_pref中的登录信息
        val tokenPrefs = requireContext().getSharedPreferences("token_pref", Context.MODE_PRIVATE)
        tokenPrefs.edit().apply {
            remove("token")
            remove("accessToken")
            remove("userId")
            remove("userName")
            remove("isLogin")
            apply()
        }
        
        // 跳转到登录页面
        val intent = Intent(requireContext(), LoginActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        startActivity(intent)
        requireActivity().finish()
    }
    
    /**
     * 跳转到工程师工单列表页面
     */
    private fun navigateToEngineerOrderList(engineer: Engineer) {
        try {
            // 准备需要传递的数据
            val bundle = Bundle().apply {
                putString("engineerId", engineer.id)
                putString("engineerName", engineer.name)
            }
            
            // 使用Navigation Component导航到工单列表页面
            findNavController().navigate(R.id.action_engineerManagementFragment_to_orderListFragment, bundle)
            
            Toast.makeText(requireContext(), "查看工程师 ${engineer.name} 的工单", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e("EngineerManagement", "导航到工单列表失败", e)
            Toast.makeText(requireContext(), "导航失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
} 
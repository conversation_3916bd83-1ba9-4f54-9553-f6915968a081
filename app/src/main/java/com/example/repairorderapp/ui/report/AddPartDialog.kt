package com.example.repairorderapp.ui.report

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import com.example.repairorderapp.R
import com.example.repairorderapp.model.ReplacedPart

/**
 * 添加零件对话框
 */
class AddPartDialog(
    context: Context,
    private val onPartAdded: (ReplacedPart) -> Unit
) : Dialog(context) {
    
    private lateinit var etPartName: EditText
    private lateinit var etPartCode: EditText
    private lateinit var etPartQuantity: EditText
    private lateinit var btnCancel: Button
    private lateinit var btnConfirm: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_add_part)
        
        // 初始化视图
        etPartName = findViewById(R.id.et_part_name)
        etPartCode = findViewById(R.id.et_part_code)
        etPartQuantity = findViewById(R.id.et_quantity)
        btnCancel = findViewById(R.id.btn_cancel)
        btnConfirm = findViewById(R.id.btn_add)
        
        // 设置取消按钮
        btnCancel.setOnClickListener { 
            dismiss() 
        }
        
        // 设置确认按钮
        btnConfirm.setOnClickListener {
            validateAndAddPart()
        }
    }
    
    private fun validateAndAddPart() {
        val name = etPartName.text.toString().trim()
        val code = etPartCode.text.toString().trim()
        val quantityStr = etPartQuantity.text.toString().trim()
        
        // 验证输入
        if (name.isEmpty()) {
            Toast.makeText(context, "请输入零件名称", Toast.LENGTH_SHORT).show()
            return
        }
        
        if (code.isEmpty()) {
            Toast.makeText(context, "请输入零件编号", Toast.LENGTH_SHORT).show()
            return
        }
        
        if (quantityStr.isEmpty()) {
            Toast.makeText(context, "请输入数量", Toast.LENGTH_SHORT).show()
            return
        }
        
        val quantity = try {
            quantityStr.toInt()
        } catch (e: Exception) {
            Toast.makeText(context, "数量必须为数字", Toast.LENGTH_SHORT).show()
            return
        }
        
        if (quantity <= 0) {
            Toast.makeText(context, "数量必须大于0", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 创建零件对象
        val part = ReplacedPart(name, code, quantity, "CUSTOMER_STORAGE")
        
        // 通知回调
        onPartAdded(part)
        
        // 关闭对话框
        dismiss()
    }
} 
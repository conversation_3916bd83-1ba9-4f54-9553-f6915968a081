package com.example.repairorderapp.ui.report

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.repository.WorkOrderRepository
import com.example.repairorderapp.databinding.ActivityRepairReportDetailBinding
import com.example.repairorderapp.model.ImageItem
import com.example.repairorderapp.model.ReplacedPart
import com.example.repairorderapp.model.ReplaceDetail
import com.example.repairorderapp.model.ReportResponse

class RepairReportDetailActivity : AppCompatActivity() {

    private lateinit var binding: ActivityRepairReportDetailBinding
    
    private val viewModel: RepairReportViewModel by viewModels { 
        RepairReportViewModelFactory(
            WorkOrderRepository(
                ApiClient.createService(com.example.repairorderapp.data.api.WorkOrderApi::class.java)
            )
        ) 
    }
    
    private lateinit var faultImagesAdapter: ImageAdapter
    private lateinit var solutionImagesAdapter: ImageAdapter
    private lateinit var replacedPartsAdapter: ReplacedPartsAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRepairReportDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 获取工单ID
        val workOrderId = intent.getStringExtra(EXTRA_WORK_ORDER_ID)
            ?: throw IllegalArgumentException("Work order ID is required")
            
        // 设置toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        // 初始化适配器
        setupAdapters()
        
        // 观察数据变化
        observeViewModel()
        
        // 设置重试按钮
        binding.btnRetry.setOnClickListener {
            viewModel.loadReportData(workOrderId)
        }
        
        // 加载报告数据
        viewModel.loadReportData(workOrderId)
    }
    
    private fun setupAdapters() {
        // 故障图片适配器 (不显示删除按钮)
        faultImagesAdapter = ImageAdapter(
            onImageClick = { clickedUrl ->
                // 处理图片点击，显示大图并支持滑动查看其他图片
                val currentUrls = faultImagesAdapter.getCurrentUrls()
                val currentIndex = currentUrls.indexOf(clickedUrl)
                if (currentIndex != -1) {
                    showFullScreenImage(currentUrls, currentIndex)
                }
            },
            showDeleteButton = false // 关键：在此处设置不显示删除按钮
        )
        binding.faultImages.adapter = faultImagesAdapter
        binding.faultImages.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)

        // 解决方案图片适配器 (不显示删除按钮)
        solutionImagesAdapter = ImageAdapter(
            onImageClick = { clickedUrl ->
                // 处理图片点击，显示大图并支持滑动查看其他图片
                val currentUrls = solutionImagesAdapter.getCurrentUrls()
                val currentIndex = currentUrls.indexOf(clickedUrl)
                if (currentIndex != -1) {
                    showFullScreenImage(currentUrls, currentIndex)
                }
            },
            showDeleteButton = false // 关键：在此处设置不显示删除按钮
        )
        binding.solutionImages.adapter = solutionImagesAdapter
        binding.solutionImages.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        
        // 更换零件适配器
        replacedPartsAdapter = ReplacedPartsAdapter()
        binding.replacedParts.adapter = replacedPartsAdapter
    }
    
    private fun observeViewModel() {
        // 观察加载状态
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.nestedScrollView.visibility = if (isLoading) View.GONE else View.VISIBLE
            binding.errorContainer.visibility = View.GONE
        }
        
        // 观察错误信息
        viewModel.errorMessage.observe(this) { errorMsg ->
            if (errorMsg.isNotBlank()) {
                binding.errorContainer.visibility = View.VISIBLE
                binding.errorMessage.text = errorMsg
                binding.nestedScrollView.visibility = View.GONE
            } else {
                binding.errorContainer.visibility = View.GONE
            }
        }
        
        // 观察报告数据
        viewModel.reportData.observe(this) { report ->
            bindReportData(report)
        }
    }
    
    private fun bindReportData(report: ReportResponse) {
        // 设备信息
        with(report.customerDeviceGroup) {
            binding.deviceModel.text = deviceGroup.label
            binding.deviceBrand.text = productInfo
            
            // 加载设备图片
            if (deviceGroupImg != null && !deviceGroupImg.url.isNullOrEmpty()) {
                binding.deviceImage.visibility = View.VISIBLE
                Glide.with(this@RepairReportDetailActivity)
                    .load(deviceGroupImg.url)
                    .into(binding.deviceImage)
            } else {
                binding.deviceImage.visibility = View.GONE
            }
        }
        
        // 故障描述
        with(report.repairReport) {
            // 故障描述文本
            binding.faultDescription.text = excDesc
            
            // 故障图片列表
            val faultImageUrls = excDescPics.map { it.url }
            faultImagesAdapter.submitList(faultImageUrls)
            
            // 解决措施文本
            binding.solutionDescription.text = resolveDesc
            
            // 解决措施图片列表
            val solutionImageUrls = resolveDescPics.map { it.url }
            solutionImagesAdapter.submitList(solutionImageUrls)
            
            // 故障分类信息
            binding.excType.text = excType.label
            binding.reasonType.text = reasonType.label
            binding.resolveType.text = resolveType.label
            binding.excUnit.text = excUnit.label
            
            // 计数器信息
            binding.blackWhiteCount.text = blackWhiteCount.toString()
            binding.blackWhiteExclude.text = blackWhiteExclude.toString()
            
            // 仅当彩色计数器有值时才显示彩色计数相关信息
            if (colorCount > 0 || colorExclude > 0) {
                binding.colorCountContainer.visibility = View.VISIBLE
                binding.colorExcludeContainer.visibility = View.VISIBLE
                binding.colorCount.text = colorCount.toString()
                binding.colorExclude.text = colorExclude.toString()
            } else {
                binding.colorCountContainer.visibility = View.GONE
                binding.colorExcludeContainer.visibility = View.GONE
            }
            
            // 上次维修后印量
            binding.printCount.text = printCount.toString()
        }
        
        // 更换零件信息
        report.replaceOrder?.let { replaceOrder ->
            if (replaceOrder.replaceDetailList.isNotEmpty()) {
                binding.partsCard.visibility = View.VISIBLE
                // 转换ReplaceDetail列表为ReplacedPart列表
                val replacedParts = convertToReplacedParts(replaceOrder.replaceDetailList)
                replacedPartsAdapter.submitList(replacedParts)
            } else {
                binding.partsCard.visibility = View.GONE
            }
        } ?: run {
            binding.partsCard.visibility = View.GONE
        }
    }
    
    /**
     * 将ReplaceDetail对象转换为ReplacedPart对象
     */
    private fun convertToReplacedParts(replaceDetails: List<ReplaceDetail>): List<ReplacedPart> {
        return replaceDetails.map { detail ->
            // 提取图片URL（如果有）
            val imageUrl = detail.itemStore.skuInfo?.picUrl?.firstOrNull()?.url
                ?: detail.skuInfo?.picUrl?.firstOrNull()?.url
            
            // 提取更换位置（如果有）
            val location = detail.location?.joinToString(", ")
            
            ReplacedPart(
                name = detail.itemName,
                code = detail.itemStore.oemNumber,
                quantity = detail.num,
                skuSource = detail.itemStore.skuSource,
                articleCode = detail.itemStore.articleCode,
                saleUnitPrice = detail.saleUnitPrice ?: detail.itemStore.saleUnitPrice,
                saleAttrVals = detail.itemStore.skuInfo?.saleAttrVals,
                imageUrl = imageUrl,
                location = location
            )
        }
    }
    
    private fun showFullScreenImage(urls: List<String>, position: Int) {
        // 实现全屏查看图片的功能
        try {
            // 使用自定义对话框显示图片
            val dialog = com.example.repairorderapp.ui.common.ImageViewerDialog(
                this,
                urls,
                position
            )
            dialog.show()
        } catch (e: Exception) {
            // 如果出现异常，记录日志
            android.util.Log.e("ImageViewer", "显示图片查看器失败", e)
            Toast.makeText(this, "无法显示图片", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            onBackPressed()
            return true
        }
        return super.onOptionsItemSelected(item)
    }
    
    companion object {
        const val EXTRA_WORK_ORDER_ID = "extra_work_order_id"
        
        fun start(context: Context, workOrderId: String) {
            val intent = Intent(context, RepairReportDetailActivity::class.java).apply {
                putExtra(EXTRA_WORK_ORDER_ID, workOrderId)
            }
            context.startActivity(intent)
        }
    }
} 
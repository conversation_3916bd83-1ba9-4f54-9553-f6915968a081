package com.example.repairorderapp.ui.customer

import android.app.Activity
import android.app.DatePickerDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.CustomerApi
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.databinding.FragmentCustomerDeviceBasicInfoBinding
import com.example.repairorderapp.model.customer.CustomerDevice
import com.example.repairorderapp.model.customer.CustomerOption
import com.example.repairorderapp.model.customer.ImageItem
import com.example.repairorderapp.ui.customer.ImageUploadAdapter
import com.example.repairorderapp.util.setDebounceClickListener
import com.example.repairorderapp.viewmodel.customer.CustomerDeviceViewModel
import com.example.repairorderapp.viewmodel.customer.CustomerViewModelFactory
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import java.io.IOException

/**
 * 客户设备基本信息Fragment
 */
class CustomerDeviceBasicInfoFragment : Fragment() {

    private var _binding: FragmentCustomerDeviceBasicInfoBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var viewModel: CustomerDeviceViewModel
    private var deviceId: String? = null
    private var customerId: String? = null
    
    // 字典数据列表
    private var deviceOnList = mutableListOf<CustomerOption>()
    private var deviceStatusList = mutableListOf<CustomerOption>()
    private var fixStatusList = mutableListOf<CustomerOption>()
    private var serTypeList = mutableListOf<CustomerOption>()
    private var treatyTypeList = mutableListOf<CustomerOption>()
    private var statisticsOperatNameList = mutableListOf<CustomerOption>()
    private var signOperatNameList = mutableListOf<CustomerOption>()
    private var operatIdList = mutableListOf<CustomerOption>()
    
    // 当前设备信息
    private var currentDevice: CustomerDevice? = null
    
    // 日期格式化
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    
    // 设备图片适配器
    private lateinit var deviceImageAdapter: ImageUploadAdapter
    
    // 权限请求
    private val MULTIPLE_PERMISSIONS_REQUEST = 100
    private val REQUIRED_PERMISSIONS = arrayOf(
        android.Manifest.permission.CAMERA,
        android.Manifest.permission.READ_EXTERNAL_STORAGE,
        android.Manifest.permission.WRITE_EXTERNAL_STORAGE
    )
    
    // 多权限请求
    private val requestMultiplePermissionsLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        var allPermissionsGranted = true
        permissions.entries.forEach {
            if (!it.value) {
                allPermissionsGranted = false
            }
        }
        
        if (allPermissionsGranted) {
            // 所有权限已授予，打开相机
            openCameraWithPermissionGranted()
        } else {
            // 至少有一个权限被拒绝
            Toast.makeText(requireContext(), "需要相机和存储权限才能完成操作", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 图片选择器 - 从相册选择
    private val pickImageFromGalleryLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                // 处理选择的图片
                handleSelectedImage(uri)
            }
        }
    }
    
    // 拍照结果处理
    private val takePhotoLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            // 对于拍照，我们需要从临时文件创建Uri
            currentPhotoUri?.let { uri ->
                // 处理拍照得到的图片
                handleSelectedImage(uri)
            }
        }
    }
    
    // 临时存储拍照的URI
    private var currentPhotoUri: Uri? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCustomerDeviceBasicInfoBinding.inflate(inflater, container, false)
        
        // 获取设备ID和客户ID
        deviceId = arguments?.getString("deviceId")
        customerId = arguments?.getString("customerId")
        
        // 创建CustomerRepository
        val customerApi = ApiClient.createService(CustomerApi::class.java)
        val customerRepository = CustomerRepository(customerApi, null)
        
        // 使用ViewModelFactory创建ViewModel
        val factory = CustomerViewModelFactory(customerRepository)
        viewModel = ViewModelProvider(this, factory)[CustomerDeviceViewModel::class.java]
        
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupToolbar()
        setupDatePickers()
        setupObservers()
        setupListeners()
        setupImageAdapter()
        
        // 加载设备详情
        deviceId?.let {
            viewModel.getCustomerDeviceDetail(it)
            // 在获取设备详情后再加载字典数据
            viewModel.device.observe(viewLifecycleOwner) { device ->
                currentDevice = device
                updateUI(device)
                loadDictionaryData() // 在设备详情加载完成后再加载字典数据
            }
        } ?: run {
            Toast.makeText(requireContext(), "设备ID不能为空", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
        }
    }
    
    private fun setupToolbar() {

    }
    
    private fun setupDatePickers() {
        // 统计开始时间选择器 - 使用防抖
        binding.etStatisticsStartDate.setDebounceClickListener {
            showDateTimePicker(binding.etStatisticsStartDate)
        }
        
        // 签约时间选择器 - 使用防抖
        binding.etSignDate.setDebounceClickListener {
            showDateTimePicker(binding.etSignDate)
        }
    }
    
    private fun showDateTimePicker(editText: com.google.android.material.textfield.TextInputEditText) {
        val calendar = Calendar.getInstance()
        
        // 如果editText已有日期，则解析并设置到calendar
        val currentText = editText.text.toString()
        if (currentText.isNotEmpty()) {
            try {
                val date = dateFormat.parse(currentText)
                if (date != null) {
                    calendar.time = date
                }
            } catch (e: Exception) {
                // 解析失败，使用当前时间
            }
        }
        
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        
        // 先显示日期选择器
        val datePickerDialog = android.app.DatePickerDialog(
            requireContext(),
            { _, selectedYear, selectedMonth, selectedDay ->
                calendar.set(Calendar.YEAR, selectedYear)
                calendar.set(Calendar.MONTH, selectedMonth)
                calendar.set(Calendar.DAY_OF_MONTH, selectedDay)
                
                // 然后显示时间选择器
                showTimePickerDialog(calendar, editText)
            },
            year, month, day
        )
        
        datePickerDialog.show()
    }
    
    private fun showTimePickerDialog(calendar: Calendar, editText: com.google.android.material.textfield.TextInputEditText) {
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        
        // 使用MaterialTimePicker替代传统的TimePickerDialog
        val timePickerDialog = android.app.TimePickerDialog(
            requireContext(),
            { _, selectedHour, selectedMinute ->
                calendar.set(Calendar.HOUR_OF_DAY, selectedHour)
                calendar.set(Calendar.MINUTE, selectedMinute)
                
                // 设置选择的日期和时间
                editText.setText(dateFormat.format(calendar.time))
            },
            hour, minute, true // 使用24小时制
        )
        
        timePickerDialog.show()
    }
    
    private fun setupObservers() {
        viewModel.device.observe(viewLifecycleOwner) { device ->
            if (currentDevice == null) {
                currentDevice = device
                updateUI(device)
            }
        }
        
        viewModel.loading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.contentLayout.visibility = if (isLoading) View.GONE else View.VISIBLE
        }
        
        viewModel.error.observe(viewLifecycleOwner) { errorMsg ->
            if (errorMsg.isNotEmpty()) {
                Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_SHORT).show()
                viewModel.clearError()
            }
        }
        
        viewModel.operationSuccess.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let { message ->
                Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                findNavController().navigateUp()
            }
        }
    }
    
    private fun setupListeners() {
        // 保存按钮点击事件 - 使用1500ms防抖
        binding.btnSave.setDebounceClickListener(1500) {
            saveDeviceInfo()
        }
        
        // 设置Switch文本变化监听
        binding.switchStatus.setOnCheckedChangeListener { _, isChecked ->
            binding.switchStatus.text = if (isChecked) "启用" else "禁用"
        }
        
        binding.switchRegCliState.setOnCheckedChangeListener { _, isChecked ->
            binding.switchRegCliState.text = if (isChecked) "已安装" else "未安装"
        }
        
        binding.switchEnableStatistics.setOnCheckedChangeListener { _, isChecked ->
            binding.switchEnableStatistics.text = if (isChecked) "启用统计" else "禁用统计"
        }
        
        binding.switchDataShowState.setOnCheckedChangeListener { _, isChecked ->
            binding.switchDataShowState.text = if (isChecked) "可见" else "不可见"
        }
        
        // 添加图片点击事件 - 修改逻辑，只有在图片列表为空时才处理点击
        binding.rvDeviceImages.setDebounceClickListener {
            if (deviceImageAdapter.getImages().isEmpty()) {
                showImagePickerDialog()
            }
        }
    }
    
    private fun setupImageAdapter() {
        // 初始化设备图片适配器
        deviceImageAdapter = ImageUploadAdapter(
            maxImages = 1, // 修改为只允许上传一张照片
            onImageAdded = { uri ->
                // 处理图片添加
                handleSelectedImage(uri)
            },
            onImageRemoved = { imageItem ->
                // 处理图片移除
                // 如果需要，可以在这里实现从服务器删除图片的逻辑
            },
            // 添加按钮点击事件处理
            onAddButtonClicked = {
                // 显示图片选择对话框
                showImagePickerDialog()
            }
        )
        
        binding.rvDeviceImages.apply {
            layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = deviceImageAdapter
        }
    }
    
    /**
     * 显示图片选择对话框
     */
    private fun showImagePickerDialog() {
        val options = arrayOf("拍照", "从相册选择")
        
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("选择图片来源")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> checkAndRequestCameraPermission()
                    1 -> openGallery()
                }
            }
            .show()
    }
    
    /**
     * 检查和请求相机权限
     */
    private fun checkAndRequestCameraPermission() {
        // 检查所有需要的权限
        val permissionsToRequest = REQUIRED_PERMISSIONS.filter {
            ContextCompat.checkSelfPermission(requireContext(), it) != PackageManager.PERMISSION_GRANTED
        }.toTypedArray()
        
        when {
            permissionsToRequest.isEmpty() -> {
                // 已有所有权限，打开相机
                openCameraWithPermissionGranted()
            }
            permissionsToRequest.any { shouldShowRequestPermissionRationale(it) } -> {
                // 显示权限说明
                MaterialAlertDialogBuilder(requireContext())
                    .setTitle("需要权限")
                    .setMessage("需要相机和存储权限才能拍照和选择图片。请在设置中授予权限。")
                    .setPositiveButton("确定") { _, _ ->
                        requestMultiplePermissionsLauncher.launch(permissionsToRequest)
                    }
                    .setNegativeButton("取消", null)
                    .show()
            }
            else -> {
                // 请求所有需要的权限
                requestMultiplePermissionsLauncher.launch(permissionsToRequest)
            }
        }
    }
    
    /**
     * 打开相机拍照（权限已授予）
     */
    private fun openCameraWithPermissionGranted() {
        try {
            // 创建临时文件存储拍照结果
            val photoFile = createImageFile()
            photoFile.also {
                val photoURI: Uri = androidx.core.content.FileProvider.getUriForFile(
                    requireContext(),
                    "${requireContext().packageName}.fileprovider",
                    it
                )
                currentPhotoUri = photoURI
                
                val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                takePhotoLauncher.launch(takePictureIntent)
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "打开相机失败", e)
            Toast.makeText(requireContext(), "无法打开相机: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 打开相机拍照
     */
    private fun openCamera() {
        checkAndRequestCameraPermission()
    }
    
    /**
     * 创建临时图片文件
     */
    private fun createImageFile(): java.io.File {
        try {
            // 创建图片文件名
            val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val storageDir: java.io.File? = requireContext().getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES)
            
            if (storageDir == null) {
                throw IOException("无法获取存储目录")
            }
            
            // 确保目录存在
            if (!storageDir.exists()) {
                storageDir.mkdirs()
            }
            
            return java.io.File.createTempFile(
                "JPEG_${timeStamp}_", /* 前缀 */
                ".jpg", /* 后缀 */
                storageDir /* 目录 */
            )
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "创建临时文件失败", e)
            throw e
        }
    }
    
    /**
     * 打开相册选择图片
     */
    private fun openGallery() {
        // 检查存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val hasReadPermission = ContextCompat.checkSelfPermission(
                requireContext(),
                android.Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
            
            if (!hasReadPermission) {
                // 显示权限说明
                MaterialAlertDialogBuilder(requireContext())
                    .setTitle("需要存储权限")
                    .setMessage("需要访问您的相册才能选择图片。请在设置中授予权限。")
                    .setPositiveButton("确定") { _, _ ->
                        // 只请求存储权限
                        ActivityCompat.requestPermissions(
                            requireActivity(),
                            arrayOf(android.Manifest.permission.READ_EXTERNAL_STORAGE),
                            MULTIPLE_PERMISSIONS_REQUEST
                        )
                    }
                    .setNegativeButton("取消", null)
                    .show()
                return
            }
        }
        
        try {
            // 打开图片选择器，限制只能选择图片类型
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            intent.type = "image/*" // 只显示图片
            pickImageFromGalleryLauncher.launch(intent)
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "打开相册失败", e)
            Toast.makeText(requireContext(), "无法打开相册: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun handleSelectedImage(uri: Uri) {
        try {
            android.util.Log.d("CustomerDevice", "处理选择的图片: uri=$uri")
            
            // 显示加载进度
            val progressDialog = android.app.ProgressDialog(requireContext())
            progressDialog.setMessage("正在上传图片...")
            progressDialog.setCancelable(false)
            progressDialog.show()
            
            // 使用COS工具上传图片
            viewLifecycleOwner.lifecycleScope.launch {
                try {
                    // 调用CosUtils上传图片，指定目录为"device_info/"
                    val result = com.example.repairorderapp.util.CosUtils.uploadImage(
                        context = requireContext(),
                        uri = uri,
                        directory = "device_info/"
                    )
                    
                    // 上传成功，创建ImageItem
            val imageItem = ImageItem(
                        url = result.url,
                        name = "设备图片",
                        key = result.key // 保存COS对象键
            )
            
            // 由于限制只能上传一张图片，先清空现有图片
            deviceImageAdapter.setImages(emptyList())
            
            // 将图片添加到适配器
            val imagesToSet = buildList {
                imageItem.let { add(it) }
            }
            deviceImageAdapter.setImages(imagesToSet)
                    
                    // 隐藏进度对话框
                    progressDialog.dismiss()
            
            // 添加日志
                    android.util.Log.d("CustomerDevice", "添加图片成功: url=${result.url}, key=${result.key}")
                } catch (e: Exception) {
                    // 上传失败，显示错误信息
                    progressDialog.dismiss()
                    android.util.Log.e("CustomerDevice", "处理选择的图片时出错", e)
                    Toast.makeText(requireContext(), "处理图片失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "处理选择的图片时出错", e)
            Toast.makeText(requireContext(), "处理图片失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun loadDictionaryData() {
        val customerApi = ApiClient.createService(CustomerApi::class.java)
        
        // 为每个API调用创建单独的协程作用域，避免一个请求失败影响所有请求
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 先设置预定义的服务类型（避免API调用失败）
                // 注意：后端可能使用不同的值格式，需要适配
                serTypeList = mutableListOf(
                    CustomerOption("SCATTERED", "散修"),
                    CustomerOption("NO_WARRANTY", "购机不保"),
                    CustomerOption("WARRANTY", "购机质保"),
                    CustomerOption("BUY_FULL", "购机全保"),
                    CustomerOption("BUY_HALF", "购机半保"),
                    CustomerOption("RENT_FULL", "租赁全保"),
                    CustomerOption("RENT_HALF", "租赁半保"),
                    CustomerOption("ALL", "普通全保"),
                    CustomerOption("HALF", "普通半保"),
                    CustomerOption("PACKAGE_ALL", "包量全保"),
                    CustomerOption("PACKAGE_HALF", "包量半保"),
                    CustomerOption("FINANCING_FULL", "融资全保"),
                    CustomerOption("FINANCING_HALF", "融资半保"),
                    CustomerOption("OTHER", "其它")
                )
                
                // 初始化其他列表，避免空列表
                deviceOnList = mutableListOf(
                    CustomerOption("1", "新机"), 
                    CustomerOption("2", "旧机"),
                    CustomerOption("3", "再生机")
                )
                deviceStatusList = mutableListOf(
                    CustomerOption("1", "正常"), 
                    CustomerOption("2", "故障")
                )
                fixStatusList = mutableListOf(
                    CustomerOption("1", "正常"), 
                    CustomerOption("2", "维修中")
                )
                treatyTypeList = mutableListOf(
                    CustomerOption("1201", "包机"), 
                    CustomerOption("1202", "包印")
                )
                statisticsOperatNameList = mutableListOf(CustomerOption("", "请选择"))
                signOperatNameList = mutableListOf(CustomerOption("", "请选择"))
                operatIdList = mutableListOf(CustomerOption("", "请选择"))
                
                // 安全检查，确保Fragment仍然附加到Context
                if (!isAdded) {
                    return@launch
                }
                
                // 预先更新UI，使用默认值
                withContext(Dispatchers.Main) {
                    if (isAdded && context != null) {
                        setupSpinners()
                    }
                }
                
                // 预加载工程师列表，避免在保存时因协程取消导致错误
                preloadEngineerList()
                
                // 同时加载所有字典数据
                val deferreds = listOf(
                    async { loadDeviceStatus() },
                    async { loadDeviceOn() },
                    async { loadTreatyType() },
                    async { loadFixStatus() },
                    async { loadWorkers() }
                )
                
                // 等待所有请求完成
                deferreds.awaitAll()
                
                // 更新UI
                withContext(Dispatchers.Main) {
                    if (isAdded && context != null) {
                        setupSpinners()
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    if (isAdded && context != null) {
                        // 捕获到异常，但仍然设置Spinners以保证界面可用
                        setupSpinners()
                        Toast.makeText(requireContext(), "部分数据加载失败，使用默认值", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }
    
    // 预加载工程师列表
    private suspend fun preloadEngineerList() {
        try {
            val customerApi = ApiClient.createService(CustomerApi::class.java)
            // 需要先获取当前设备的productId
            val deviceDetail = currentDevice
            val productId = deviceDetail?.productId
                
            if (productId != null) {
                android.util.Log.d("CustomerDevice", "预加载工程师列表: $productId")
                val engineerResponse = customerApi.getEngineerList(productId)
                if (engineerResponse.isSuccess() && engineerResponse.data != null) {
                    operatIdList = mutableListOf(CustomerOption("", "请选择"))
                        
                    engineerResponse.data.forEach { engineer ->
                        val engineerId = engineer["id"]?.toString() ?: ""
                        val name = engineer["name"]?.toString() ?: ""
                        if (engineerId.isNotEmpty() && name.isNotEmpty()) {
                            operatIdList.add(CustomerOption(engineerId, name))
                        }
                    }
                    
                    // 如果有operatId但没有操作人员的名称，尝试查找匹配的名称
                    if (deviceDetail.operatId != null && (deviceDetail.operatName == null || deviceDetail.operatName.isEmpty())) {
                        // 在列表中查找匹配的工程师，设置operatName
                        val matchedEngineer = operatIdList.find { it.value == deviceDetail.operatId }
                        if (matchedEngineer != null) {
                            android.util.Log.d("CustomerDevice", "找到匹配的工程师: ID=${matchedEngineer.value}, 名称=${matchedEngineer.label}")
                        } else {
                            android.util.Log.d("CustomerDevice", "未找到匹配的工程师，ID=${deviceDetail.operatId}")
                        }
                    }
                        
                    android.util.Log.d("CustomerDevice", "预加载工程师列表成功: ${operatIdList.size}个工程师")
                }
            } else {
                android.util.Log.e("CustomerDevice", "预加载工程师列表失败: 缺少productId")
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "预加载工程师列表失败", e)
        }
    }
    
    // 加载设备状态
    private suspend fun loadDeviceStatus() {
        try {
            val customerApi = ApiClient.createService(CustomerApi::class.java)
            val deviceStatusResponse = customerApi.getDictTreeByCode("900")
            if (deviceStatusResponse.isSuccess() && deviceStatusResponse.data != null) {
                deviceStatusList = deviceStatusResponse.data.toMutableList()
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "加载设备状态失败", e)
        }
    }
    
    // 加载设备新旧
    private suspend fun loadDeviceOn() {
        try {
            val customerApi = ApiClient.createService(CustomerApi::class.java)
            val deviceOnResponse = customerApi.getDictTreeByCode("1100")
            if (deviceOnResponse.isSuccess() && deviceOnResponse.data != null) {
                deviceOnList = deviceOnResponse.data.toMutableList()
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "加载设备新旧失败", e)
        }
    }
    
    // 加载合约类型
    private suspend fun loadTreatyType() {
        try {
            val customerApi = ApiClient.createService(CustomerApi::class.java)
            val treatyTypeResponse = customerApi.getDictTreeByCode("1200")
            if (treatyTypeResponse.isSuccess() && treatyTypeResponse.data != null) {
                val responseData = treatyTypeResponse.data
                if (responseData.isNotEmpty()) {
                    treatyTypeList = responseData.toMutableList()
                } else {
                    // 如果API返回空列表，使用默认值
                    initTreatyType()
                }
            } else {
                // 如果API失败，使用默认值
                initTreatyType()
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "加载合约类型失败", e)
            // 发生异常时使用默认值
            initTreatyType()
        }
    }
    
    // 加载维修状态
    private suspend fun loadFixStatus() {
        try {
            val customerApi = ApiClient.createService(CustomerApi::class.java)
            val fixStatusResponse = customerApi.getDictTreeByCode("1500")
            if (fixStatusResponse.isSuccess() && fixStatusResponse.data != null) {
                fixStatusList = fixStatusResponse.data.toMutableList()
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "加载维修状态失败", e)
        }
    }
    
    // 加载工作人员
    private suspend fun loadWorkers() {
        try {
            val customerApi = ApiClient.createService(CustomerApi::class.java)
            val workerParams = mapOf("pageNumber" to 1, "pageSize" to 10000)
            val workerResponse = customerApi.getWorkerList(workerParams)
            if (workerResponse.isSuccess() && workerResponse.data != null) {
                // 按照Vue项目的实现方式处理数据
                statisticsOperatNameList = mutableListOf(CustomerOption("", "请选择"))
                signOperatNameList = mutableListOf(CustomerOption("", "请选择"))
                
                workerResponse.data.forEach { worker ->
                    val name = worker["name"]?.toString() ?: ""
                    if (name.isNotEmpty()) {
                        // 使用name作为value和text，与Vue项目保持一致
                        statisticsOperatNameList.add(CustomerOption(name, name))
                        signOperatNameList.add(CustomerOption(name, name))
                    }
                }
                
                android.util.Log.d("CustomerDevice", "加载工作人员成功: ${statisticsOperatNameList.size}个")
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "加载工作人员失败", e)
        }
    }
    
    private fun getSerTypeBackendValue(label: String): String {
        return when(label) {
            "散修" -> "SCATTERED"
            "全保" -> "FULL_INSURANCE"
            "租赁" -> "LEASE"
            "半保" -> "HALF_INSURANCE"
            "签约" -> "SIGN" 
            "数据" -> "DATA"
            "购机不保" -> "NO_WARRANTY"
            "购机质保" -> "WARRANTY"
            "购机全保" -> "BUY_FULL"
            "购机半保" -> "BUY_HALF"
            "租赁全保" -> "RENT_FULL"
            "租赁半保" -> "RENT_HALF"
            "普通全保" -> "ALL"
            "普通半保" -> "HALF"
            "包量全保" -> "PACKAGE_ALL"
            "包量半保" -> "PACKAGE_HALF"
            "融资全保" -> "FINANCING_FULL"
            "融资半保" -> "FINANCING_HALF"
            "其它" -> "OTHER"
            else -> "SCATTERED" // 默认值
        }
    }
    
    private fun setupSpinners() {
        // 确保Fragment仍然附加到Context
        if (!isAdded || context == null) {
            return
        }

        try {
            // 设置设备新旧Spinner
            val deviceOnAdapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                deviceOnList.map { it.label ?: "" }
            )
            deviceOnAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerDeviceOn.adapter = deviceOnAdapter
            
            // 设置设备状态Spinner
            val deviceStatusAdapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                deviceStatusList.map { it.label ?: "" }
            )
            deviceStatusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerDeviceStatus.adapter = deviceStatusAdapter
            
            // 设置维修状态Spinner
            val fixStatusAdapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                fixStatusList.map { it.label ?: "" }
            )
            fixStatusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerFixStatus.adapter = fixStatusAdapter
            
            // 设置服务类型Spinner
            val serTypeAdapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                serTypeList.map { it.label ?: "" }
            )
            serTypeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerSerType.adapter = serTypeAdapter
            
            // 设置合约类型Spinner
            val treatyTypeAdapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                treatyTypeList.map { it.label ?: "" }
            )
            treatyTypeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerTreatyType.adapter = treatyTypeAdapter
            
            // 设置统计操作人员Spinner
            val statisticsOperatNameAdapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                statisticsOperatNameList.map { it.label ?: "" }
            )
            statisticsOperatNameAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerStatisticsOperatName.adapter = statisticsOperatNameAdapter
            
            // 设置签约操作人员Spinner
            val signOperatNameAdapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                signOperatNameList.map { it.label ?: "" }
            )
            signOperatNameAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerSignOperatName.adapter = signOperatNameAdapter
            
            // 设置负责工程师Spinner
            val operatIdAdapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                operatIdList.map { it.label ?: "" }
            )
            operatIdAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerOperatId.adapter = operatIdAdapter
            
            // 设置当前选中项
            currentDevice?.let { device ->
                try {
                    // 设备新旧
                    try {
                        device.deviceOn?.let { deviceOn ->
                            if (deviceOn.value != null) {
                                val index = deviceOnList.indexOfFirst { it.value == deviceOn.value }
                                if (index >= 0) {
                                    binding.spinnerDeviceOn.setSelection(index)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomerDevice", "设置设备新旧失败", e)
                    }
                    
                    // 设备状态
                    try {
                        device.deviceStatus?.let { deviceStatus ->
                            if (deviceStatus.value != null) {
                                val index = deviceStatusList.indexOfFirst { it.value == deviceStatus.value }
                                if (index >= 0) {
                                    binding.spinnerDeviceStatus.setSelection(index)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomerDevice", "设置设备状态失败", e)
                    }
                    
                    // 维修状态
                    try {
                        device.fixStatus?.let { fixStatus ->
                            if (fixStatus.value != null) {
                                val index = fixStatusList.indexOfFirst { it.value == fixStatus.value }
                                if (index >= 0) {
                                    binding.spinnerFixStatus.setSelection(index)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomerDevice", "设置维修状态失败", e)
                    }
                    
                    // 服务类型
                    try {
                        device.serType?.let { serType ->
                            if (serType.value != null) {
                                val index = serTypeList.indexOfFirst { it.value == serType.value }
                                if (index >= 0) {
                                    binding.spinnerSerType.setSelection(index)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomerDevice", "设置服务类型失败", e)
                    }
                    
                    // 合约类型
                    try {
                        device.treatyType?.let { treatyType ->
                            if (treatyType.value != null) {
                                android.util.Log.d("CustomerDevice", "设置合约类型: value=${treatyType.value}, label=${treatyType.label ?: "null"}")
                                // 先尝试按value匹配
                                var index = treatyTypeList.indexOfFirst { it.value == treatyType.value }
                                if (index < 0) {
                                    // 如果按value未找到，尝试使用默认值1201匹配
                                    index = treatyTypeList.indexOfFirst { it.value == "1201" }
                                }
                                if (index >= 0) {
                                    binding.spinnerTreatyType.setSelection(index)
                                } else if (treatyTypeList.isNotEmpty()) {
                                    // 如果未找到匹配项，但列表不为空，则选择第一项
                                    binding.spinnerTreatyType.setSelection(0)
                                }
                            } else if (treatyTypeList.isNotEmpty()) {
                                // 如果value为null但列表不为空，则选择第一项
                                binding.spinnerTreatyType.setSelection(0)
                            }
                        } ?: run {
                            // 如果treatyType为null但列表不为空，则选择第一项
                            if (treatyTypeList.isNotEmpty()) {
                                binding.spinnerTreatyType.setSelection(0)
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomerDevice", "设置合约类型失败", e)
                        // 如果设置失败但列表不为空，则选择第一项
                        if (treatyTypeList.isNotEmpty()) {
                            binding.spinnerTreatyType.setSelection(0)
                        }
                    }
                    
                    // 统计操作人员
                    try {
                        device.statisticsOperatName?.let { operatName ->
                            if (operatName.isNotEmpty()) {
                                val index = statisticsOperatNameList.indexOfFirst { it.label == operatName }
                                if (index >= 0) {
                                    binding.spinnerStatisticsOperatName.setSelection(index)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomerDevice", "设置统计操作人员失败", e)
                    }
                    
                    // 签约操作人员
                    try {
                        device.signOperatName?.let { operatName ->
                            if (operatName.isNotEmpty()) {
                                val index = signOperatNameList.indexOfFirst { it.label == operatName }
                                if (index >= 0) {
                                    binding.spinnerSignOperatName.setSelection(index)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomerDevice", "设置签约操作人员失败", e)
                    }
                    
                    // 负责工程师
                    try {
                        device.operatId?.let { operatId ->
                            if (operatId.isNotEmpty()) {
                                val index = operatIdList.indexOfFirst { it.value == operatId }
                                if (index >= 0) {
                                    binding.spinnerOperatId.setSelection(index)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("CustomerDevice", "设置负责工程师失败", e)
                    }
                } catch (e: Exception) {
                    android.util.Log.e("CustomerDevice", "设置选中项时出错", e)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "设置Spinner时出错", e)
            Toast.makeText(requireContext(), "设置下拉框时出错: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun updateUI(device: CustomerDevice) {
        try {
            // 基本信息
            binding.tvDeviceGroup.text = device.deviceGroup.label
            binding.tvProductInfo.text = device.productInfo
            
            // 记录原始数据，用于调试和比对
            android.util.Log.d("CustomerDevice", "原始设备数据: " + 
                "serType=${device.serType?.value ?: "null"}/${device.serType?.label ?: "null"}, " + 
                "treatyType=${device.treatyType?.value ?: "null"}/${device.treatyType?.label ?: "null"}, " + 
                "operatId=${device.operatId}, operatName=${device.operatName}, " + 
                "statisticsOperatName=${device.statisticsOperatName}, signOperatName=${device.signOperatName}")
            
            // 开关状态
            binding.switchStatus.isChecked = device.status
            binding.switchStatus.text = if (device.status) "启用" else "禁用"
            
            binding.switchRegCliState.isChecked = device.regCliState
            binding.switchRegCliState.text = if (device.regCliState) "已安装" else "未安装"
            
            binding.switchEnableStatistics.isChecked = device.enableStatistics
            binding.switchEnableStatistics.text = if (device.enableStatistics) "启用统计" else "禁用统计"
            
            binding.switchDataShowState.isChecked = device.dataShowState == 1
            binding.switchDataShowState.text = if (device.dataShowState == 1) "可见" else "不可见"
            
            // 计数器信息
            binding.etBlackWhiteCounter.setText(device.blackWhiteCounter?.toString() ?: "")
            binding.etColorCounter.setText(device.colorCounter?.toString() ?: "")
            binding.etStatisticsBlackWhiteCounter.setText(device.statisticsBlackWhiteCounter?.toString() ?: "")
            binding.etStatisticsColoursCounter.setText(device.statisticsColoursCounter?.toString() ?: "")
            binding.etBlackWhitePrice.setText(device.blackWhitePrice?.toString() ?: "")
            binding.etColorPrice.setText(device.colorPrice?.toString() ?: "")
            
            // 签约计数器信息
            binding.etSignBlackWhiteCounter.setText(device.signBlackWhiteCounter?.toString() ?: "")
            binding.etSignColoursCounter.setText(device.signColoursCounter?.toString() ?: "")
            
            // 时间信息
            device.statisticsStartDate?.let {
                binding.etStatisticsStartDate.setText(dateFormat.format(Date(it)))
            }
            
            device.signDate?.let {
                binding.etSignDate.setText(dateFormat.format(Date(it)))
            }
            
            // 使用lifecycleScope而不是创建新的协程，这样可以与Fragment生命周期绑定
            // 添加检查确保Fragment仍然附加到Context
            viewLifecycleOwner.lifecycleScope.launch {
                delay(300) // 确保Spinner适配器已经设置好
                if (isAdded && context != null) { // 检查Fragment是否仍然附加到Context
                    setupSpinners()
                }
            }
            
            // 其他信息
            binding.etPaperType.setText(device.paperType ?: "")
            binding.etPlaceOrigin.setText(device.placeOrigin ?: "")
            binding.etSupplyVoltage.setText(device.supplyVoltage ?: "")
            
            // 设置设备图片 - 确保安全处理设备图片
            if (::deviceImageAdapter.isInitialized) {
                // 检查设备是否有有效的图片信息
                val deviceImage = device.deviceGroupImg
                val hasValidImage = deviceImage != null && deviceImage.url != null && deviceImage.url.isNotEmpty()
                
                android.util.Log.d("CustomerDevice", "设备图片: " + 
                    "hasValidImage=$hasValidImage, " + 
                    "url=${deviceImage?.url ?: "null"}")
                
                if (hasValidImage) {
                    // 有效图片，设置到适配器
                    val imagesToSet = buildList {
                        deviceImage?.let { add(it) }
                    }
                    deviceImageAdapter.setImages(imagesToSet)
                } else {
                    // 无效图片，清空适配器
                    deviceImageAdapter.setImages(emptyList())
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomerDevice", "更新UI时出错", e)
            Toast.makeText(requireContext(), "显示设备信息时出错: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 安全地获取CustomerOption值的辅助函数
     */
    private fun safeGetCustomerOption(option: CustomerOption?, defaultValue: String = ""): CustomerOption {
        return option ?: CustomerOption(defaultValue, "")
    }

    /**
     * 提供默认值的CustomerOption值获取
     */
    private fun CustomerOption?.orDefault(defaultValue: String = ""): CustomerOption {
        return this ?: CustomerOption(defaultValue, "")
    }
    
    /**
     * 提供安全的ImageItem访问
     */
    private fun ImageItem?.orDefault(): ImageItem {
        return this ?: ImageItem(url = "", name = "默认图片")
    }

    /**
     * 安全地初始化和处理合约类型
     */
    private fun initTreatyType() {
        // 确保有默认值
        if (treatyTypeList.isEmpty()) {
            treatyTypeList = mutableListOf(
                CustomerOption("1201", "包机"), 
                CustomerOption("1202", "包印")
            )
        }
    }
    
    private fun saveDeviceInfo() {
        currentDevice?.let { device ->
            try {
                // 获取选中的选项值
                val deviceOnIndex = binding.spinnerDeviceOn.selectedItemPosition
                val deviceStatusIndex = binding.spinnerDeviceStatus.selectedItemPosition
                val fixStatusIndex = binding.spinnerFixStatus.selectedItemPosition
                val serTypeIndex = binding.spinnerSerType.selectedItemPosition
                val treatyTypeIndex = binding.spinnerTreatyType.selectedItemPosition
                val statisticsOperatNameIndex = binding.spinnerStatisticsOperatName.selectedItemPosition
                val signOperatNameIndex = binding.spinnerSignOperatName.selectedItemPosition
                val operatIdIndex = binding.spinnerOperatId.selectedItemPosition
                
                // 打印日志，帮助调试
                android.util.Log.d("CustomerDevice", "保存设备信息, " + 
                    "deviceOnIndex=$deviceOnIndex, deviceStatusIndex=$deviceStatusIndex, " +
                    "fixStatusIndex=$fixStatusIndex, serTypeIndex=$serTypeIndex, " +
                    "treatyTypeIndex=$treatyTypeIndex, " +
                    "统计操作人员索引: $statisticsOperatNameIndex, " +
                    "签约操作人员索引: $signOperatNameIndex, " +
                    "负责工程师索引: $operatIdIndex")
                
                // 解析日期
                val statisticsStartDateStr = binding.etStatisticsStartDate.text?.toString() ?: ""
                val signDateStr = binding.etSignDate.text?.toString() ?: ""
                
                val statisticsStartDate = if (statisticsStartDateStr.isNotEmpty()) {
                    try {
                        dateFormat.parse(statisticsStartDateStr)?.time
                    } catch (e: Exception) {
                        null
                    }
                } else null
                
                val signDate = if (signDateStr.isNotEmpty()) {
                    try {
                        dateFormat.parse(signDateStr)?.time
                    } catch (e: Exception) {
                        null
                    }
                } else null
                
                // 安全地获取负责工程师信息
                val operatId = if (operatIdIndex > 0 && operatIdIndex < operatIdList.size) {
                    operatIdList[operatIdIndex].value
                } else {
                    device.operatId // 保留原有值
                }
                
                // 获取负责工程师名称
                val operatName = if (operatIdIndex > 0 && operatIdIndex < operatIdList.size) {
                    operatIdList[operatIdIndex].label
                } else {
                    device.operatName // 保留原有值
                }
                
                // 安全地获取统计操作人员和签约操作人员信息
                val statisticsOperatName = if (statisticsOperatNameIndex > 0 && statisticsOperatNameIndex < statisticsOperatNameList.size) {
                    statisticsOperatNameList[statisticsOperatNameIndex].label
                } else {
                    device.statisticsOperatName // 保留原有值
                }
                
                val signOperatName = if (signOperatNameIndex > 0 && signOperatNameIndex < signOperatNameList.size) {
                    signOperatNameList[signOperatNameIndex].label
                } else {
                    device.signOperatName // 保留原有值
                }
                
                // 获取设备新旧
                val deviceOn = if (deviceOnIndex >= 0 && deviceOnIndex < deviceOnList.size) {
                    deviceOnList[deviceOnIndex]
                } else {
                    device.deviceOn.orDefault("1") // 默认为新机
                }
                
                // 获取设备状态
                val deviceStatus = if (deviceStatusIndex >= 0 && deviceStatusIndex < deviceStatusList.size) {
                    deviceStatusList[deviceStatusIndex]
                } else {
                    device.deviceStatus.orDefault("1") // 默认为正常
                }
                
                // 获取维修状态
                val fixStatus = if (fixStatusIndex >= 0 && fixStatusIndex < fixStatusList.size) {
                    fixStatusList[fixStatusIndex]
                } else {
                    device.fixStatus.orDefault("1") // 默认为正常
                }
                
                // 获取服务类型，确保使用正确的值格式
                val serType = if (serTypeIndex >= 0 && serTypeIndex < serTypeList.size) {
                    // 注意：从Vue项目分析，服务类型的值应该是字符串格式，但可能后端接受不同格式
                    // 先获取选中的服务类型选项
                    val selectedSerType = serTypeList[serTypeIndex]
                    // 后端可能期望不同的value格式，需要根据label映射到正确的后端值
                    val backendValue = getSerTypeBackendValue(selectedSerType.label)
                    CustomerOption(backendValue, selectedSerType.label)
                } else {
                    device.serType.orDefault("SCATTERED") // 默认为散修
                }
                
                // 获取合约类型
                val treatyType = if (treatyTypeIndex >= 0 && treatyTypeIndex < treatyTypeList.size) {
                    val selectedTreatyType = treatyTypeList[treatyTypeIndex]
                    // 确保label不为null
                    if (selectedTreatyType.label.isNullOrEmpty()) {
                        CustomerOption(selectedTreatyType.value, "包机") // 提供默认标签
                    } else {
                        selectedTreatyType
                    }
                } else {
                    device.treatyType.orDefault("1201") // 默认为包机
                }
                
                android.util.Log.d("CustomerDevice", "保存前数据确认: " + 
                    "serType=${serType.value}/${serType.label}, " + 
                    "treatyType=${treatyType.value}/${treatyType.label}, " +
                    "operatId=$operatId, operatName=$operatName, " + 
                    "statisticsOperatName=$statisticsOperatName, signOperatName=$signOperatName")
                
                // 获取设备组照片
                val deviceGroupImg = if (deviceImageAdapter.getImages().isNotEmpty()) {
                    // 如果有上传的图片，使用第一张
                    deviceImageAdapter.getImages().first()
                } else if (device.deviceGroupImg != null && device.deviceGroupImg.url.isNotEmpty()) {
                    // 如果没有上传新图片但有原始图片，使用原始图片
                    device.deviceGroupImg
                } else {
                    // 如果没有图片，创建一个空图片对象
                    ImageItem(url = "", name = "")
                }
                
                android.util.Log.d("CustomerDevice", "保存设备图片: url=${deviceGroupImg.url}")
                
                // 安全获取表单输入值
                val blackWhiteCounter = binding.etBlackWhiteCounter.text?.toString()?.toIntOrNull()
                val colorCounter = binding.etColorCounter.text?.toString()?.toIntOrNull()
                val statisticsBlackWhiteCounter = binding.etStatisticsBlackWhiteCounter.text?.toString()?.toIntOrNull()
                val statisticsColoursCounter = binding.etStatisticsColoursCounter.text?.toString()?.toIntOrNull()
                val blackWhitePrice = binding.etBlackWhitePrice.text?.toString()?.toDoubleOrNull()
                val colorPrice = binding.etColorPrice.text?.toString()?.toDoubleOrNull()
                val signBlackWhiteCounter = binding.etSignBlackWhiteCounter.text?.toString()?.toIntOrNull()
                val signColoursCounter = binding.etSignColoursCounter.text?.toString()?.toIntOrNull()
                val paperType = binding.etPaperType.text?.toString()?.takeIf { it.isNotEmpty() }
                val placeOrigin = binding.etPlaceOrigin.text?.toString()?.takeIf { it.isNotEmpty() }
                val supplyVoltage = binding.etSupplyVoltage.text?.toString()?.takeIf { it.isNotEmpty() }
                
                // 创建更新后的设备对象
                val updatedDevice = device.copy(
                    status = binding.switchStatus.isChecked,
                    deviceOn = deviceOn,
                    deviceStatus = deviceStatus,
                    fixStatus = fixStatus,
                    serType = serType,
                    treatyType = treatyType,
                    regCliState = binding.switchRegCliState.isChecked,
                    enableStatistics = binding.switchEnableStatistics.isChecked,
                    dataShowState = if (binding.switchDataShowState.isChecked) 1 else 0,
                    blackWhiteCounter = blackWhiteCounter,
                    colorCounter = colorCounter,
                    statisticsBlackWhiteCounter = statisticsBlackWhiteCounter,
                    statisticsColoursCounter = statisticsColoursCounter,
                    blackWhitePrice = blackWhitePrice,
                    colorPrice = colorPrice,
                    statisticsOperatName = statisticsOperatName,
                    signOperatName = signOperatName,
                    operatId = operatId,
                    operatName = operatName,
                    statisticsStartDate = statisticsStartDate ?: device.statisticsStartDate,
                    signDate = signDate ?: device.signDate,
                    signBlackWhiteCounter = signBlackWhiteCounter,
                    signColoursCounter = signColoursCounter,
                    paperType = paperType,
                    placeOrigin = placeOrigin,
                    supplyVoltage = supplyVoltage,
                    deviceGroupImg = deviceGroupImg
                )
                
                // 保存更新后的设备信息
                viewModel.updateCustomerDeviceInfo(updatedDevice) { success ->
                    if (success) {
                        // 更新成功，会在 ViewModel 中处理成功消息
                    } else {
                        // 更新失败，会在 ViewModel 中处理错误消息
                    }
                }
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "保存失败: ${e.message}", Toast.LENGTH_SHORT).show()
                android.util.Log.e("CustomerDevice", "保存设备信息失败", e)
            }
        } ?: run {
            Toast.makeText(requireContext(), "未找到设备信息", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    // 处理权限请求结果
    override fun onRequestPermissionsResult(
        requestCode: Int, 
        permissions: Array<out String>, 
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == MULTIPLE_PERMISSIONS_REQUEST) {
            // 检查是否所有权限都被授予
            if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                // 所有权限都被授予
                // 根据请求的权限判断执行什么操作
                if (permissions.contains(android.Manifest.permission.CAMERA)) {
                    openCameraWithPermissionGranted()
                } else if (permissions.contains(android.Manifest.permission.READ_EXTERNAL_STORAGE)) {
                    openGallery()
                }
            } else {
                // 权限被拒绝
                Toast.makeText(requireContext(), "需要相应权限才能完成操作", Toast.LENGTH_SHORT).show()
            }
        }
    }
} 

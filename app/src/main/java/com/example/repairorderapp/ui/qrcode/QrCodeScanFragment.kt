package com.example.repairorderapp.ui.qrcode

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.QrCodeApi
import com.example.repairorderapp.data.repository.QrCodeRepository
import com.example.repairorderapp.databinding.FragmentQrCodeScanBinding
import com.example.repairorderapp.model.qrcode.QrCodeBindStatus
import com.example.repairorderapp.model.customer.CustomerListItem
import com.example.repairorderapp.viewmodel.qrcode.QrCodeViewModel
import com.example.repairorderapp.viewmodel.qrcode.QrCodeViewModelFactory
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanIntentResult
import com.journeyapps.barcodescanner.ScanOptions

/**
 * 二维码扫描Fragment
 */
class QrCodeScanFragment : Fragment() {
    
    private var _binding: FragmentQrCodeScanBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var viewModel: QrCodeViewModel
    private var customerId: String? = null
    private var customerName: String? = null
    
    // 权限请求launcher
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startScan()
        } else {
            Toast.makeText(requireContext(), "需要相机权限才能扫描二维码", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
        }
    }
    
    // 扫码launcher
    private val barcodeLauncher = registerForActivityResult(ScanContract()) { result ->
        if (result.contents == null) {
            Toast.makeText(requireContext(), "扫描取消", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
        } else {
            // 处理扫描结果
            viewModel.handleScanResult(result.contents)
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentQrCodeScanBinding.inflate(inflater, container, false)
        
        // 获取参数
        customerId = arguments?.getString("customerId")
        customerName = arguments?.getString("customerName")
        
        // 创建ViewModel
        val qrCodeApi = ApiClient.createService(QrCodeApi::class.java)
        val qrCodeRepository = QrCodeRepository(qrCodeApi)
        val factory = QrCodeViewModelFactory(qrCodeRepository)
        viewModel = ViewModelProvider(this, factory)[QrCodeViewModel::class.java]
        
        // 设置当前客户ID
        viewModel.setCurrentCustomerId(customerId)
        
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupToolbar()
        setupButtons()
        observeViewModel()
        
        // 检查权限并开始扫描
        checkCameraPermissionAndScan()
    }
    
    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }
    }
    
    private fun setupButtons() {
        binding.btnScanAgain.setOnClickListener {
            viewModel.resetStatus()
            // 重置UI状态
            binding.layoutResult.visibility = View.GONE
            binding.layoutLoading.visibility = View.GONE
            binding.layoutDefault.visibility = View.VISIBLE
            checkCameraPermissionAndScan()
        }
        
        binding.btnBackToHome.setOnClickListener {
            findNavController().navigateUp()
        }
    }
    
    private fun observeViewModel() {
        // 观察加载状态
        viewModel.loading.observe(viewLifecycleOwner) { isLoading ->
            if (isLoading) {
                binding.layoutLoading.visibility = View.VISIBLE
                binding.layoutResult.visibility = View.GONE
                binding.layoutDefault.visibility = View.GONE
                binding.tvLoadingMessage.text = "正在处理扫描结果..."
            }
            binding.btnScanAgain.isEnabled = !isLoading
        }
        
        // 观察错误信息
        viewModel.error.observe(viewLifecycleOwner) { error ->
            if (error.isNotEmpty()) {
                showError(error)
                viewModel.clearError()
            }
        }
        
        // 观察绑定状态
        viewModel.bindStatus.observe(viewLifecycleOwner) { status ->
            when (status) {
                QrCodeBindStatus.SUCCESS -> showSuccess("授权成功")           // type=1
                QrCodeBindStatus.INVALID_QR -> showError("该二维码无效")        // type=2
                QrCodeBindStatus.SCAN_ERROR -> showError("扫描二维码出现错误")   // type=3
                QrCodeBindStatus.WRONG_QR -> showError("请扫描正确的二维码")     // type=4
                QrCodeBindStatus.SERVER_ERROR -> {
                    // type=5: 显示服务器返回的具体消息，通过error LiveData传递
                    // 错误消息会在error观察者中处理
                }
                null -> {
                    // 重置状态
                    binding.layoutResult.visibility = View.GONE
                    binding.layoutLoading.visibility = View.GONE
                    binding.layoutDefault.visibility = View.VISIBLE  // 显示默认提示
                }
            }
        }
        
        // 观察是否需要选择客户
        viewModel.needSelectCustomer.observe(viewLifecycleOwner) { needSelect ->
            if (needSelect) {
                showCustomerSelection()
            }
        }
        
        // 观察客户列表
        viewModel.customers.observe(viewLifecycleOwner) { customers ->
            if (customers.isNotEmpty()) {
                // 如果已有指定客户ID，直接绑定
                customerId?.let { id ->
                    val customer = customers.find { it.id == id }
                    if (customer != null) {
                        viewModel.selectCustomerAndBind(id)
                    } else {
                        showCustomerSelectionDialog(customers)
                    }
                } ?: run {
                    // 显示客户选择对话框
                    showCustomerSelectionDialog(customers)
                }
            }
        }
    }
    
    private fun checkCameraPermissionAndScan() {
        when {
            ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                startScan()
            }
            else -> {
                requestPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }
    
    private fun startScan() {
        val options = ScanOptions().apply {
            setDesiredBarcodeFormats(ScanOptions.QR_CODE)
            setPrompt("将二维码放入扫描框内\n扫描时请保持竖屏状态")
            setCameraId(0) // 使用后置摄像头
            setBeepEnabled(true)
            setBarcodeImageEnabled(true)
            setOrientationLocked(true) // 锁定为竖屏模式
            // 设置扫描区域为正方形，适合二维码
            // ZXing会自动处理竖屏显示
        }
        barcodeLauncher.launch(options)
    }
    
    private fun showSuccess(message: String = "绑定成功") {
        binding.layoutResult.visibility = View.VISIBLE
        binding.layoutLoading.visibility = View.GONE
        binding.layoutDefault.visibility = View.GONE  // 隐藏默认提示
        binding.ivResultIcon.setImageResource(R.drawable.ic_check_circle)
        binding.tvResultTitle.text = message
        binding.tvResultMessage.text = "设备已成功绑定到客户"
        binding.btnScanAgain.text = "继续扫码"
    }
    
    private fun showError(message: String) {
        binding.layoutResult.visibility = View.VISIBLE
        binding.layoutLoading.visibility = View.GONE
        binding.layoutDefault.visibility = View.GONE  // 隐藏默认提示
        binding.ivResultIcon.setImageResource(R.drawable.ic_error_outline)
        binding.tvResultTitle.text = "绑定失败"
        binding.tvResultMessage.text = message
        binding.btnScanAgain.text = "重新扫码"
    }
    
    private fun showCustomerSelection() {
        binding.layoutLoading.visibility = View.VISIBLE
        binding.layoutResult.visibility = View.GONE
        binding.layoutDefault.visibility = View.GONE  // 隐藏默认提示
        binding.tvLoadingMessage.text = "正在获取客户列表..."
    }
    
    private fun showCustomerSelectionDialog(customers: List<CustomerListItem>) {
        binding.layoutLoading.visibility = View.GONE
        
        val customerNames = customers.map { "${it.displayName} - ${it.address ?: "暂无地址"}" }.toTypedArray()
        
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("选择客户")
            .setItems(customerNames) { dialog, which ->
                val selectedCustomer = customers[which]
                viewModel.selectCustomerAndBind(selectedCustomer.id)
            }
            .setNegativeButton("取消") { dialog, _ ->
                findNavController().navigateUp()
            }
            .setCancelable(false)
            .show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 
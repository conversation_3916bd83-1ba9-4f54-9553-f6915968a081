package com.example.repairorderapp.ui.customer

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.repairorderapp.databinding.ItemDeviceModelSuggestionBinding
import com.example.repairorderapp.model.customer.DeviceModel

/**
 * 设备型号建议适配器
 */
class DeviceModelSuggestionsAdapter(
    private val onItemClick: (DeviceModel) -> Unit
) : ListAdapter<DeviceModel, DeviceModelSuggestionsAdapter.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemDeviceModelSuggestionBinding.inflate(
            LayoutInflater.from(parent.context), 
            parent, 
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(
        private val binding: ItemDeviceModelSuggestionBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(model: DeviceModel) {
            binding.tvDeviceModel.text = model.displayName
            
            binding.root.setOnClickListener {
                onItemClick(model)
            }
        }
    }

    private class DiffCallback : DiffUtil.ItemCallback<DeviceModel>() {
        override fun areItemsTheSame(oldItem: DeviceModel, newItem: DeviceModel): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DeviceModel, newItem: DeviceModel): Boolean {
            return oldItem == newItem
        }
    }
} 
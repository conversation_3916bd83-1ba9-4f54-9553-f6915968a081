package com.example.repairorderapp.ui.customer

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 * 客户设备详情页面的ViewPager适配器
 * 用于展示设备的不同类型信息（基本信息、维护记录、故障记录等）
 */
class CustomerDeviceDetailPagerAdapter(
    fragment: Fragment,
    private val deviceId: String?,
    private val customerId: String?
) : FragmentStateAdapter(fragment) {

    // 页面类型
    companion object {
        const val PAGE_BASIC_INFO = 0
        const val PAGE_MAINTENANCE = 1
        const val PAGE_FAULT = 2
        const val PAGE_PRINT = 3
        const val PAGE_PARTS = 4
        const val PAGE_CONTRACT = 5
        
        const val PAGE_COUNT = 6
    }

    override fun getItemCount(): Int = PAGE_COUNT

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            PAGE_BASIC_INFO -> createDeviceInfoFragment()
            PAGE_MAINTENANCE -> createRecordListFragment("maintenance")
            PAGE_FAULT -> createRecordListFragment("fault")
            PAGE_PRINT -> createRecordListFragment("print")
            PAGE_PARTS -> createRecordListFragment("parts")
            PAGE_CONTRACT -> createDeviceContractFragment()
            else -> createDeviceInfoFragment()
        }
    }
    
    /**
     * 创建设备基本信息Fragment
     */
    private fun createDeviceInfoFragment(): Fragment {
        return DeviceInfoFragment().apply {
            arguments = createBundle()
        }
    }
    
    /**
     * 创建记录列表Fragment
     */
    private fun createRecordListFragment(recordType: String): Fragment {
        return DeviceRecordListFragment().apply {
            arguments = createBundle().apply {
                putString("recordType", recordType)
            }
        }
    }
    
    /**
     * 创建设备合约信息Fragment
     */
    private fun createDeviceContractFragment(): Fragment {
        return DeviceContractFragment().apply {
            arguments = createBundle()
        }
    }
    
    /**
     * 创建包含设备ID和客户ID的Bundle
     */
    private fun createBundle() = android.os.Bundle().apply {
        putString("deviceId", deviceId)
        putString("customerId", customerId)
    }
}

/**
 * 设备基本信息Fragment
 * 展示设备的详细信息
 */
class DeviceInfoFragment : Fragment() {
    // 实现设备基本信息展示
}

/**
 * 设备记录列表Fragment
 * 展示设备的各类记录（维护、故障、印量等）
 */
class DeviceRecordListFragment : Fragment() {
    // 实现设备记录列表展示
}

/**
 * 设备合约信息Fragment
 * 展示设备的合约信息
 */
class DeviceContractFragment : Fragment() {
    // 实现设备合约信息展示
} 
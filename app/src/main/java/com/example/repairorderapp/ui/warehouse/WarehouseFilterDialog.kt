package com.example.repairorderapp.ui.warehouse

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.*
import androidx.core.content.ContextCompat
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.model.PagedData
import com.example.repairorderapp.model.warehouse.ProductItem
import com.example.repairorderapp.model.warehouse.WarehouseFilter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class WarehouseFilterDialog(
    context: Context,
    private val currentFilter: WarehouseFilter,
    private val productList: List<Pair<String, String>>,
    private val categoryList: List<Pair<String, String>>,
    private val partCategoryList: List<Pair<String, String>>,
    private val unitList: List<Pair<String, String>>,
    private val savedProductNameMap: Map<String, String>,
    private val onFilterApplied: (WarehouseFilter, String) -> Unit
) : Dialog(context) {

    private lateinit var editProduct: EditText
    private lateinit var spinnerCategory: Spinner
    private lateinit var spinnerPartCategory: Spinner
    private lateinit var spinnerUnit: Spinner
    private lateinit var editOem: EditText
    private lateinit var btnReset: Button
    private lateinit var btnConfirm: Button
    
    // 存储选中的机型ID
    private var selectedProductId: String = ""
    // 存储选中的机型名称
    private var selectedProductName: String = ""
    // 存储机型ID到名称的映射
    private val productMap = mutableMapOf<String, String>()
    
    // 自定义下拉列表
    private lateinit var productListView: ListView
    private lateinit var listPopupWindow: PopupWindow
    private lateinit var productAdapter: ArrayAdapter<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(LayoutInflater.from(context).inflate(R.layout.dialog_warehouse_filter, null))

        // 设置对话框大小和位置
        window?.apply {
            val lp = attributes
            lp.width = ViewGroup.LayoutParams.MATCH_PARENT
            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT
            attributes = lp
        }
        
        // 初始化控件
        initViews()
        
        // 初始化产品映射
        initProductMap()
        
        // 初始化自定义下拉列表
        initPopupWindow()
        
        // 设置数据适配器
        setupAdapters()
        
        // 设置初始值
        setInitialValues()
        
        // 设置按钮点击事件
        setupListeners()
    }

    private fun initViews() {
        editProduct = findViewById(R.id.autocomplete_product)
        spinnerCategory = findViewById(R.id.spinner_category)
        spinnerPartCategory = findViewById(R.id.spinner_part_category)
        spinnerUnit = findViewById(R.id.spinner_unit)
        editOem = findViewById(R.id.edit_oem)
        btnReset = findViewById(R.id.btn_reset)
        btnConfirm = findViewById(R.id.btn_confirm)
    }
    
    private fun initProductMap() {
        // 添加空选项
        productMap[""] = "全部"
        
        // 添加产品列表中的选项
        productList.forEach { (id, name) ->
            productMap[id] = name
        }
        
        // 添加已保存的机型名称映射
        savedProductNameMap.forEach { (id, name) ->
            productMap[id] = name
        }
        
        // 如果当前筛选器有选中的产品ID，但不在列表中，添加它
        if (currentFilter.productId.isNotEmpty() && !productMap.containsKey(currentFilter.productId)) {
            // 先从savedProductNameMap中查找
            val name = savedProductNameMap[currentFilter.productId]
            if (name != null) {
                productMap[currentFilter.productId] = name
            } else {
                productMap[currentFilter.productId] = "未知机型(${currentFilter.productId})"
            }
        }
    }
    
    private fun initPopupWindow() {
        // 创建ListView
        productListView = ListView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            setBackgroundColor(ContextCompat.getColor(context, android.R.color.white))
            divider = ContextCompat.getDrawable(context, R.drawable.list_divider)
            dividerHeight = 1
        }
        
        // 创建弹出窗口
        listPopupWindow = PopupWindow(
            productListView,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            false  // 设置为false，防止抢占输入框焦点
        ).apply {
            isOutsideTouchable = true
            isFocusable = false  // 不抢占焦点，让输入框保持焦点
            // 设置下拉列表的背景
            setBackgroundDrawable(ContextCompat.getDrawable(context, R.drawable.popup_background))
            // 设置弹出窗口动画
            animationStyle = android.R.style.Animation_Dialog
        }
        
        // 设置ListView项点击监听器
        productListView.setOnItemClickListener { _, _, position, _ ->
            val selectedName = productAdapter.getItem(position) as String
            selectedProductId = productMap.entries.find { it.value == selectedName }?.key ?: ""
            selectedProductName = selectedName
            
            // 先关闭下拉列表，然后再设置文本，避免触发新的搜索
            listPopupWindow.dismiss()
            
            // 设置标志，防止触发新的搜索
            val isSettingProgrammatically = true
            
            // 设置选中的文本
            editProduct.setText(selectedName)
            editProduct.setSelection(selectedName.length)
            
            // 清除焦点，防止再次弹出下拉列表
            editProduct.clearFocus()
            
            Log.d("WarehouseFilterDialog", "选择了机型: $selectedName, ID: $selectedProductId")
        }
    }

    private fun setupAdapters() {
        // 创建适配器
        productAdapter = ArrayAdapter(
            context,
            R.layout.item_dropdown,
            R.id.text_item,
            mutableListOf<String>()
        )
        
        // 设置ListView适配器
        productListView.adapter = productAdapter
        
        // 设置文本变化监听器实现实时搜索
        val textWatcher = CustomTextWatcher()
        editProduct.addTextChangedListener(textWatcher)
        
        // 保存TextWatcher引用以便后续使用
        editProduct.setTag(R.id.tag_text_watcher, textWatcher)
        
        // 物品分类适配器
        val emptyOption = Pair("", "全部")
        val categoryOptions = listOf(emptyOption) + categoryList
        val categoryAdapter = ArrayAdapter(
            context,
            android.R.layout.simple_spinner_item,
            categoryOptions.map { it.second }
        )
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerCategory.adapter = categoryAdapter

        // 零件分类适配器
        val partCategoryOptions = listOf(emptyOption) + partCategoryList
        val partCategoryAdapter = ArrayAdapter(
            context,
            android.R.layout.simple_spinner_item,
            partCategoryOptions.map { it.second }
        )
        partCategoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerPartCategory.adapter = partCategoryAdapter

        // 所属单元适配器
        val unitOptions = listOf(emptyOption) + unitList
        val unitAdapter = ArrayAdapter(
            context,
            android.R.layout.simple_spinner_item,
            unitOptions.map { it.second }
        )
        unitAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerUnit.adapter = unitAdapter
        
        // 设置输入框焦点变化监听器
        editProduct.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                val text = editProduct.text.toString().trim()
                if (text.length >= 1 && productAdapter.count > 0) {
                    // 显示下拉列表
                    showDropDown()
                }
            } else {
                // 失去焦点时收起下拉列表
                listPopupWindow.dismiss()
            }
        }
    }
    
    /**
     * 显示下拉列表
     */
    private fun showDropDown() {
        if (productAdapter.count > 0) {
            // 确保不会移走输入框焦点
            if (!editProduct.hasFocus()) {
                editProduct.requestFocus()
            }
            
            // 设置弹出窗口的宽度与输入框相同
            listPopupWindow.width = editProduct.width
            
            // 设置弹出窗口的高度，最多显示5个项
            val itemHeight = 120 // 估计的项高度，单位是像素
            val maxHeight = 5 * itemHeight
            val listHeight = productAdapter.count * itemHeight
            listPopupWindow.height = minOf(listHeight, maxHeight)
            
            // 在输入框下方显示
            listPopupWindow.showAsDropDown(editProduct)
            
            Log.d("WarehouseFilterDialog", "显示下拉列表: 宽度=${editProduct.width}, 高度=${listPopupWindow.height}, 项数=${productAdapter.count}")
        }
    }
    
    /**
     * 搜索产品
     */
    private fun searchProducts(searchText: String) {
        Log.d("WarehouseFilterDialog", "开始搜索: $searchText")
        
        ApiClient.workOrderApi.getProductList(
            name = searchText,
            pageNumber = 1,
            pageSize = 20
        ).enqueue(object : Callback<ApiResponse<PagedData<ProductItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<ProductItem>>>,
                response: Response<ApiResponse<PagedData<ProductItem>>>
            ) {
                val currentInput = editProduct.text.toString().trim()
                Log.d("WarehouseFilterDialog", "当前输入文本: $currentInput，API返回状态: ${response.isSuccessful}, 代码: ${response.body()?.code}")
                
                // 首先检查当前输入是否仍然与搜索文本匹配，避免处理过期请求
                if (currentInput != searchText) {
                    Log.d("WarehouseFilterDialog", "输入已更改，忽略旧的搜索结果")
                    return
                }
                
                // 再检查输入长度是否有效
                if (currentInput.length < 1) {
                    Log.d("WarehouseFilterDialog", "当前输入文本长度小于1，不显示下拉列表")
                    return
                }
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    val items = response.body()?.data?.rows ?: emptyList()
                    Log.d("WarehouseFilterDialog", "API返回产品数量: ${items.size}")
                    
                    // 更新产品映射
                    items.forEach { 
                        productMap[it.id] = it.fullName
                    }
                    
                    // 提取产品名称列表
                    val filteredValues = items.map { it.fullName }
                    
                    // 使用主线程更新UI但不阻塞输入
                    Handler(Looper.getMainLooper()).post {
                        try {
                            // 再次检查当前输入是否仍然匹配
                            if (editProduct.text.toString().trim() != searchText) {
                                Log.d("WarehouseFilterDialog", "输入在UI更新前已变化，取消更新")
                                return@post
                            }
                            
                            // 更新适配器数据
                            productAdapter.clear()
                            if (filteredValues.isNotEmpty()) {
                                Log.d("WarehouseFilterDialog", "更新下拉列表: ${filteredValues.size}个结果")
                                productAdapter.addAll(filteredValues)
                                productAdapter.notifyDataSetChanged()
                                
                                // 显示下拉列表但保持输入框焦点
                                showDropDown()
                            } else {
                                Log.d("WarehouseFilterDialog", "没有匹配的搜索结果")
                                productAdapter.add("没有找到匹配的机型")
                                productAdapter.notifyDataSetChanged()
                                
                                // 显示"没有找到匹配的机型"提示
                                showDropDown()
                            }
                        } catch (e: Exception) {
                            Log.e("WarehouseFilterDialog", "更新下拉列表时发生错误", e)
                        }
                    }
                } else {
                    // API请求失败
                    val errorCode = response.body()?.code ?: response.code()
                    val errorMsg = response.body()?.msg ?: response.message()
                    Log.e("WarehouseFilterDialog", "API请求失败: 错误码 $errorCode, 消息: $errorMsg")
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<ProductItem>>>, t: Throwable) {
                Log.e("WarehouseFilterDialog", "搜索请求失败", t)
                
                // 在主线程显示错误提示
                Handler(Looper.getMainLooper()).post {
                    Toast.makeText(context, "网络请求失败: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            }
        })
    }

    private fun setInitialValues() {
        // 设置已选择的机型
        selectedProductId = currentFilter.productId
        
        if (selectedProductId.isNotEmpty()) {
            // 显示当前选中的机型名称
            // 使用setText会触发TextWatcher，需要防止搜索
            val textWatcher = editProduct.getTag(R.id.tag_text_watcher) as? TextWatcher
            if (textWatcher != null && textWatcher is CustomTextWatcher) {
                textWatcher.setTextWithoutSearch(productMap[selectedProductId] ?: "")
            } else {
                editProduct.setText(productMap[selectedProductId])
            }
        }

        // 设置物品分类
        val categoryOptions = listOf(Pair("", "全部")) + categoryList
        val categoryIndex = categoryOptions.indexOfFirst { it.first == currentFilter.categoryId }
        spinnerCategory.setSelection(if (categoryIndex >= 0) categoryIndex else 0)

        // 设置零件分类
        val partCategoryOptions = listOf(Pair("", "全部")) + partCategoryList
        val partCategoryIndex = partCategoryOptions.indexOfFirst { it.first == currentFilter.partCategoryId }
        spinnerPartCategory.setSelection(if (partCategoryIndex >= 0) partCategoryIndex else 0)

        // 设置所属单元
        val unitOptions = listOf(Pair("", "全部")) + unitList
        val unitIndex = unitOptions.indexOfFirst { it.first == currentFilter.unitId }
        spinnerUnit.setSelection(if (unitIndex >= 0) unitIndex else 0)

        // 设置OEM编号
        editOem.setText(currentFilter.oemNumber)
    }

    private fun setupListeners() {
        // 重置按钮
        btnReset.setOnClickListener {
            // 使用自定义方法设置文本，防止触发搜索
            val textWatcher = editProduct.getTag(R.id.tag_text_watcher) as? TextWatcher
            if (textWatcher != null && textWatcher is CustomTextWatcher) {
                textWatcher.setTextWithoutSearch("")
            } else {
                editProduct.setText("")
            }
            
            selectedProductId = ""
            selectedProductName = ""
            spinnerCategory.setSelection(0)
            spinnerPartCategory.setSelection(0)
            spinnerUnit.setSelection(0)
            editOem.setText("")
            
            // 确保下拉列表关闭
            if (listPopupWindow.isShowing) {
                listPopupWindow.dismiss()
            }
        }

        // 确认按钮
        btnConfirm.setOnClickListener {
            val categoryOptions = listOf(Pair("", "全部")) + categoryList
            val partCategoryOptions = listOf(Pair("", "全部")) + partCategoryList
            val unitOptions = listOf(Pair("", "全部")) + unitList

            // 如果用户输入的文本不在下拉列表中，则清除选择
            val inputText = editProduct.text.toString()
            if (inputText.isNotEmpty() && !productMap.containsValue(inputText)) {
                selectedProductId = ""
                selectedProductName = ""
            } else if (inputText.isNotEmpty() && selectedProductName.isEmpty()) {
                // 用户可能手动输入了列表中存在的机型名称，但没有通过下拉列表选择
                // 尝试找到对应的ID
                val entry = productMap.entries.find { it.value == inputText }
                if (entry != null) {
                    selectedProductId = entry.key
                    selectedProductName = entry.value
                }
            }
            
            val filter = WarehouseFilter(
                productId = selectedProductId,
                categoryId = categoryOptions[spinnerCategory.selectedItemPosition].first,
                partCategoryId = partCategoryOptions[spinnerPartCategory.selectedItemPosition].first,
                unitId = unitOptions[spinnerUnit.selectedItemPosition].first,
                oemNumber = editOem.text.toString().trim()
            )

            onFilterApplied(filter, selectedProductName)
            dismiss()
        }
    }
    
    /**
     * 自定义TextWatcher，用于控制搜索行为
     */
    private inner class CustomTextWatcher : TextWatcher {
        // 标记当前是否是程序设置文本，而不是用户输入
        private var isInternalTextChange = false
        
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            // 如果是程序设置的文本，不触发搜索
            if (isInternalTextChange) {
                return
            }
            
            // 如果文本与选中项匹配，不触发新搜索
            val currentText = s?.toString()?.trim() ?: ""
            if (selectedProductId.isNotEmpty() && currentText == productMap[selectedProductId]) {
                if (listPopupWindow.isShowing) {
                    listPopupWindow.dismiss()
                }
                return
            }
            
            // 用户手动输入文本，清除选中的ID
            if (selectedProductId.isNotEmpty()) {
                selectedProductId = ""
            }
            
            // 获取当前文本，但不阻塞UI线程
            val searchText = currentText
            
            if (searchText.length >= 1) {
                // 使用Handler延迟一下调用，避免阻塞输入
                Handler(Looper.getMainLooper()).post {
                    // 再次检查输入是否已变化或选中
                    val newText = editProduct.text.toString().trim()
                    if (newText != searchText || selectedProductId.isNotEmpty()) {
                        return@post
                    }
                    
                    Log.d("WarehouseFilterDialog", "开始搜索: $searchText")
                    searchProducts(searchText)
                }
            } else if (searchText.isEmpty()) {
                // 当文本被清空时，清空下拉列表并隐藏
                productAdapter.clear()
                productAdapter.notifyDataSetChanged()
                if (listPopupWindow.isShowing) {
                    listPopupWindow.dismiss()
                }
            }
        }
        
        override fun afterTextChanged(s: Editable?) {
            // 重置内部文本更改标记
            isInternalTextChange = false
        }
        
        // 提供一个方法来设置文本而不触发搜索
        fun setTextWithoutSearch(text: String) {
            isInternalTextChange = true
            editProduct.setText(text)
            editProduct.setSelection(text.length)
        }
    }
    
    override fun dismiss() {
        if (listPopupWindow.isShowing) {
            listPopupWindow.dismiss()
        }
        super.dismiss()
    }
} 
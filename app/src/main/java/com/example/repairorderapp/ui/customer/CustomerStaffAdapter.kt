package com.example.repairorderapp.ui.customer

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.repairorderapp.R
import com.example.repairorderapp.databinding.ItemCustomerStaffBinding
import com.example.repairorderapp.model.customer.CustomerStaff

/**
 * 客户员工列表适配器
 */
class CustomerStaffAdapter(
    private val onItemClick: (CustomerStaff) -> Unit,
    private val onDeleteClick: (CustomerStaff) -> Unit
) : ListAdapter<CustomerStaff, CustomerStaffAdapter.StaffViewHolder>(StaffDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StaffViewHolder {
        val binding = ItemCustomerStaffBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return StaffViewHolder(binding)
    }

    override fun onBindViewHolder(holder: StaffViewHolder, position: Int) {
        val staff = getItem(position)
        holder.bind(staff)
    }

    inner class StaffViewHolder(private val binding: ItemCustomerStaffBinding) : RecyclerView.ViewHolder(binding.root) {
        
        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
            
            // 暂时注释删除功能
            /*
            binding.btnDelete.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onDeleteClick(getItem(position))
                }
            }
            */
        }
        
        fun bind(staff: CustomerStaff) {
            // 在原有文本前面添加标题
            binding.tvName.text = "${staff.name}"
            binding.tvPhone.text = "${staff.tel}"
            binding.tvRole.text = "${staff.role?.label ?: "未设置角色"}"
            
            // 显示账号状态
            if (staff.status == true) {
                binding.tvStatus.text = "在职"
                binding.tvStatus.setBackgroundResource(R.drawable.bg_status_tag)
                binding.tvStatus.setTextColor(ContextCompat.getColor(binding.root.context, R.color.green))
            } else {
                binding.tvStatus.text = "离职"
                binding.tvStatus.setBackgroundResource(R.drawable.bg_status_tag_red)
                binding.tvStatus.setTextColor(ContextCompat.getColor(binding.root.context, R.color.red))
            }
            
            // 删除按钮已隐藏
            binding.btnDelete.visibility = android.view.View.GONE
        }
    }

    private class StaffDiffCallback : DiffUtil.ItemCallback<CustomerStaff>() {
        override fun areItemsTheSame(oldItem: CustomerStaff, newItem: CustomerStaff): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: CustomerStaff, newItem: CustomerStaff): Boolean {
            return oldItem == newItem
        }
    }
} 
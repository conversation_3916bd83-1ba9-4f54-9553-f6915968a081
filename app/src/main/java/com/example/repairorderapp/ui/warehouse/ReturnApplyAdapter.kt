package com.example.repairorderapp.ui.warehouse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.repairorderapp.R
import com.example.repairorderapp.model.warehouse.WarehouseItem

/**
 * 退料申请物品列表适配器
 */
class ReturnApplyAdapter : ListAdapter<WarehouseItem, ReturnApplyAdapter.ViewHolder>(DiffCallback()) {

    private var onItemSelectListener: ((WarehouseItem, Boolean) -> Unit)? = null
    private var onItemQuantityChangeListener: ((WarehouseItem, Int) -> Unit)? = null
    private var onImageClickListener: ((String) -> Unit)? = null
    
    // 存储选中的物品ID，用于全局状态管理
    private val selectedItems = mutableSetOf<String>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_return_apply, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(item)
    }

    fun setOnItemSelectListener(listener: (WarehouseItem, Boolean) -> Unit) {
        this.onItemSelectListener = listener
    }

    fun setOnItemQuantityChangeListener(listener: (WarehouseItem, Int) -> Unit) {
        this.onItemQuantityChangeListener = listener
    }

    fun setOnImageClickListener(listener: (String) -> Unit) {
        this.onImageClickListener = listener
    }
    
    /**
     * 重置所有选中状态
     */
    fun resetSelections() {
        selectedItems.clear()
        notifyDataSetChanged()
    }
    
    /**
     * 移除指定ID列表中物品的选中状态
     */
    fun removeSelections(itemIds: List<String>) {
        var hasChanged = false
        for (id in itemIds) {
            if (selectedItems.contains(id)) {
                selectedItems.remove(id)
                hasChanged = true
            }
        }
        if (hasChanged) {
            notifyDataSetChanged()
        }
    }
    
    /**
     * 同步已选物品状态
     */
    fun syncSelectedItems(selectedItemIds: List<String>) {
        var hasChanged = false
        
        // 先清空当前选中状态
        if (selectedItems.isNotEmpty()) {
            selectedItems.clear()
            hasChanged = true
        }
        
        // 设置新的选中状态
        for (id in selectedItemIds) {
            selectedItems.add(id)
            hasChanged = true
        }
        
        if (hasChanged) {
            notifyDataSetChanged()
        }
    }
    
    /**
     * Getter方法，获取已选中物品的数量
     */
    fun getSelectedItemsCount(): Int {
        return selectedItems.size
    }
    
    /**
     * 检查物品是否被选中
     */
    fun isItemSelected(itemId: String): Boolean {
        return selectedItems.contains(itemId)
    }
    
    /**
     * 获取所有选中的物品ID
     */
    fun getSelectedItemIds(): Set<String> {
        return selectedItems.toSet()
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val checkbox: View = itemView.findViewById(R.id.checkbox)
        private val imageView: ImageView = itemView.findViewById(R.id.image_view)
        private val tvItemName: TextView = itemView.findViewById(R.id.tv_item_name)
        private val tvArticleCode: TextView = itemView.findViewById(R.id.tv_article_code)
        private val tvOemNumber: TextView = itemView.findViewById(R.id.tv_oem_number)
        private val tvSpecification: TextView = itemView.findViewById(R.id.tv_specification)
        private val tvPrice: TextView = itemView.findViewById(R.id.tv_price)
        private val tvAvailableQuantity: TextView = itemView.findViewById(R.id.tv_available_quantity)
        private val btnDecrease: ImageButton = itemView.findViewById(R.id.btn_decrease)
        private val tvQuantity: TextView = itemView.findViewById(R.id.tv_quantity)
        private val btnIncrease: ImageButton = itemView.findViewById(R.id.btn_increase)

        fun bind(item: WarehouseItem) {
            // 设置物品名称
            tvItemName.text = item.itemName
            
            // 设置物品编码
            val articleCode = item.articleCode ?: "未知"
            tvArticleCode.text = "物品编号: $articleCode"
            
            // 设置OEM编号
            tvOemNumber.text = "OEM编号: ${item.oemNumber}"
            
            // 设置规格 - 处理规格信息，只显示值部分
            val specText = if (item.skuInfo?.saleAttrVals != null && item.skuInfo.saleAttrVals.isNotEmpty()) {
                item.skuInfo.saleAttrVals.joinToString(", ") { it.`val` }
            } else {
                item.categoryName
            }
            tvSpecification.text = specText
            
            // 设置价格
            tvPrice.text = "￥${String.format("%.2f", item.saleUnitPrice)}"
            
            // 设置可用数量
            tvAvailableQuantity.text = "可退: ${item.returnableNum}"
            
            // 设置当前数量
            tvQuantity.text = "${item.num}"
            
            // 加载图片
            if (item.picUrl?.isNotEmpty() == true) {
                Glide.with(itemView.context)
                    .load(item.picUrl?.get(0)?.url)
                    .placeholder(R.drawable.ic_image_placeholder)
                    .error(R.drawable.ic_image_placeholder)
                    .into(imageView)
                
                // 设置图片点击事件
                imageView.setOnClickListener {
                    item.picUrl?.get(0)?.url?.let { url ->
                        onImageClickListener?.invoke(url)
                    }
                }
            } else {
                imageView.setImageResource(R.drawable.ic_image_placeholder)
            }
            
            // 设置复选框初始状态 - 每次绑定时从selectedItems集合获取当前状态
            val isSelected = selectedItems.contains(item.id)
            checkbox.isSelected = isSelected
            checkbox.setBackgroundResource(
                if (isSelected) R.drawable.checkbox_selected 
                else R.drawable.checkbox_normal
            )
            
            // 设置复选框点击事件
            checkbox.setOnClickListener {
                // 直接从集合中获取当前状态，而不是使用闭包中的isSelected变量
                val currentSelected = selectedItems.contains(item.id)
                val newCheckedState = !currentSelected
                
                if (newCheckedState) {
                    selectedItems.add(item.id)
                } else {
                    selectedItems.remove(item.id)
                }
                
                // 更新UI状态
                checkbox.isSelected = newCheckedState
                checkbox.setBackgroundResource(
                    if (newCheckedState) R.drawable.checkbox_selected 
                    else R.drawable.checkbox_normal
                )
                
                // 通知监听器
                onItemSelectListener?.invoke(item, newCheckedState)
            }
            
            // 设置数量减少按钮点击事件
            btnDecrease.setOnClickListener {
                if (item.num > 1) {
                    item.num -= 1
                    tvQuantity.text = "${item.num}"
                    onItemQuantityChangeListener?.invoke(item, item.num)
                    
                    // 根据最新数量更新按钮状态
                    updateButtonState(item)
                }
            }
            
            // 设置数量增加按钮点击事件
            btnIncrease.setOnClickListener {
                if (item.num < item.returnableNum) {
                    item.num += 1
                    tvQuantity.text = "${item.num}"
                    onItemQuantityChangeListener?.invoke(item, item.num)
                    
                    // 根据最新数量更新按钮状态
                    updateButtonState(item)
                }
            }
            
            // 根据初始数量设置按钮状态
            updateButtonState(item)
        }
        
        // 更新按钮状态
        private fun updateButtonState(item: WarehouseItem) {
            // 数量达到最大可退数量时禁用增加按钮
            btnIncrease.isEnabled = item.num < item.returnableNum
            
            // 数量为1时也能点击减少按钮（至少选择1个）
            btnDecrease.isEnabled = item.num > 1
        }
    }

    private class DiffCallback : DiffUtil.ItemCallback<WarehouseItem>() {
        override fun areItemsTheSame(oldItem: WarehouseItem, newItem: WarehouseItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: WarehouseItem, newItem: WarehouseItem): Boolean {
            return oldItem == newItem
        }
    }
} 

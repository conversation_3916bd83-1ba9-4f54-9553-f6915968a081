package com.example.repairorderapp.ui.warehouse

import android.os.Bundle
import android.os.Parcelable
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.databinding.FragmentWarehouseApplyBinding
import com.example.repairorderapp.model.PagedData
import com.example.repairorderapp.model.warehouse.CategoryItem
import com.example.repairorderapp.model.warehouse.ProductItem
import com.example.repairorderapp.model.warehouse.WarehouseFilter
import com.example.repairorderapp.model.warehouse.WarehouseItem
import com.example.repairorderapp.ui.common.FilterDialogFragment
import com.example.repairorderapp.ui.common.ImagePreviewDialogFragment
import com.example.repairorderapp.util.SharedPrefsManager
import com.example.repairorderapp.util.setDebounceClickListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 申请领料Fragment
 */
class WarehouseApplyFragment : Fragment() {
    
    private var _binding: FragmentWarehouseApplyBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var adapter: WarehouseApplyAdapter
    private var currentPage = 1
    private val pageSize = 10
    private var isLoading = false
    private var isLastPage = false
    private var isFragmentDestroyed = false
    
    // 筛选相关
    private var currentFilter = WarehouseFilter()
    private var productList = listOf<Pair<String, String>>()
    private val productNameMap = mutableMapOf<String, String>()
    private var categoryList = listOf<Pair<String, String>>()
    private var partCategoryList = listOf<Pair<String, String>>()
    private var unitList = listOf<Pair<String, String>>()
    
    // 标记分类数据是否已加载
    private var filterDataLoaded = false
    
    // 分类标识
    private var partCategoryTagId: String = ""
    private var unitTagId: String = ""
    
    // 选中的物品列表
    private val selectedItems = mutableListOf<SelectedItem>()
    
    // 是否处于查看清单模式
    private var isViewingCartMode = false
    
    // 用于判断是否已通过Fragment Result同步过状态
    private var hasReceivedFragmentResult = false
    
    // 用于控制是否需要重新加载数据
    private var needReloadData = true
    
    // 缓存上一次加载的数据，用于返回时恢复
    private var cachedItems = mutableListOf<WarehouseItem>()
    
    /**
     * 添加搜索协程作用域，便于统一管理
     */
    private var searchCoroutineScope = CoroutineScope(Dispatchers.Main + Job())
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWarehouseApplyBinding.inflate(inflater, container, false)
        isFragmentDestroyed = false
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 确保销毁前的搜索协程已取消，创建新的搜索协程作用域
        searchCoroutineScope.cancel()
        val newSearchScope = CoroutineScope(Dispatchers.Main + Job())
        searchCoroutineScope = newSearchScope
        
        setupToolbar()
        setupRecyclerView()
        setupListeners()
        setupResultListener()
        loadFilterData()
        
        // 如果有缓存数据且不需要重新加载，直接使用缓存数据
        if (!needReloadData && cachedItems.isNotEmpty()) {
            Log.d(TAG, "使用缓存的${cachedItems.size}个项目，不重新加载数据")
            
            // 确保UI可见性正确
            binding.recyclerView.visibility = View.VISIBLE
            binding.emptyView.root.visibility = View.GONE
            
            // 使用缓存数据
            adapter.submitList(cachedItems)
            
            // 同步已选状态
            syncAdapterSelections()
        } 
        // 只有在需要加载数据时才加载
        else if (needReloadData) {
            Log.d(TAG, "首次加载或需要刷新，请求网络数据")
            loadWarehouseItems()
            // 加载后将标志位设为false，避免重复加载
            needReloadData = false
        }
        
        // 初始化时显示查看清单按钮
        updateButtonText(0)
        
        // 初始状态不是查看清单模式
        isViewingCartMode = false
    }
    
    /**
     * 当Fragment恢复到前台时检查申请状态
     */
    override fun onResume() {
        super.onResume()
        
        // 确保搜索协程正常工作
        if (searchCoroutineScope.coroutineContext[Job]?.isActive != true) {
            searchCoroutineScope = CoroutineScope(Dispatchers.Main + Job())
            Log.d(TAG, "重新初始化搜索协程作用域")
            
            // 如果筛选卡片可见，重新设置文本变更监听
            if (binding.filterCard.visibility == View.VISIBLE) {
                setupFilterCard()
            }
        }
        
        // 检查申请是否成功，如果成功则清空已选物品
        if (WarehouseConfirmApplyFragment.applySuccessFlag) {
            // 清空已选列表
            selectedItems.clear()
            // 重置adapter中的选中状态
            adapter.resetSelections()
            // 更新底部按钮显示
            updateButtonText(0)
            // 重置标志
            WarehouseConfirmApplyFragment.applySuccessFlag = false
            // 重置为非查看清单模式
            isViewingCartMode = false
            // 需要重新加载数据
            needReloadData = true
            
            // 显示提示消息
            Toast.makeText(requireContext(), "申请成功，已清空选择列表", Toast.LENGTH_SHORT).show()
            
            // 由于需要刷新数据，这里立即重新加载
            refreshData()
        } else {
            // 使用一个标志来判断是否已通过Fragment Result同步过状态
            // 如果没有收到Fragment Result回调，才进行同步
            if (!hasReceivedFragmentResult) {
                Log.d(TAG, "onResume: 没有收到Fragment Result回调，尝试同步状态")
                
                // 如果有缓存数据但RecyclerView不可见，尝试恢复显示
                if (cachedItems.isNotEmpty() && binding.recyclerView.visibility != View.VISIBLE) {
                    Log.d(TAG, "onResume: 发现RecyclerView不可见但有缓存数据，恢复显示")
                    binding.recyclerView.visibility = View.VISIBLE
                    binding.emptyView.root.visibility = View.GONE
                    
                    // 重新提交数据到适配器
                    adapter.submitList(cachedItems)
                }
                
                // 只有在必要时同步已选物品状态到适配器
                syncAdapterSelections()
                
                // 从确认清单页面返回时，设置为查看清单模式
                isViewingCartMode = true
            } else {
                Log.d(TAG, "onResume: 已通过Fragment Result同步状态，跳过")
            }
            // 重置标志
            hasReceivedFragmentResult = false
        }
    }
    
    /**
     * 同步已选物品状态到适配器
     */
    private fun syncAdapterSelections() {
        if (::adapter.isInitialized) {
            // 获取所有已选物品的ID
            val selectedItemIds = selectedItems.map { it.skuId }
            
            // 添加日志
            Log.d(TAG, "同步选中状态到适配器: ${selectedItemIds.size}个物品")
            if (selectedItemIds.isNotEmpty()) {
                Log.d(TAG, "选中物品ID: ${selectedItemIds.joinToString()}")
            }
            
            // 同步到适配器
            adapter.syncSelectedItems(selectedItemIds)
            
            // 根据实际选中数量更新按钮文字
            updateButtonText(selectedItems.size)
        } else {
            Log.e(TAG, "适配器未初始化，无法同步选中状态")
        }
    }
    
    /**
     * 设置工具栏
     */
    private fun setupToolbar() {
        // 设置筛选按钮点击事件
        binding.btnFilter.setDebounceClickListener {
            toggleFilterCard()
        }
    }
    
    /**
     * 切换筛选卡片的显示状态
     */
    private fun toggleFilterCard() {
        if (binding.filterCard.visibility == View.VISIBLE) {
            binding.filterCard.visibility = View.GONE
        } else {
            if (!filterDataLoaded) {
                Toast.makeText(context, "筛选数据加载中，请稍后再试", Toast.LENGTH_SHORT).show()
                return
            }
            setupFilterCard()
            binding.filterCard.visibility = View.VISIBLE
        }
    }
    
    /**
     * 设置筛选卡片内容
     */
    private fun setupFilterCard() {
        // 只在第一次设置时初始化数据，或者搜索协程被取消时重新设置
        if (binding.autoCompleteProduct.adapter == null || searchCoroutineScope.coroutineContext[Job]?.isActive != true) {
            // 添加根布局的触摸监听，使用触摸事件代替点击事件，避免点击动画
            val rootView = binding.filterCard
            rootView.setOnTouchListener { v, event ->
                if (event.action == MotionEvent.ACTION_DOWN) {
                    // 清除所有输入框的焦点
                    clearAllFocus()
                    
                    // 隐藏键盘
                    val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                    imm.hideSoftInputFromWindow(rootView.windowToken, 0)
                }
                // 返回false，不消费事件，让子视图仍能接收点击事件
                false
            }
            
            // 适用机型
            val productAdapter = ArrayAdapter(
                requireContext(), 
                android.R.layout.simple_dropdown_item_1line,
                productList.map { it.second }
            )
            binding.autoCompleteProduct.setAdapter(productAdapter)
            
            // 设置选择监听
            binding.autoCompleteProduct.setOnItemClickListener { _, _, position, _ ->
                if (position < productList.size) {
                    val selectedProduct = productList[position]
                    currentFilter.productId = selectedProduct.first
                    productNameMap[selectedProduct.first] = selectedProduct.second
                    
                    // 显示清除按钮
                    showClearButton(binding.autoCompleteProduct)
                    
                    // 选择后收起键盘
                    val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                    imm.hideSoftInputFromWindow(binding.autoCompleteProduct.windowToken, 0)
                    
                    // 清除焦点
                    binding.autoCompleteProduct.clearFocus()
                }
            }
            
            // 移除旧的文本变更监听器，确保不会有多个监听器
            if (binding.autoCompleteProduct.tag is TextWatcher) {
                binding.autoCompleteProduct.removeTextChangedListener(binding.autoCompleteProduct.tag as TextWatcher)
            }
            
            // 添加文本变更监听器，实现实时搜索
            val textWatcher = object : TextWatcher {
                private var searchJob: Job? = null
                
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                
                override fun afterTextChanged(s: Editable?) {
                    val searchText = s?.toString()?.trim() ?: ""
                    
                    // 清除之前的搜索Job
                    searchJob?.cancel()
                    
                    // 清空产品ID，因为搜索文本已变更
                    if (searchText.isNotEmpty()) {
                        currentFilter.productId = ""
                        
                        // 显示清除按钮
                        showClearButton(binding.autoCompleteProduct)
                    } else {
                        // 隐藏清除按钮
                        binding.autoCompleteProduct.setCompoundDrawables(null, null, null, null)
                    }
                    
                    // 如果输入长度大于等于2，才开始搜索
                    if (searchText.length >= 2) {
                        // 延迟300ms再执行搜索，避免频繁请求
                        searchJob = searchCoroutineScope.launch {
                            delay(300)
                            Log.d(TAG, "开始搜索机型: $searchText")
                            searchProductModels(searchText)
                        }
                    }
                }
            }
            
            binding.autoCompleteProduct.addTextChangedListener(textWatcher)
            // 保存监听器引用，以便以后可以移除
            binding.autoCompleteProduct.tag = textWatcher
            
            // 添加焦点变化监听
            binding.autoCompleteProduct.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    // 获得焦点时，显示清除按钮（如果有文本）
                    if (binding.autoCompleteProduct.text?.isNotEmpty() == true) {
                        showClearButton(binding.autoCompleteProduct)
                    }
                    
                    // 如果有已加载的数据，直接显示下拉列表
                    if (productList.isNotEmpty()) {
                        binding.autoCompleteProduct.showDropDown()
                        Log.d(TAG, "焦点获取时显示${productList.size}个机型下拉选项")
                    }
                } else {
                    // 失去焦点时，隐藏清除按钮
                    binding.autoCompleteProduct.setCompoundDrawables(null, null, null, null)
                }
            }
            
            // 物品分类
            setupAutoCompleteAdapter(
                binding.autoCompleteCategory,
                categoryList.map { it.second },
                { position -> 
                    if (position < categoryList.size) {
                        val selectedCategory = categoryList[position]
                        if (selectedCategory.first.isEmpty()) {
                            // 选择"全部"选项，清除分类ID
                            currentFilter.categoryId = ""
                        } else {
                            currentFilter.categoryId = selectedCategory.first
                        }
                    }
                }
            )
            
            // 零件分类
            setupAutoCompleteAdapter(
                binding.autoCompletePartCategory,
                partCategoryList.map { it.second },
                { position -> 
                    if (position < partCategoryList.size) {
                        val selectedPartCategory = partCategoryList[position]
                        if (selectedPartCategory.first.isEmpty()) {
                            // 选择"全部"选项，清除零件分类ID
                            currentFilter.partCategoryId = ""
                        } else {
                            currentFilter.partCategoryId = selectedPartCategory.first
                        }
                    }
                }
            )
            
            // 所属单元
            setupAutoCompleteAdapter(
                binding.autoCompleteUnit,
                unitList.map { it.second },
                { position -> 
                    if (position < unitList.size) {
                        val selectedUnit = unitList[position]
                        if (selectedUnit.first.isEmpty()) {
                            // 选择"全部"选项，清除单元ID
                            currentFilter.unitId = ""
                        } else {
                            currentFilter.unitId = selectedUnit.first
                        }
                    }
                }
            )
            
            // 设置当前选中值
            setFilterInitialValues()
            
            // 设置按钮事件
            binding.btnReset.setDebounceClickListener {
                resetFilter()
            }
            
            binding.btnSearch.setDebounceClickListener {
                applyFilter()
            }
        }
    }
    
    /**
     * 清除所有输入框的焦点
     */
    private fun clearAllFocus() {
        binding.autoCompleteProduct.clearFocus()
        binding.autoCompleteCategory.clearFocus()
        binding.autoCompletePartCategory.clearFocus()
        binding.autoCompleteUnit.clearFocus()
        binding.inputOem.clearFocus()
    }
    
    /**
     * 显示清除按钮
     */
    private fun showClearButton(editText: android.widget.EditText) {
        val clearDrawable = ContextCompat.getDrawable(
            requireContext(),
            android.R.drawable.ic_menu_close_clear_cancel
        )
        clearDrawable?.setBounds(0, 0, clearDrawable.intrinsicWidth/2, clearDrawable.intrinsicHeight/2)
        
        editText.setCompoundDrawables(null, null, clearDrawable, null)
        
        // 设置点击监听
        editText.setOnTouchListener { v, event ->
            if (event.action == android.view.MotionEvent.ACTION_UP) {
                if (event.rawX >= (editText.right - editText.compoundDrawables[2].bounds.width() - editText.paddingEnd)) {
                    // 点击了清除按钮
                    editText.text?.clear()
                    // 隐藏清除按钮
                    editText.setCompoundDrawables(null, null, null, null)
                    // 清空选中的产品ID
                    currentFilter.productId = ""
                    return@setOnTouchListener true
                }
            }
            return@setOnTouchListener false
        }
    }
    
    /**
     * 搜索机型模型
     */
    private fun searchProductModels(searchText: String) {
        if (searchText.isEmpty()) return
        
        // 安全检查
        if (!isAdded || isFragmentDestroyed) return
        
        // 添加详细日志
        Log.d(TAG, "执行机型搜索请求: $searchText")
        
        // 显示加载状态 - 添加一个小菊花指示器
        val progressIndicator = binding.autoCompleteProduct.compoundDrawables[2]  // TRAILING 位置的drawable
        if (progressIndicator == null) {
            val loadingDrawable = ContextCompat.getDrawable(
                requireContext(), 
                android.R.drawable.progress_indeterminate_horizontal
            )
            loadingDrawable?.setBounds(0, 0, 24, 24)
            binding.autoCompleteProduct.setCompoundDrawables(
                null, null, loadingDrawable, null
            )
        }
        
        // 调用API搜索机型
        ApiClient.workOrderApi.getProductList(
            name = searchText,
            pageNumber = 1,
            pageSize = 10 // 减少数量，提高响应速度
        ).enqueue(object : Callback<ApiResponse<PagedData<ProductItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<ProductItem>>>,
                response: Response<ApiResponse<PagedData<ProductItem>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                // 清除加载指示器
                binding.autoCompleteProduct.setCompoundDrawables(null, null, null, null)
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    Log.d(TAG, "机型搜索请求成功，开始处理结果")
                    response.body()?.data?.rows?.map { 
                        Pair(it.id, it.fullName)
                    }?.let { results ->
                        Log.d(TAG, "获取到 ${results.size} 个机型结果")
                        
                        // 更新机型数据
                        results.forEach { pair ->
                            productNameMap[pair.first] = pair.second
                        }
                        
                        // 重要：更新搜索结果列表
                        productList = results
                        
                        // 更新适配器数据
                        val adapter = ArrayAdapter(
                            requireContext(),
                            android.R.layout.simple_dropdown_item_1line,
                            productList.map { it.second }
                        )
                        binding.autoCompleteProduct.setAdapter(adapter)
                        
                        // 重要：重新设置选择监听器，确保搜索结果中的选择也能正确处理
                        binding.autoCompleteProduct.setOnItemClickListener { _, _, position, _ ->
                            if (position < productList.size) {
                                val selectedProduct = productList[position]
                                currentFilter.productId = selectedProduct.first
                                productNameMap[selectedProduct.first] = selectedProduct.second
                                
                                // 显示清除按钮
                                showClearButton(binding.autoCompleteProduct)
                                
                                // 选择后收起键盘
                                val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                                imm.hideSoftInputFromWindow(binding.autoCompleteProduct.windowToken, 0)
                                
                                // 清除焦点
                                binding.autoCompleteProduct.clearFocus()
                            }
                        }
                        
                        // 显示下拉列表 - 如果结果非空并且文本框仍在焦点中
                        if (results.isNotEmpty() && binding.autoCompleteProduct.hasFocus()) {
                            binding.autoCompleteProduct.showDropDown()
                            Log.d(TAG, "显示机型下拉列表")
                        }
                        
                        // 如果没有结果，显示提示
                        if (results.isEmpty() && binding.autoCompleteProduct.hasFocus()) {
                            val noResultAdapter = ArrayAdapter(
                                requireContext(),
                                android.R.layout.simple_dropdown_item_1line,
                                listOf("没有找到匹配的机型")
                            )
                            binding.autoCompleteProduct.setAdapter(noResultAdapter)
                            binding.autoCompleteProduct.showDropDown()
                            Log.d(TAG, "没有找到匹配的机型")
                        }
                    }
                } else {
                    // 处理API错误
                    Log.e(TAG, "搜索机型API错误: ${response.code()}")
                    
                    // 显示错误提示
                    if (binding.autoCompleteProduct.hasFocus()) {
                        val errorAdapter = ArrayAdapter(
                            requireContext(),
                            android.R.layout.simple_dropdown_item_1line,
                            listOf("加载数据失败，请重试")
                        )
                        binding.autoCompleteProduct.setAdapter(errorAdapter)
                        binding.autoCompleteProduct.showDropDown()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<ProductItem>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                // 清除加载指示器
                binding.autoCompleteProduct.setCompoundDrawables(null, null, null, null)
                
                // 记录错误
                Log.e(TAG, "搜索机型失败: ${t.message}")
                
                // 显示错误提示
                if (binding.autoCompleteProduct.hasFocus()) {
                    val errorAdapter = ArrayAdapter(
                        requireContext(),
                        android.R.layout.simple_dropdown_item_1line,
                        listOf("网络错误，请检查网络连接")
                    )
                    binding.autoCompleteProduct.setAdapter(errorAdapter)
                    binding.autoCompleteProduct.showDropDown()
                }
            }
        })
    }
    
    /**
     * 设置AutoCompleteTextView的适配器
     */
    private fun setupAutoCompleteAdapter(
        autoCompleteTextView: AutoCompleteTextView,
        items: List<String>,
        onItemSelected: (Int) -> Unit
    ) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            items
        )
        autoCompleteTextView.setAdapter(adapter)
        autoCompleteTextView.setOnItemClickListener { _, _, position, _ ->
            onItemSelected(position)
        }
    }
    
    /**
     * 设置筛选初始值
     */
    private fun setFilterInitialValues() {
        // 适用机型
        if (currentFilter.productId.isNotEmpty()) {
            val productName = productNameMap[currentFilter.productId] ?: ""
            if (productName.isNotEmpty()) {
                binding.autoCompleteProduct.setText(productName, false)
            } else {
                // 如果没有找到产品名称，说明可能数据有问题，清除选择
                binding.autoCompleteProduct.text?.clear()
                currentFilter.productId = ""
            }
        } else {
            binding.autoCompleteProduct.text?.clear()
        }
        
        // 物品分类
        setAutoCompleteInitialValue(
            binding.autoCompleteCategory,
            categoryList,
            currentFilter.categoryId
        )
        
        // 零件分类
        setAutoCompleteInitialValue(
            binding.autoCompletePartCategory,
            partCategoryList,
            currentFilter.partCategoryId
        )
        
        // 所属单元
        setAutoCompleteInitialValue(
            binding.autoCompleteUnit,
            unitList,
            currentFilter.unitId
        )
        
        // OEM编号
        binding.inputOem.setText(currentFilter.oemNumber)
    }
    
    /**
     * 设置AutoCompleteTextView的初始值
     */
    private fun setAutoCompleteInitialValue(
        autoCompleteTextView: AutoCompleteTextView,
        items: List<Pair<String, String>>,
        selectedId: String
    ) {
        if (selectedId.isNotEmpty()) {
            val selectedItem = items.find { it.first == selectedId }
            if (selectedItem != null) {
                autoCompleteTextView.setText(selectedItem.second, false)
            } else {
                // 如果找不到对应的选项，设置为"全部"
                autoCompleteTextView.setText("全部", false)
            }
        } else {
            // 没有选择时，默认显示"全部"
            autoCompleteTextView.setText("全部", false)
        }
    }
    
    /**
     * 重置筛选条件
     */
    private fun resetFilter() {
        // 清空所有筛选条件
        currentFilter = WarehouseFilter()
        currentFilter.setTagIds(partCategoryTagId, unitTagId)
        
        // 重置UI
        binding.autoCompleteProduct.text.clear()
        binding.autoCompleteCategory.setText("全部", false)
        binding.autoCompletePartCategory.setText("全部", false)
        binding.autoCompleteUnit.setText("全部", false)
        binding.inputOem.text?.clear()
    }
    
    /**
     * 应用筛选条件
     */
    private fun applyFilter() {
        // 获取OEM编号
        currentFilter.oemNumber = binding.inputOem.text?.toString()?.trim() ?: ""
        
        // 获取机型ID - 如果还没有选择机型但有输入文本，尝试匹配
        val productText = binding.autoCompleteProduct.text.toString().trim()
        if (productText.isNotEmpty() && currentFilter.productId.isEmpty()) {
            // 尝试精确匹配产品名称
            val matchedProduct = productList.find { it.second.equals(productText, ignoreCase = true) }
            if (matchedProduct != null) {
                // 找到精确匹配
                currentFilter.productId = matchedProduct.first
                productNameMap[matchedProduct.first] = matchedProduct.second
            } else {
                // 如果没有精确匹配，可以选择保留文本作为通用搜索条件
                // 在这种情况下，我们可以清空产品ID，后端可能有自己的文本搜索逻辑
                currentFilter.productId = ""
                
                // 或者提示用户选择有效的机型
                Toast.makeText(requireContext(), "请从下拉列表中选择有效的机型", Toast.LENGTH_SHORT).show()
                return // 不关闭筛选卡片，让用户重新选择
            }
        }
        
        // 更新当前筛选条件的tagId
        currentFilter.setTagIds(partCategoryTagId, unitTagId)
        
        // 隐藏筛选卡片
        binding.filterCard.visibility = View.GONE
        
        // 清除所有输入框的焦点
        binding.autoCompleteProduct.clearFocus()
        binding.autoCompleteCategory.clearFocus()
        binding.autoCompletePartCategory.clearFocus()
        binding.autoCompleteUnit.clearFocus()
        binding.inputOem.clearFocus()
        
        // 隐藏键盘
        val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
        imm.hideSoftInputFromWindow(binding.filterCard.windowToken, 0)
        
        // 刷新数据
        refreshData()
    }
    
    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = WarehouseApplyAdapter()
        
        // 设置图片点击监听
        adapter.setOnImageClickListener { imageUrl ->
            showImagePreview(imageUrl)
        }
        
        // 设置选中项监听
        adapter.setOnItemSelectListener { item, isChecked, quantity ->
            handleItemSelection(item, isChecked, quantity)
        }
        
        // 设置数量变更监听
        adapter.setOnQuantityChangeListener { item, newQuantity ->
            updateSelectedItemQuantity(item, newQuantity)
        }
        
        binding.recyclerView.adapter = adapter
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // 添加滚动监听器实现分页加载
        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val visibleItemCount = layoutManager.childCount
                val totalItemCount = layoutManager.itemCount
                val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()
                
                if (!isLoading && !isLastPage) {
                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount
                        && firstVisibleItemPosition >= 0
                        && totalItemCount >= pageSize) {
                        loadMoreItems()
                    }
                }
            }
        })
    }
    
    /**
     * 加载更多数据
     */
    private fun loadMoreItems() {
        isLoading = true
        currentPage++
        loadWarehouseItems()
    }
    
    /**
     * 处理物品选择
     */
    private fun handleItemSelection(item: WarehouseItem, isChecked: Boolean, quantity: Int) {
        if (isChecked) {
            // 添加到已选列表
            val existingItem = selectedItems.find { it.skuId == item.id }
            if (existingItem == null) {
                // 处理图片URL
                val picUrl = when {
                    // 如果picUrl是List类型且不为空
                    item.picUrl != null && item.picUrl!!.isNotEmpty() -> {
                        item.picUrl!!.firstOrNull()?.url
                    }
                    
                    // 如果SkuInfo中有图片，使用它的URL
                    item.skuInfo?.picUrl != null && item.skuInfo.picUrl.isNotEmpty() -> {
                        item.skuInfo.picUrl.firstOrNull()?.url
                    }
                    
                    // 如果article中有图片，使用它的URL
                    item.article?.imageFiles != null && item.article.imageFiles.isNotEmpty() -> {
                        item.article.imageFiles.firstOrNull()?.url
                    }
                    
                    else -> null
                }
                
                // 提取规格属性
                val specification = if (item.skuInfo?.saleAttrVals != null && item.skuInfo.saleAttrVals.isNotEmpty()) {
                    val attrValues = item.skuInfo.saleAttrVals
                    attrValues.joinToString(", ") { "${it.`val`}" }
                } else {
                    null
                }
                
                selectedItems.add(
                    SelectedItem(
                        skuId = item.id,
                        quantity = quantity,
                        itemName = item.itemName,
                        oemNumber = item.oemNumber,
                        categoryName = item.categoryName,
                        unitPrice = item.saleUnitPrice,
                        picUrl = picUrl,
                        articleCode = item.articleCode,
                        specification = specification
                    )
                )
                
                // 用户选中新物品，切换到添加模式
                isViewingCartMode = false
            }
        } else {
            // 从已选列表移除
            selectedItems.removeAll { it.skuId == item.id }
        }
        
        // 更新底部按钮显示的数量
        if (isViewingCartMode) {
            // 如果处于查看清单模式，将按钮更新为最近从确认页面获取的数量
            val currentCount = binding.btnApply.text.toString().let {
                val regex = "\\((\\d+)\\)".toRegex()
                val matchResult = regex.find(it)
                matchResult?.groupValues?.get(1)?.toIntOrNull() ?: 0
            }
            updateButtonText(currentCount)
        } else {
            // 否则显示当前已选物品的数量
            updateButtonText(0)
        }
    }
    
    /**
     * 更新已选物品的数量
     */
    private fun updateSelectedItemQuantity(item: WarehouseItem, newQuantity: Int) {
        val existingItem = selectedItems.find { it.skuId == item.id }
        if (existingItem != null) {
            existingItem.quantity = newQuantity
        }
        
        // 更新底部按钮显示的数量
        updateButtonText(0)
    }
    
    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 设置下拉刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            refreshData()
        }
        
        // 设置申请记录按钮点击事件
        binding.btnApplyHistory.setDebounceClickListener {
            navigateToApplyHistory()
        }
        
        // 设置确认申请按钮点击事件
        binding.btnApply.setDebounceClickListener(1500) {
            submitApply()
        }
    }
    
    /**
     * 设置Fragment结果监听器
     * 用于处理从确认页面返回的数据
     */
    private fun setupResultListener() {
        setupDeleteResultListener()
    }
    
    /**
     * 处理物品删除和选中状态同步
     */
    private fun setupDeleteResultListener() {
        // 注册结果监听器
        parentFragmentManager.setFragmentResultListener("warehouse_confirm_result", this) { _, bundle ->
            // 标记已接收到Fragment Result回调
            hasReceivedFragmentResult = true
            
            // 标记不需要重新加载数据，从确认页面返回只需要更新UI状态
            needReloadData = false
            
            // 获取删除的物品ID列表
            val deletedItemIds = bundle.getStringArrayList(WarehouseConfirmApplyFragment.DELETED_ITEMS_RESULT_KEY)
            
            // 添加日志，便于调试
            Log.d(TAG, "收到确认页面返回数据: 删除ID数量=${deletedItemIds?.size ?: 0}")
            
            // 处理删除的物品ID列表
            if (!deletedItemIds.isNullOrEmpty()) {
                // 打印删除的物品ID，便于调试
                Log.d(TAG, "需要删除的物品ID: ${deletedItemIds.joinToString()}")
                
                // 从已选列表中移除被删除的物品
                val iterator = selectedItems.iterator()
                var removedCount = 0
                while (iterator.hasNext()) {
                    val item = iterator.next()
                    if (deletedItemIds.contains(item.skuId)) {
                        Log.d(TAG, "从已选列表中移除物品: ${item.itemName} (ID=${item.skuId})")
                        iterator.remove()
                        removedCount++
                    }
                }
                
                Log.d(TAG, "已从selectedItems中移除${removedCount}个物品, 剩余${selectedItems.size}个")
                
                // 更新适配器中的选中状态 - 确保适配器正确处理
                if (::adapter.isInitialized) {
                    adapter.removeSelections(deletedItemIds)
                    Log.d(TAG, "已更新适配器选中状态")
                } else {
                    Log.e(TAG, "适配器未初始化，无法更新选中状态")
                }
            }
            
            // 获取当前清单中的物品数量
            val currentItemsCount = bundle.getInt(WarehouseConfirmApplyFragment.CURRENT_ITEMS_COUNT_KEY, 0)
            Log.d(TAG, "当前清单中物品数量: $currentItemsCount")
            
            // 设置为查看清单模式
            isViewingCartMode = selectedItems.isNotEmpty()
            
            // 根据是否有选中的物品和清单中的物品数量来更新按钮文字
            updateButtonText(currentItemsCount)
            
            // 同步已选物品状态到适配器 - 直接调用一次，避免onResume中重复调用
            if (::adapter.isInitialized) {
                // 确保RecyclerView和EmptyView的可见性正确
                if (cachedItems.isEmpty()) {
                    // 如果缓存为空但适配器已初始化，说明可能有问题，尝试恢复列表显示
                    binding.recyclerView.visibility = View.VISIBLE
                    binding.emptyView.root.visibility = View.GONE
                    
                    Log.d(TAG, "缓存数据为空，但适配器已初始化，尝试恢复显示")
                } else {
                    // 使用缓存的数据恢复列表
                    Log.d(TAG, "使用缓存的${cachedItems.size}个项目恢复列表显示")
                    
                    // 确保UI可见性正确
                    binding.recyclerView.visibility = View.VISIBLE
                    binding.emptyView.root.visibility = View.GONE
                    
                    // 重新提交列表数据，刷新显示
                    adapter.submitList(cachedItems)
                }
                
                // 延迟执行，确保状态已更新
                view?.post {
                    syncAdapterSelections()
                }
            } else {
                Log.e(TAG, "适配器未初始化，无法同步选中状态")
            }
        }
    }
    
    /**
     * 根据选中物品和清单中物品数量更新按钮文字
     */
    private fun updateButtonText(confirmListCount: Int) {
        // 计算选中物品的种类数量，而不是总申请数量
        val selectedItemTypesCount = selectedItems.size
        
        if (isViewingCartMode) {
            // 如果是查看清单模式，始终显示查看清单
            val countToShow = if (confirmListCount > 0) confirmListCount else selectedItemTypesCount
            binding.btnApply.text = "查看清单(${countToShow})"
            binding.btnApply.isEnabled = countToShow > 0
        } else {
            // 根据是否有选中的物品来决定按钮文字显示
            if (selectedItems.isEmpty()) {
                // 未选中物品时，显示"查看清单"
                binding.btnApply.text = "查看清单(0)"
                binding.btnApply.isEnabled = false
            } else {
                // 已选中物品时，显示"加入清单"
                binding.btnApply.text = "加入清单(${selectedItemTypesCount})"
                binding.btnApply.isEnabled = true
            }
        }
    }
    
    /**
     * 刷新数据
     */
    private fun refreshData() {
        currentPage = 1
        isLastPage = false
        loadWarehouseItems()
    }
    
    /**
     * 加载筛选数据
     */
    private fun loadFilterData() {
        if (filterDataLoaded) return
        
        // 安全检查
        if (!isAdded || isFragmentDestroyed) return
        
        // 1. 加载物品分类数据
        ApiClient.workOrderApi.getCategoryList("1", "9999").enqueue(object : Callback<ApiResponse<PagedData<CategoryItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<CategoryItem>>>,
                response: Response<ApiResponse<PagedData<CategoryItem>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.rows?.map { 
                        Pair(it.id, it.name)
                    }?.let {
                        // 添加"全部"选项作为第一个选项
                        val allOption = Pair("", "全部")
                        categoryList = listOf(allOption) + it
                        updateFilterLoadStatus()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<CategoryItem>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                Log.e(TAG, "加载分类数据失败: ${t.message}")
            }
        })
        
        // 2. 预先加载一些热门机型数据
        loadProductModels("", 1, 50)
        
        // 3. 加载零件分类和所属单元数据
        ApiClient.workOrderApi.getWarehouseFilterData().enqueue(object : Callback<ApiResponse<Map<String, Any>>> {
            override fun onResponse(
                call: Call<ApiResponse<Map<String, Any>>>,
                response: Response<ApiResponse<Map<String, Any>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.let { data ->
                        // 解析标签数据（零件分类和所属单元）
                        val tagsList = data["tagList"] as? List<Map<String, Any>>
                        tagsList?.forEach { tag ->
                            when (tag["name"] as? String) {
                                "零件分类" -> {
                                    val tagId = tag["id"] as? String ?: ""
                                    val values = tag["value"] as? List<String>
                                    
                                    values?.map { 
                                        Pair(it, it)
                                    }?.let {
                                        // 添加"全部"选项作为第一个选项
                                        val allOption = Pair("", "全部")
                                        partCategoryList = listOf(allOption) + it
                                        
                                        // 保存tagId，用于API请求
                                        partCategoryTagId = tagId
                                    }
                                }
                                "所属单元" -> {
                                    val tagId = tag["id"] as? String ?: ""
                                    val values = tag["value"] as? List<String>
                                    
                                    values?.map { 
                                        Pair(it, it)
                                    }?.let {
                                        // 添加"全部"选项作为第一个选项
                                        val allOption = Pair("", "全部")
                                        unitList = listOf(allOption) + it
                                        
                                        // 保存tagId，用于API请求
                                        unitTagId = tagId
                                    }
                                }
                            }
                        }
                        
                        updateFilterLoadStatus()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Map<String, Any>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                Log.e(TAG, "加载筛选数据失败: ${t.message}")
            }
        })
    }
    
    /**
     * 加载机型数据
     */
    private fun loadProductModels(searchText: String, page: Int, pageSize: Int) {
        // 安全检查
        if (!isAdded || isFragmentDestroyed) return
        
        ApiClient.workOrderApi.getProductList(
            name = searchText,
            pageNumber = page,
            pageSize = pageSize
        ).enqueue(object : Callback<ApiResponse<PagedData<ProductItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<ProductItem>>>,
                response: Response<ApiResponse<PagedData<ProductItem>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.rows?.map { 
                        Pair(it.id, it.fullName)
                    }?.let {
                        // 更新机型名称映射
                        it.forEach { pair ->
                            productNameMap[pair.first] = pair.second
                        }
                        
                        // 如果是搜索结果，替换整个列表；否则只添加新条目
                        productList = if (searchText.isNotEmpty()) {
                            it
                        } else {
                            (productList + it).distinctBy { pair -> pair.first }
                        }
                        updateFilterLoadStatus()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<ProductItem>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                Log.e(TAG, "加载机型数据失败: ${t.message}")
            }
        })
    }
    
    /**
     * 更新筛选数据加载状态
     */
    private fun updateFilterLoadStatus() {
        // 当所有数据都加载完成后，标记为已加载
        if (categoryList.isNotEmpty() && (partCategoryList.isNotEmpty() || unitList.isNotEmpty())) {
            filterDataLoaded = true
        }
    }
    
    /**
     * 加载仓库耗材列表
     */
    private fun loadWarehouseItems() {
        if (!isAdded || isFragmentDestroyed) return
        
        isLoading = true
        binding.swipeRefreshLayout.isRefreshing = true
        
        // 创建基本参数
        val params = mutableMapOf<String, Any>(
            "pageNumber" to currentPage,
            "pageSize" to pageSize
        )
        
        // 添加筛选参数
        if (!currentFilter.isEmpty()) {
            with(currentFilter) {
                if (productId.isNotEmpty()) params["productTreeIdList"] = listOf(productId)
                if (categoryId.isNotEmpty()) params["categoryId"] = categoryId
                if (oemNumber.isNotEmpty()) params["oem"] = oemNumber
                
                // 添加标签筛选
                val tags = mutableListOf<Map<String, Any>>()
                if (partCategoryId.isNotEmpty() && partCategoryTagId.isNotEmpty()) {
                    tags.add(mapOf(
                        "tagId" to partCategoryTagId,
                        "values" to listOf(partCategoryId)
                    ))
                }
                if (unitId.isNotEmpty() && unitTagId.isNotEmpty()) {
                    tags.add(mapOf(
                        "tagId" to unitTagId,
                        "values" to listOf(unitId)
                    ))
                }
                if (tags.isNotEmpty()) params["tags"] = tags
            }
        }
        
        // 调用API获取耗材列表
        ApiClient.workOrderApi.getWarehouseItems(params).enqueue(object : Callback<ApiResponse<PagedData<WarehouseItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<WarehouseItem>>>,
                response: Response<ApiResponse<PagedData<WarehouseItem>>>
            ) {
                if (!isAdded || isFragmentDestroyed) return
                
                isLoading = false
                binding.swipeRefreshLayout.isRefreshing = false
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.let { pagedData ->
                        // 处理分页数据
                        val rawItems = pagedData.rows ?: emptyList()
                        
                        // 更新是否为最后一页
                        isLastPage = currentPage * pageSize >= pagedData.total
                        
                        if (rawItems.isNotEmpty()) {
                            binding.recyclerView.visibility = View.VISIBLE
                            binding.emptyView.root.visibility = View.GONE
                            
                            // 处理多SKU商品，将其拆分为单个SKU条目
                            val processedItems = processItemsToSingleSku(rawItems)
                            
                            // 更新缓存数据
                            if (currentPage == 1) {
                                cachedItems.clear()
                                cachedItems.addAll(processedItems)
                            } else {
                                cachedItems.addAll(processedItems)
                            }
                            
                            // 处理列表数据
                            if (currentPage == 1) {
                                adapter.submitList(processedItems)
                                
                                // 页面更新后，同步已选状态
                                syncAdapterSelections()
                            } else {
                                adapter.addItems(processedItems)
                                
                                // 添加新数据后，同步已选状态
                                syncAdapterSelections()
                            }
                        } else {
                            if (currentPage == 1) {
                                binding.recyclerView.visibility = View.GONE
                                binding.emptyView.root.visibility = View.VISIBLE
                                
                                // 清空缓存数据
                                cachedItems.clear()
                            }
                        }
                    }
                } else {
                    // 处理错误
                    if (currentPage == 1) {
                        binding.recyclerView.visibility = View.GONE
                        binding.emptyView.root.visibility = View.VISIBLE
                        
                        // 保留缓存数据，以便在错误情况下仍能恢复显示
                    }
                    
                    Toast.makeText(
                        requireContext(),
                        "加载失败: ${response.errorBody()?.string() ?: "未知错误"}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<WarehouseItem>>>, t: Throwable) {
                if (!isAdded || isFragmentDestroyed) return
                
                isLoading = false
                binding.swipeRefreshLayout.isRefreshing = false
                
                // 处理网络错误
                if (currentPage == 1) {
                    // 如果有缓存数据，恢复显示
                    if (cachedItems.isNotEmpty()) {
                        binding.recyclerView.visibility = View.VISIBLE
                        binding.emptyView.root.visibility = View.GONE
                        
                        // 使用缓存数据
                        adapter.submitList(cachedItems)
                        
                        // 同步已选状态
                        syncAdapterSelections()
                        
                        Toast.makeText(
                            requireContext(),
                            "加载失败，显示缓存数据",
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        binding.recyclerView.visibility = View.GONE
                        binding.emptyView.root.visibility = View.VISIBLE
                        
                        Toast.makeText(
                            requireContext(),
                            "网络错误: ${t.message}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                } else {
                    Toast.makeText(
                        requireContext(),
                        "加载更多数据失败: ${t.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        })
    }
    
    /**
     * 处理多SKU商品，将其拆分为单个SKU展示
     */
    private fun processItemsToSingleSku(items: List<WarehouseItem>): List<WarehouseItem> {
        val resultList = mutableListOf<WarehouseItem>()
        
        items.forEach { item ->
            val skuList = item.skuList
            
            if (skuList.isNullOrEmpty()) {
                // 单SKU商品直接添加
                resultList.add(item)
            } else {
                if (skuList.size == 1) {
                    // 只有一个SKU的情况，直接添加但确保ID正确
                    val sku = skuList[0]
                    
                    // 创建SkuInfo对象保存属性值
                    val skuInfo = if (!sku.saleAttrVals.isNullOrEmpty()) {
                        com.example.repairorderapp.model.warehouse.SkuInfo(
                            saleAttrVals = sku.saleAttrVals,
                            picUrl = sku.skuPicUrl,
                            invSkuId = sku.invSkuId
                        )
                    } else {
                        item.skuInfo
                    }
                    
                    val newItem = item.copy(
                        id = "${item.id}_${sku.id}", // 创建唯一ID
                        itemName = sku.invSkuName ?: item.itemName, // 优先使用invSkuName
                        oemNumber = sku.oemNumber ?: item.oemNumber, // 使用SKU的oemNumber
                        articleCode = sku.articleCode ?: "", // 使用SKU的articleCode
                        availableNum = sku.availableNum, // 确保使用正确的可用数量
                        saleUnitPrice = sku.saleUnitPrice?.toDoubleOrNull() ?: item.saleUnitPrice, // 使用SKU的价格
                        skuInfo = skuInfo // 设置SKU信息，包含属性值
                    )
                    
                    // 如果SKU有独立图片，使用SKU的图片
                    if (!sku.skuPicUrl.isNullOrEmpty()) {
                        newItem.picUrl = sku.skuPicUrl
                    }
                    
                    resultList.add(newItem)
                } else {
                    // 多SKU商品需要拆分
                    skuList.forEach { sku ->
                        try {
                            // 创建新的商品名称 - 只使用invSkuName，不再附加属性值
                            val newName = sku.invSkuName ?: item.itemName
                            
                            // 创建SkuInfo对象保存属性值
                            val skuInfo = if (!sku.saleAttrVals.isNullOrEmpty()) {
                                com.example.repairorderapp.model.warehouse.SkuInfo(
                                    saleAttrVals = sku.saleAttrVals,
                                    picUrl = sku.skuPicUrl,
                                    invSkuId = sku.invSkuId
                                )
                            } else {
                                item.skuInfo
                            }
                            
                            // 创建新的WarehouseItem对象
                            val newItem = item.copy(
                                id = "${item.id}_${sku.id}", // 创建唯一ID
                                itemName = newName, // 商品名称使用invSkuName
                                oemNumber = sku.oemNumber ?: item.oemNumber, // 使用SKU的oemNumber
                                availableNum = sku.availableNum, // 使用SKU的可用数量
                                articleCode = sku.articleCode ?: "", // 使用SKU的articleCode
                                saleUnitPrice = sku.saleUnitPrice?.toDoubleOrNull() ?: item.saleUnitPrice, // 使用SKU的价格
                                skuInfo = skuInfo // 设置SKU信息，包含属性值
                            )
                            
                            // 如果SKU有独立图片，使用SKU的图片
                            if (!sku.skuPicUrl.isNullOrEmpty()) {
                                newItem.picUrl = sku.skuPicUrl
                            }
                            
                            resultList.add(newItem)
                            
                        } catch (e: Exception) {
                            Log.e(TAG, "处理SKU时出错: ${e.message}", e)
                            // 出错时，将原始item添加到列表
                            if (!resultList.contains(item)) {
                                resultList.add(item)
                            }
                        }
                    }
                }
            }
        }
        
        return resultList
    }
    
    /**
     * 导航到申请历史页面
     */
    private fun navigateToApplyHistory() {
        try {
            findNavController().navigate(R.id.action_warehouseApplyFragment_to_warehouseApplyHistoryFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到申请记录失败: ${e.message}")
            Toast.makeText(requireContext(), "页面跳转失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 提交申请
     */
    private fun submitApply() {
        // 检查是否是查看清单模式
        if (isViewingCartMode) {
            // 查看清单模式下，直接导航到确认页面查看已有清单
            // 设置返回时不需要重新加载数据
            needReloadData = false
            navigateToConfirmPage(ArrayList(selectedItems))
            return
        }
        
        // 如果没有选择物品，检查按钮文字确定有无清单中物品
        if (selectedItems.isEmpty()) {
            // 获取按钮文字中的数字
            val buttonText = binding.btnApply.text.toString()
            val countRegex = "\\((\\d+)\\)".toRegex() // 匹配(数字)格式
            val matchResult = countRegex.find(buttonText)
            val count = matchResult?.groupValues?.get(1)?.toIntOrNull() ?: 0
            
            // 如果清单中确实有物品（按钮已启用），直接导航到确认页面
            if (count > 0 && binding.btnApply.isEnabled) {
                // 虽然没有新选择物品，但确认清单中已有物品，可以查看
                // 设置返回时不需要重新加载数据
                needReloadData = false
                navigateToConfirmPage(ArrayList())
                return
            }
            
            // 确认清单中没有物品，提示用户选择
            Toast.makeText(requireContext(), "请选择至少一项耗材", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 有选择物品，准备添加到清单
        val itemsToConfirm = ArrayList(selectedItems)
        // 设置返回时不需要重新加载数据
        needReloadData = false
        navigateToConfirmPage(itemsToConfirm)
        
        // 导航后设置为查看清单模式
        isViewingCartMode = true
    }
    
    /**
     * 导航到确认页面
     */
    private fun navigateToConfirmPage(items: ArrayList<SelectedItem>) {
        // 准备确认清单，直接使用putParcelableArray，避免类型不匹配
        val bundle = Bundle().apply {
            putParcelableArray("selectedItems", items.toTypedArray())
        }
        
        try {
            findNavController().navigate(
                R.id.action_warehouseApplyFragment_to_warehouseConfirmApplyFragment,
                bundle
            )
        } catch (e: Exception) {
            Log.e(TAG, "导航到确认申请页面失败: ${e.message}")
            Toast.makeText(requireContext(), "页面跳转失败，请稍后再试", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 显示图片预览对话框
     */
    private fun showImagePreview(imageUrl: String) {
        val dialog = ImagePreviewDialogFragment.newInstance(imageUrl)
        dialog.show(childFragmentManager, "image_preview")
    }
    
    override fun onDestroyView() {
        // 移除文本变更监听器，避免内存泄漏
        if (binding.autoCompleteProduct.tag is TextWatcher) {
            binding.autoCompleteProduct.removeTextChangedListener(binding.autoCompleteProduct.tag as TextWatcher)
        }
        
        // 取消所有协程作业
        searchCoroutineScope.cancel()
        
        // 清理资源
        binding.autoCompleteProduct.setOnTouchListener(null)
        binding.autoCompleteProduct.setOnFocusChangeListener(null)
        binding.autoCompleteProduct.setOnItemClickListener(null)
        
        super.onDestroyView()
        isFragmentDestroyed = true
        _binding = null
    }
    
    companion object {
        private const val TAG = "WarehouseApplyFragment"
    }
    
    /**
     * 已选物品数据类
     */
    @Parcelize
    data class SelectedItem(
        val skuId: String,
        var quantity: Int,
        val itemName: String,
        val oemNumber: String? = null,      // OEM编号
        val categoryName: String? = null,    // 分类名称
        val unitPrice: Double = 0.0,         // 单价
        val picUrl: String? = null,          // 图片URL
        val articleCode: String? = null,     // 物品编码
        val specification: String? = null    // 规格属性
    ) : Parcelable
} 
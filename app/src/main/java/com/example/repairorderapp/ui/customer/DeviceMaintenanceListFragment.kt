package com.example.repairorderapp.ui.customer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.CustomerApi
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.databinding.FragmentDeviceMaintenanceListBinding
import com.example.repairorderapp.model.customer.DeviceMaintenanceRecord
import com.example.repairorderapp.ui.customer.adapter.DeviceMaintenanceAdapter
import com.example.repairorderapp.viewmodel.customer.CustomerViewModelFactory
import com.example.repairorderapp.viewmodel.customer.DeviceMaintenanceViewModel

/**
 * 设备维修记录列表Fragment
 */
class DeviceMaintenanceListFragment : Fragment() {

    private var _binding: FragmentDeviceMaintenanceListBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: DeviceMaintenanceViewModel
    private lateinit var adapter: DeviceMaintenanceAdapter
    
    private var deviceId: String? = null
    private var customerId: String? = null
    private var customerName: String? = null

    private lateinit var layoutManager: LinearLayoutManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDeviceMaintenanceListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 从参数中获取设备ID和客户ID
        deviceId = arguments?.getString("deviceId")
        customerId = arguments?.getString("customerId")
        customerName = arguments?.getString("customerName")
        
        if (deviceId == null) {
            Toast.makeText(requireContext(), "设备ID不能为空", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
            return
        }
        
        setupViewModel()
        setupRecyclerView()
        setupToolbar()
        setupSwipeRefresh()
        setupSearchView()
        setupEmptyView()
        observeViewModel()
        
        // 加载数据或恢复
        if (viewModel.maintenanceRecords.value.isNullOrEmpty()) {
            viewModel.loadDeviceMaintenanceRecords(deviceId!!)
        } else {
            adapter.submitList(viewModel.filteredRecords.value ?: emptyList())
            layoutManager.scrollToPositionWithOffset(viewModel.savedScrollPosition, viewModel.savedScrollOffset)
        }
    }
    
    private fun setupViewModel() {
        // 创建CustomerRepository
        val customerApi = ApiClient.createService(CustomerApi::class.java)
        val customerRepository = CustomerRepository(customerApi, null)
        
        // 使用ViewModelFactory创建ViewModel
        val factory = CustomerViewModelFactory(customerRepository)
        viewModel = ViewModelProvider(this, factory)[DeviceMaintenanceViewModel::class.java]
    }
    
    private fun setupRecyclerView() {
        adapter = DeviceMaintenanceAdapter { record ->
            // 点击查看详情
            viewModel.selectRecord(record)
        }
        
        layoutManager = LinearLayoutManager(requireContext())
        binding.recyclerView.layoutManager = layoutManager
        binding.recyclerView.adapter = adapter
    }
    
    private fun setupToolbar() {

        binding.toolbar.title = "维修记录"
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefresh.setOnRefreshListener {
            deviceId?.let {
                viewModel.loadDeviceMaintenanceRecords(it, true)
            }
        }
    }
    
    private fun setupSearchView() {
        binding.searchView.setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                viewModel.searchRecords(newText ?: "")
                return true
            }
        })
    }

    private fun setupEmptyView() {
        // 设置维修记录页面的空状态提示文本
        binding.emptyView.textEmpty.text = "暂无维修记录"
        binding.emptyView.textEmptyHint.text = "设备暂无维修记录或使用搜索功能"
    }

    private fun observeViewModel() {
        // 观察原始数据，用于判断是否有数据
        viewModel.maintenanceRecords.observe(viewLifecycleOwner) { records ->
            // 如果原始数据为空，显示空视图
            if (records.isEmpty()) {
                binding.emptyView.root.visibility = View.VISIBLE
                binding.recyclerView.visibility = View.GONE
            }
        }
        
        // 观察过滤后的数据，用于显示列表
        viewModel.filteredRecords.observe(viewLifecycleOwner) { records ->
            adapter.submitList(records)
            
            // 如果有原始数据但过滤后为空，显示"无搜索结果"，否则隐藏空视图
            val hasOriginalData = viewModel.maintenanceRecords.value?.isNotEmpty() == true
            if (hasOriginalData && records.isEmpty()) {
                // 显示无搜索结果的状态
                binding.emptyView.root.visibility = View.VISIBLE
                binding.recyclerView.visibility = View.GONE
            } else if (records.isNotEmpty()) {
                binding.emptyView.root.visibility = View.GONE
                binding.recyclerView.visibility = View.VISIBLE
            }
        }
        
        viewModel.loading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            if (isLoading) {
                binding.emptyView.root.visibility = View.GONE
            }
        }
        
        viewModel.error.observe(viewLifecycleOwner) { errorMsg ->
            if (errorMsg.isNotEmpty()) {
                Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_SHORT).show()
                viewModel.clearError()
            }
        }
        
        viewModel.refreshing.observe(viewLifecycleOwner) { isRefreshing ->
            binding.swipeRefresh.isRefreshing = isRefreshing
        }
        
        viewModel.selectedRecord.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let { record ->
                // 导航到维修记录详情页面
                showRecordDetails(record)
            }
        }

        if (viewModel.savedScrollPosition > 0) {
            layoutManager.scrollToPositionWithOffset(viewModel.savedScrollPosition, viewModel.savedScrollOffset)
        }
    }
    
    private fun showRecordDetails(record: DeviceMaintenanceRecord) {
        // 获取工单ID
        val orderCode = record.repairReport?.workOrderCode ?: "未知工单"
        val workOrderId = record.repairReport?.workOrderId
        
        if (workOrderId != null) {
            // 直接打开维修报告详情Activity
            com.example.repairorderapp.ui.report.RepairReportDetailActivity.start(requireContext(), workOrderId)
        } else {
            Toast.makeText(requireContext(), "无法获取工单ID", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onPause() {
        super.onPause()
        val pos = layoutManager.findFirstVisibleItemPosition()
        val offset = layoutManager.findViewByPosition(pos)?.top ?: 0
        viewModel.updateScrollPosition(pos, offset)
    }
} 
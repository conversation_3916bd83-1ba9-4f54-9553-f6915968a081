package com.example.repairorderapp.ui.map

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.example.repairorderapp.model.MapMarker
import com.example.repairorderapp.model.RepairOrder
import com.tencent.tencentmap.mapsdk.maps.model.CameraPosition

/**
 * 地图状态ViewModel
 * 用于保存地图数据和状态，实现2分钟内返回时的无感加载
 */
class MapViewModel : ViewModel() {
    // 地图完整状态
    private val _mapState = MutableLiveData<MapState>()
    val mapState: LiveData<MapState> = _mapState
    
    // 是否需要刷新
    private val _needRefresh = MutableLiveData<Boolean>(true)
    val needRefresh: LiveData<Boolean> = _needRefresh
    
    // 最后一次保存状态的时间
    private var lastSaveTime: Long = 0
    
    // 是否已初始化过地图
    private var hasInitialized = false
    
    // 计时器相关
    private val handler = Handler(Looper.getMainLooper())
    private val invalidateCacheRunnable = Runnable { 
        _needRefresh.postValue(true) 
    }
    
    // 所有未完成工单列表（包括没有经纬度的）
    private val _allUncompletedOrders = MutableLiveData<List<RepairOrder>>(emptyList())
    val allUncompletedOrders: LiveData<List<RepairOrder>> = _allUncompletedOrders
    
    /**
     * 保存完整的地图状态
     */
    fun saveMapState(state: MapState) {
        _mapState.value = state
        _needRefresh.value = false
        hasInitialized = true
        lastSaveTime = System.currentTimeMillis()
        
        // 重置计时器，2分钟后标记需要刷新
        handler.removeCallbacks(invalidateCacheRunnable)
        handler.postDelayed(invalidateCacheRunnable, 2 * 60 * 1000)
    }
    
    /**
     * 保存所有未完成工单列表（包括没有经纬度的）
     */
    fun saveAllUncompletedOrders(orders: List<RepairOrder>) {
        // 在主线程上安全更新LiveData
        handler.post {
            _allUncompletedOrders.value = ArrayList(orders)
            // 打印日志，便于跟踪更新
            android.util.Log.d("MapViewModel", "保存未完成工单: ${orders.size}个")
        }
    }
    
    /**
     * 获取所有未完成工单列表（包括没有经纬度的）
     */
    fun getAllUncompletedOrders(): List<RepairOrder> {
        val result = _allUncompletedOrders.value ?: emptyList()
        // 打印日志，便于跟踪获取
        android.util.Log.d("MapViewModel", "获取未完成工单: ${result.size}个")
        return result
    }
    
    /**
     * 重置刷新标记
     * 在恢复状态后调用，避免立即刷新
     */
    fun resetRefreshFlag() {
        _needRefresh.value = false
    }
    
    /**
     * 检查是否需要刷新数据
     * @return true 表示需要刷新，false 表示可以使用缓存
     */
    fun checkNeedRefresh(): Boolean {
        if (!hasInitialized) return true
        
        val timePassed = System.currentTimeMillis() - lastSaveTime
        return timePassed > 2 * 60 * 1000 || _needRefresh.value == true
    }
    
    /**
     * 获取缓存的地图状态
     */
    fun getCachedState(): MapState? = _mapState.value
    
    /**
     * 标记需要刷新
     */
    fun markForRefresh() {
        _needRefresh.value = true
    }
    
    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        handler.removeCallbacks(invalidateCacheRunnable)
    }
}

/**
 * 地图状态数据类
 * 包含地图所有需要保存的状态
 */
data class MapState(
    val customerMarkers: List<MapMarker>,
    val engineerMarkers: List<MapMarker>,
    val expandedMarkerId: String?,
    val cameraPosition: CameraPosition,
    val filterState: FilterState,
    val visibleEngineers: Int,
    val visibleCustomers: Int
)

/**
 * 筛选状态数据类
 * 保存地图筛选器的状态
 */
data class FilterState(
    val isEngineersChecked: Boolean,
    val isIdleEngineersChecked: Boolean,
    val isCustomersChecked: Boolean,
    val isOverdueCustomersChecked: Boolean
) 
package com.example.repairorderapp.ui.profile

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.example.repairorderapp.R
import com.example.repairorderapp.ui.login.LoginActivity
import com.example.repairorderapp.ui.setting.KeepAliveGuideActivity

import com.example.repairorderapp.util.CrashLogManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder

/**
 * 系统设置页面
 */
class SettingsFragment : Fragment() {

    companion object {
        private const val TAG = "SettingsFragment"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        
        // 设置修改密码按钮
        view.findViewById<View>(R.id.btn_account_security)?.setOnClickListener {
            Toast.makeText(requireContext(), "功能开发中", Toast.LENGTH_SHORT).show()
            // TODO: 修改密码弹窗
        }
        
        // 设置后台运行设置按钮
        view.findViewById<View>(R.id.btn_keep_alive_guide)?.setOnClickListener {
            navigateToKeepAliveGuide()
        }
        
        // 设置崩溃日志查看按钮
        view.findViewById<View>(R.id.btn_crash_logs)?.setOnClickListener {
            showCrashLogsDialog()
        }



        // 设置关于我们按钮
        view.findViewById<View>(R.id.btn_about)?.setOnClickListener {
            Toast.makeText(requireContext(), "功能开发中", Toast.LENGTH_SHORT).show()
            // TODO: 跳转到关于我们页面
        }
        
        // 设置退出登录按钮
        view.findViewById<View>(R.id.btn_logout)?.setOnClickListener {
            logout()
        }
    }
    
    /**
     * 导航到保活设置指南页面
     */
    private fun navigateToKeepAliveGuide() {
        try {
            val intent = Intent(requireContext(), KeepAliveGuideActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "导航到保活设置指南失败：${e.message}", e)
            Toast.makeText(requireContext(), "打开设置页面失败", Toast.LENGTH_SHORT).show()
        }
    }


    
    /**
     * 显示崩溃日志对话框
     */
    private fun showCrashLogsDialog() {
        try {
            val stats = CrashLogManager.getCrashLogStats(requireContext())

            val statusInfo = if (stats.totalCount == 0) {
                "暂无崩溃日志"
            } else {
                "发现 ${stats.totalCount} 个崩溃日志\n最后崩溃时间: ${stats.getLatestCrashTimeFormatted() ?: "未知"}\n总大小: ${stats.getTotalSizeFormatted()}"
            }

            // 显示状态信息弹窗，包含基本操作和"更多"按钮
            MaterialAlertDialogBuilder(requireContext())
                .setTitle("崩溃日志状态")
                .setMessage(statusInfo)
                .setPositiveButton("查看最新") { _, _ ->
                    showLatestCrashLog()
                }
                .setNeutralButton("更多") { _, _ ->
                    showCrashLogManagementDialog()
                }
                .setNegativeButton("取消", null)
                .show()

        } catch (e: Exception) {
            Log.e(TAG, "显示崩溃日志对话框失败：${e.message}", e)
            Toast.makeText(requireContext(), "获取崩溃日志失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 显示崩溃日志管理弹窗（不包含状态信息）
     */
    private fun showCrashLogManagementDialog() {
        try {
            val options = arrayOf(
                "📄 查看最新崩溃日志",
                "📋 查看所有崩溃日志列表",
                "📤 导出所有崩溃日志",
                "📱 一键分享所有日志",
                "🧹 清理旧日志（保留最近10个）",
                "🧪 生成测试崩溃日志"
            )

            MaterialAlertDialogBuilder(requireContext())
                .setTitle("崩溃日志管理")
                .setItems(options) { _, which ->
                    when (which) {
                        0 -> showLatestCrashLog()
                        1 -> showAllCrashLogsList()
                        2 -> exportAllCrashLogs()
                        3 -> shareAllCrashLogsDirectly()
                        4 -> cleanOldCrashLogs()
                        5 -> generateTestCrashLog()
                    }
                }
                .setNegativeButton("取消", null)
                .show()

        } catch (e: Exception) {
            Log.e(TAG, "显示崩溃日志管理弹窗失败：${e.message}", e)
            Toast.makeText(requireContext(), "操作失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 显示最新的崩溃日志
     */
    private fun showLatestCrashLog() {
        try {
            val latestLog = CrashLogManager.getLatestCrashLog(requireContext())
            if (latestLog == null) {
                Toast.makeText(requireContext(), "没有找到崩溃日志", Toast.LENGTH_SHORT).show()
                return
            }
            
            val logContent = CrashLogManager.readCrashLog(latestLog)
            showCrashLogContentDialog(latestLog.name, logContent, latestLog)
            
        } catch (e: Exception) {
            Log.e(TAG, "显示最新崩溃日志失败：${e.message}", e)
            Toast.makeText(requireContext(), "读取崩溃日志失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 显示所有崩溃日志列表
     */
    private fun showAllCrashLogsList() {
        try {
            val allLogs = CrashLogManager.getAllCrashLogs(requireContext())
            if (allLogs.isEmpty()) {
                Toast.makeText(requireContext(), "没有找到崩溃日志", Toast.LENGTH_SHORT).show()
                return
            }
            
            val logNames = allLogs.map { 
                "${it.name} (${java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault()).format(java.util.Date(it.lastModified()))})"
            }.toTypedArray()
            
            MaterialAlertDialogBuilder(requireContext())
                .setTitle("选择要查看的崩溃日志")
                .setItems(logNames) { _, which ->
                    val selectedLog = allLogs[which]
                    val logContent = CrashLogManager.readCrashLog(selectedLog)
                    showCrashLogContentDialog(selectedLog.name, logContent, selectedLog)
                }
                .setNegativeButton("取消", null)
                .show()
                
        } catch (e: Exception) {
            Log.e(TAG, "显示崩溃日志列表失败：${e.message}", e)
            Toast.makeText(requireContext(), "获取崩溃日志列表失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 显示崩溃日志内容对话框
     */
    private fun showCrashLogContentDialog(title: String, content: String, logFile: java.io.File) {
        val scrollView = ScrollView(requireContext())
        val textView = TextView(requireContext()).apply {
            text = content
            textSize = 12f
            typeface = android.graphics.Typeface.MONOSPACE
            setPadding(32, 32, 32, 32)
            setTextIsSelectable(true)
        }
        scrollView.addView(textView)
        
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(title)
            .setView(scrollView)
            .setPositiveButton("分享日志") { _, _ ->
                CrashLogManager.shareCrashLog(requireContext(), logFile)
            }
            .setNegativeButton("关闭", null)
            .show()
    }
    
    /**
     * 导出所有崩溃日志
     */
    private fun exportAllCrashLogs() {
        try {
            val exportFile = CrashLogManager.exportAllCrashLogs(requireContext())
            if (exportFile != null) {
                MaterialAlertDialogBuilder(requireContext())
                    .setTitle("导出成功")
                    .setMessage("所有崩溃日志已导出到：\n${exportFile.absolutePath}")
                    .setPositiveButton("分享文件") { _, _ ->
                        CrashLogManager.shareCrashLog(requireContext(), exportFile)
                    }
                    .setNegativeButton("确定", null)
                    .show()
            } else {
                Toast.makeText(requireContext(), "导出失败，没有崩溃日志", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "导出崩溃日志失败：${e.message}", e)
            Toast.makeText(requireContext(), "导出崩溃日志失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 清理旧的崩溃日志
     */
    private fun cleanOldCrashLogs() {
        try {
            val result = CrashLogManager.cleanOldCrashLogs(requireContext(), 10)
            Toast.makeText(requireContext(), result, Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "清理崩溃日志失败：${e.message}", e)
            Toast.makeText(requireContext(), "清理崩溃日志失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 生成测试崩溃日志
     */
    private fun generateTestCrashLog() {
        try {
            val result = CrashLogManager.generateTestCrashLog(requireContext())
            Toast.makeText(requireContext(), result, Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "生成测试崩溃日志失败：${e.message}", e)
            Toast.makeText(requireContext(), "生成测试崩溃日志失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 一键分享所有崩溃日志
     */
    private fun shareAllCrashLogsDirectly() {
        try {
            Toast.makeText(requireContext(), "正在准备分享所有日志...", Toast.LENGTH_SHORT).show()

            // 提供两种分享方式的选择
            val options = arrayOf(
                "📄 分享单个合并文件",
                "📁 分享多个独立文件"
            )

            MaterialAlertDialogBuilder(requireContext())
                .setTitle("选择分享方式")
                .setItems(options) { _, which ->
                    when (which) {
                        0 -> shareAsMergedFile()
                        1 -> shareAsMultipleFiles()
                    }
                }
                .setNegativeButton("取消", null)
                .show()

        } catch (e: Exception) {
            Log.e(TAG, "一键分享崩溃日志失败：${e.message}", e)
            Toast.makeText(requireContext(), "分享失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 分享为单个合并文件
     */
    private fun shareAsMergedFile() {
        try {
            val exportFile = CrashLogManager.exportAllCrashLogs(requireContext())
            if (exportFile != null) {
                CrashLogManager.shareCrashLog(requireContext(), exportFile)
                Toast.makeText(requireContext(), "合并文件已准备分享", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "没有崩溃日志可分享", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "分享合并文件失败：${e.message}", e)
            Toast.makeText(requireContext(), "分享失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 分享为多个独立文件
     */
    private fun shareAsMultipleFiles() {
        try {
            val result = CrashLogManager.shareAllCrashLogsMultiple(requireContext())
            Toast.makeText(requireContext(), result, Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "分享多个文件失败：${e.message}", e)
            Toast.makeText(requireContext(), "分享失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 退出登录
     */
    private fun logout() {
        try {
            // 显示退出登录提示
            Toast.makeText(requireContext(), "退出登录中...", Toast.LENGTH_SHORT).show()
            
            // 使用TokenFixer清除所有存储位置的令牌
            val tokenFixer = com.example.repairorderapp.network.TokenFixer.getInstance(requireContext())
            tokenFixer.clearAllTokens()
            
            // 清除SharedPreferences中的用户登录信息
            val prefs = requireActivity().getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            prefs.edit().apply {
                remove("accessToken")
                remove("refreshToken")
                remove("userId")
                remove("userName")
                remove("isLogin")
                remove("expiresAt")
                remove("engineerId")
                // 清除其他可能存在的用户相关信息
                apply()
            }
            
            // 清除SharedPrefsManager中的用户数据
            val sharedPrefsManager = com.example.repairorderapp.util.SharedPrefsManager(requireContext())
            sharedPrefsManager.clearUserData()
            sharedPrefsManager.clearAuthToken()
            sharedPrefsManager.saveLoginStatus(false)
            
            // 清除PreferenceUtils中的令牌
            com.example.repairorderapp.utils.PreferenceUtils.remove(requireContext(), "accessToken")
            com.example.repairorderapp.utils.PreferenceUtils.remove(requireContext(), "refreshToken")
            
            // TODO: 如果有需要，调用API通知服务器用户已登出
            // apiService.logout()
            
            // 跳转到登录界面
            val intent = Intent(requireContext(), com.example.repairorderapp.ui.login.LoginActivity::class.java).apply {
                // 清除Activity堆栈，防止用户通过返回键回到已登录的界面
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            startActivity(intent)
            
            // 关闭当前Activity
            requireActivity().finish()
        } catch (e: Exception) {
            Log.e(TAG, "退出登录时出错：${e.message}", e)
            Toast.makeText(requireContext(), "退出登录失败，请重试", Toast.LENGTH_SHORT).show()
        }
    }
} 
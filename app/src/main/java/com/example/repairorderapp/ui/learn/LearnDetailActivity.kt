package com.example.repairorderapp.ui.learn

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.repairorderapp.R
import com.example.repairorderapp.data.repository.LearnRepository
import com.example.repairorderapp.model.LearnDetailResponse
import com.example.repairorderapp.util.SharedPrefsManager
import com.example.repairorderapp.util.ToastUtils
import kotlinx.coroutines.launch

/**
 * 知识库详情页面
 */
class LearnDetailActivity : AppCompatActivity() {
    
    companion object {
        const val EXTRA_LEARN_ID = "learn_id"
        const val EXTRA_LEARN_TITLE = "learn_title"
    }
    
    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    private lateinit var repository: LearnRepository
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_learn_detail)
        
        // 设置工具栏
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = intent.getStringExtra(EXTRA_LEARN_TITLE) ?: "知识库详情"
        }
        
        // 初始化Repository
        val sharedPrefsManager = SharedPrefsManager(this)
        repository = LearnRepository(sharedPrefsManager)
        
        initViews()
        loadContent()
    }
    
    private fun initViews() {
        webView = findViewById(R.id.webview_content)
        progressBar = findViewById(R.id.progress_loading)
        
        webView.apply {
            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    hideLoading()
                }
                
                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                    // 处理案例点击事件
                    if (url?.startsWith("case://") == true) {
                        val caseId = url.substringAfter("case://")
                        handleCaseClick(caseId)
                        return true
                    }
                    return false
                }
            }
            settings.apply {
                javaScriptEnabled = true
                setSupportZoom(true)
                builtInZoomControls = true
                displayZoomControls = false
                loadWithOverviewMode = true
                useWideViewPort = true
                domStorageEnabled = true
                allowFileAccess = false
                allowContentAccess = false
                setGeolocationEnabled(false)
            }
        }
    }
    
    private fun loadContent() {
        val learnId = intent.getStringExtra(EXTRA_LEARN_ID)
        
        if (learnId.isNullOrEmpty()) {
            ToastUtils.showToast(this, "无效的知识库ID")
            finish()
            return
        }
        
        showLoading()
        
        lifecycleScope.launch {
            try {
                val result = repository.getLearnDetail(learnId)
                if (result.isSuccess) {
                    val learnDetail = result.getOrNull()
                    if (learnDetail != null) {
                        // 根据Vue项目的实现生成详细的HTML内容
                        val htmlContent = generateDetailContent(learnDetail)
                        webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)
                    } else {
                        showError("知识库内容为空")
                    }
                } else {
                    showError(result.exceptionOrNull()?.message ?: "加载知识库详情失败")
                }
            } catch (e: Exception) {
                showError("网络错误：${e.message}")
            }
        }
    }
    
    private fun showLoading() {
        progressBar.visibility = View.VISIBLE
        webView.visibility = View.GONE
    }
    
    private fun hideLoading() {
        progressBar.visibility = View.GONE
        webView.visibility = View.VISIBLE
    }
    
    private fun showError(message: String) {
        hideLoading()
        ToastUtils.showToast(this, message)
        
        // 显示错误页面
        val errorHtml = """
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        text-align: center; 
                        padding: 40px 20px; 
                        color: #666;
                    }
                    .error-icon { 
                        font-size: 48px; 
                        margin-bottom: 16px; 
                    }
                    .error-message { 
                        font-size: 16px; 
                        margin-bottom: 24px; 
                    }
                    .retry-button {
                        background: #1976d2;
                        color: white;
                        padding: 12px 24px;
                        border: none;
                        border-radius: 4px;
                        font-size: 14px;
                        cursor: pointer;
                    }
                </style>
            </head>
            <body>
                <div class="error-icon">⚠️</div>
                <div class="error-message">$message</div>
                <button class="retry-button" onclick="location.reload()">重试</button>
            </body>
            </html>
        """.trimIndent()
        
        webView.loadDataWithBaseURL(null, errorHtml, "text/html", "UTF-8", null)
    }
    
    private fun handleCaseClick(caseId: String) {
        try {
            // 尝试从当前加载的知识库详情中查找对应的案例
            lifecycleScope.launch {
                try {
                    val learnId = intent.getStringExtra(EXTRA_LEARN_ID) ?: ""
                    if (learnId.isNotEmpty()) {
                        // 重新获取知识库详情以获取完整的案例信息
                        val result = repository.getLearnDetail(learnId)
                        if (result.isSuccess) {
                            val learnDetail = result.getOrNull()
                            val selectedCase = learnDetail?.knowledgeRepairCase?.find { it.id == caseId }
                            
                            if (selectedCase != null) {
                                // 使用Gson序列化案例数据
                                val gson = com.google.gson.Gson()
                                val caseJson = gson.toJson(selectedCase)
                                
                                val intent = Intent(this@LearnDetailActivity, CaseDetailActivity::class.java).apply {
                                    putExtra(CaseDetailActivity.EXTRA_CASE_ID, caseId)
                                    putExtra(CaseDetailActivity.EXTRA_CASE_TITLE, selectedCase.title)
                                    putExtra(CaseDetailActivity.EXTRA_CASE_JSON, caseJson)
                                }
                                startActivity(intent)
                            } else {
                                ToastUtils.showToast(this@LearnDetailActivity, "未找到案例信息")
                            }
                        } else {
                            ToastUtils.showToast(this@LearnDetailActivity, "获取案例信息失败")
                        }
                    } else {
                        ToastUtils.showToast(this@LearnDetailActivity, "无效的知识库ID")
                    }
                } catch (e: Exception) {
                    ToastUtils.showToast(this@LearnDetailActivity, "打开案例详情失败: ${e.message}")
                }
            }
        } catch (e: Exception) {
            ToastUtils.showToast(this, "打开案例详情失败")
        }
    }
    
    private fun processCodeExplanation(codeExplain: String?): String {
        if (codeExplain.isNullOrEmpty()) return ""
        
        // 处理HTML内容
        val processedContent = codeExplain
            .trim()
            // 将 <br/> 替换为段落分隔标记
            .replace(Regex("<br\\s*/?>"), "|||PARAGRAPH_BREAK|||")
            // 将 </p><p> 替换为换行，保持在同一段落内
            .replace(Regex("</p>\\s*<p[^>]*>"), "\n")
            // 去除开头和结尾的p标签
            .replace(Regex("^<p[^>]*>"), "")
            .replace(Regex("</p>$"), "")
            // 去除其他HTML标签
            .replace(Regex("<[^>]*>"), "")
            // 去除HTML实体
            .replace("&nbsp;", "")
            
        // 按段落分隔符分割
        val paragraphs = processedContent.split("|||PARAGRAPH_BREAK|||")
        val result = mutableListOf<String>()
        
        for (paragraph in paragraphs) {
            // 处理每个段落内的行
            val lines = paragraph.lines()
                .map { line ->
                    line.replace(Regex("^[\\s　\\t]+"), "") // 去除行首空白
                        .replace(Regex("[\\s　\\t]+$"), "") // 去除行尾空白
                        .trim()
                }
                .filter { it.isNotEmpty() }
            
            if (lines.isNotEmpty()) {
                result.addAll(lines)
                result.add("") // 段落间添加空行
            }
        }
        
        // 去除最后一个多余的空行
        if (result.isNotEmpty() && result.last().isEmpty()) {
            result.removeAt(result.size - 1)
        }
        
        return result.joinToString("\n").trim()
    }
    
    private fun generateDetailContent(learnDetail: LearnDetailResponse): String {
        val title = learnDetail.title ?: "知识库详情"
        val learnId = learnDetail.id
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>$title</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
                        line-height: 1.6;
                        color: #1D1D1F;
                        padding: 12px;
                        margin: 0;
                        background-color: #f8f9fa;
                        font-size: 16px;
                        font-weight: 400;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
                    }
                    .content {
                        background: white;
                        padding: 16px;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                        margin-bottom: 12px;
                    }
                    h1 {
                        color:rgb(5, 111, 249);
                        margin-top: 0;
                        margin-bottom: 12px;
                        font-size: 28px;
                        font-weight: 700;
                        line-height: 1.2;
                        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                        letter-spacing: -0.01em;
                    }
                    h2 {
                        color: #1D1D1F;
                        margin-top: 20px;
                        margin-bottom: 12px;
                        font-size: 20px;
                        font-weight: 600;
                        border-bottom: 2px solid #e8eaed;
                        padding-bottom: 4px;
                        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                        letter-spacing: -0.005em;
                    }
                    .meta {
                        background: #f1f3f4;
                        padding: 12px 16px;
                        border-radius: 6px;
                        margin-bottom: 16px;
                        font-size: 15px;
                        color: #5f6368;
                        line-height: 1.6;
                    }
                    .step {
                        background: #fff3cd;
                        border-left: 4px solid #ffc107;
                        padding: 12px 16px;
                        margin: 12px 0;
                        border-radius: 0 6px 6px 0;
                    }
                    .warning {
                        background: #f8d7da;
                        border-left: 4px solid #dc3545;
                        padding: 12px 16px;
                        margin: 12px 0;
                        border-radius: 0 6px 6px 0;
                    }
                    .tip {
                        background: #d1ecf1;
                        border-left: 4px solid #17a2b8;
                        padding: 12px 16px;
                        margin: 12px 0;
                        border-radius: 0 6px 6px 0;
                    }
                    code {
                        background: #f1f3f4;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-family: 'Courier New', monospace;
                        font-size: 15px;
                        color: #d63384;
                    }
                    .code-block {
                        background: #f8f9fa;
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                        padding: 12px;
                        margin: 12px 0;
                        overflow-x: auto;
                        font-size: 15px;
                    }
                    .model-list {
                        display: flex;
                        flex-wrap: nowrap;
                        gap: 8px;
                        margin: 12px 0 16px 0;
                        overflow-x: auto;
                        overflow-y: hidden;
                        white-space: nowrap;
                        padding-bottom: 8px;
                        scrollbar-width: thin;
                        scrollbar-color: #ddd transparent;
                    }
                    .model-list::-webkit-scrollbar {
                        height: 4px;
                    }
                    .model-list::-webkit-scrollbar-track {
                        background: transparent;
                    }
                    .model-list::-webkit-scrollbar-thumb {
                        background: #ddd;
                        border-radius: 2px;
                    }
                    .model-list::-webkit-scrollbar-thumb:hover {
                        background: #bbb;
                    }
                    .model-tag {
                        background: #F2F2F7;
                        color: #3C3C43;
                        padding: 6px 12px;
                        border-radius: 4px;
                        font-size: 14px;
                        font-weight: 500;
                        white-space: nowrap;
                        flex-shrink: 0;
                        margin-inline-end: 4px;
                        border: 1px solid rgba(60, 60, 67, 0.1);
                    }
                    .content-types {
                        display: flex;
                        gap: 20px;
                        margin: 20px 0;
                        justify-content: center;
                        flex-wrap: wrap;
                    }
                    .content-type {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        background: #f8f9fa;
                        padding: 12px 16px;
                        border-radius: 24px;
                        font-size: 15px;
                        color: #5f6368;
                    }
                    .icon {
                        width: 18px;
                        height: 18px;
                        background: #666;
                        border-radius: 3px;
                    }
                    .code-explanation {
                        background: #f0f8f0;
                        color: #2d3748;
                        padding: 12px 8px;
                        border-radius: 8px;
                        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 1.6;
                        margin: 8px 0 20px 0;
                        overflow-x: auto;
                        white-space: pre-line;
                        word-break: break-word;
                        border: 1px solid #e2e8f0;
                        text-indent: 0;
                        padding-left: 8px;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
                    }
                    .code-explanation p {
                        margin: 0;
                        padding: 0;
                    }
                    .code-explanation::before,
                    .code-explanation::after {
                        content: "";
                        display: none;
                    }
                    .repair-cases {
                        margin: 8px 0 20px 0;
                    }
                    .repair-case-item {
                        display: flex;
                        align-items: center;
                        padding: 12px 8px;
                        border-bottom: 1px solid #f0f0f0;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border-radius: 6px;
                        margin-bottom: 6px;
                    }
                    .repair-case-item:hover {
                        background-color: #f8f9fa;
                        transform: translateX(4px);
                    }
                    .repair-case-item:last-child {
                        border-bottom: none;
                        margin-bottom: 0;
                    }
                    .case-number {
                        width: 28px;
                        height: 28px;
                        background: #007AFF;
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 13px;
                        font-weight: 600;
                        margin-right: 12px;
                        color: white;
                        min-width: 28px;
                        box-shadow: 0 1px 3px rgba(0, 122, 255, 0.2);
                    }
                    .case-title {
                        flex: 1;
                        font-size: 16px;
                        color: #333;
                        font-weight: 500;
                        line-height: 1.5;
                    }
                    .case-arrow {
                        font-size: 18px;
                        color: #999;
                        margin-left: 12px;
                    }
                    /* 强调文本样式 */
                    strong {
                        font-weight: 600;
                        color: #2c3e50;
                    }
                    /* 响应式设计 */
                    @media (max-width: 768px) {
                        body {
                            padding: 8px;
                            font-size: 15px;
                        }
                        .content {
                            padding: 12px;
                        }
                        h1 {
                            font-size: 22px;
                            margin-bottom: 8px;
                        }
                        h2 {
                            font-size: 18px;
                            margin-top: 16px;
                            margin-bottom: 8px;
                        }
                        .meta {
                            padding: 10px 12px;
                            font-size: 14px;
                            margin-bottom: 12px;
                        }
                        .model-tag {
                            padding: 4px 8px;
                            font-size: 12px;
                        }
                        .case-number {
                            width: 24px;
                            height: 24px;
                            font-size: 12px;
                            margin-right: 8px;
                            border-radius: 6px;
                        }
                        .case-title {
                            font-size: 14px;
                        }
                        .repair-case-item {
                            padding: 8px 6px;
                            margin-bottom: 4px;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="content">
                    <h1>$title</h1>
                    
                    <div class="meta">
                        <strong>分类：</strong>${learnDetail.type.label}
                    </div>
                    
                    <div class="model-list">
                        ${learnDetail.models.joinToString("") { "<div class=\"model-tag\">$it</div>" }}
                    </div>
                    
                    ${if (!learnDetail.codeExplain.isNullOrEmpty()) """
                    <h2>代码解释</h2>
                    <div class="code-explanation">${processCodeExplanation(learnDetail.codeExplain)}</div>
                    """ else ""}
                    
                    ${if (!learnDetail.knowledgeRepairCase.isNullOrEmpty()) """
                    <h2>相关案例</h2>
                    <div class="repair-cases">
                        ${learnDetail.knowledgeRepairCase!!.mapIndexed { index, case ->
                            """
                            <div class="repair-case-item" onclick="location.href='case://${case.id}'">
                                <div class="case-number">${index + 1}</div>
                                <div class="case-title">${case.title}</div>
                                <div class="case-arrow">→</div>
                            </div>
                            """
                        }.joinToString("")}
                    </div>
                    """ else ""}
                    

                </div>
            </body>
            </html>
        """.trimIndent()
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
} 
package com.example.repairorderapp.ui.customer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.CustomerApi
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.databinding.DialogStaffEditBinding
import com.example.repairorderapp.databinding.FragmentCustomerStaffBinding
import com.example.repairorderapp.model.customer.CustomerOption
import com.example.repairorderapp.model.customer.CustomerStaff
import com.example.repairorderapp.viewmodel.customer.CustomerStaffViewModel
import com.example.repairorderapp.viewmodel.customer.CustomerViewModelFactory
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlinx.coroutines.launch

/**
 * 客户员工管理Fragment
 */
class CustomerStaffFragment : Fragment() {

    private var _binding: FragmentCustomerStaffBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var viewModel: CustomerStaffViewModel
    private lateinit var adapter: CustomerStaffAdapter
    private var customerId: String? = null
    
    // 角色选项列表
    private val roleOptions = mutableListOf<CustomerOption>()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCustomerStaffBinding.inflate(inflater, container, false)
        
        // 获取客户ID
        customerId = arguments?.getString("customerId")
        
        // 创建CustomerRepository
        val customerApi = ApiClient.createService(CustomerApi::class.java)
        val customerRepository = CustomerRepository(customerApi, null)
        
        // 使用ViewModelFactory创建ViewModel
        val factory = CustomerViewModelFactory(customerRepository)
        viewModel = ViewModelProvider(this, factory)[CustomerStaffViewModel::class.java]
        
        // 初始化角色选项
        initRoleOptions()
        
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupToolbar()
        setupRecyclerView()
        setupSwipeRefresh()
        setupFab()
        observeViewModel()
        
        // 加载员工列表
        customerId?.let {
            viewModel.getCustomerStaffList(it)
        } ?: run {
            Toast.makeText(requireContext(), "客户ID不能为空", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
        }
    }
    
    private fun setupToolbar() {

        binding.toolbar.title = "员工管理"
    }
    
    private fun setupRecyclerView() {
        adapter = CustomerStaffAdapter(
            onItemClick = { staff ->
                showEditStaffDialog(staff)
            },
            onDeleteClick = { staff ->
                showDeleteConfirmDialog(staff)
            }
        )
        
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefresh.setOnRefreshListener {
            customerId?.let {
                viewModel.getCustomerStaffList(it)
            }
        }
    }
    
    private fun setupFab() {
        binding.fabAddStaff.setOnClickListener {
            showAddStaffDialog()
        }
    }
    
    private fun observeViewModel() {
        viewModel.staffList.observe(viewLifecycleOwner) { staffList ->
            adapter.submitList(staffList)
            binding.emptyView.visibility = if (staffList.isEmpty()) View.VISIBLE else View.GONE
        }
        
        viewModel.loading.observe(viewLifecycleOwner) { isLoading ->
            binding.swipeRefresh.isRefreshing = isLoading
            binding.progressBar.visibility = if (isLoading && viewModel.staffList.value.isNullOrEmpty()) View.VISIBLE else View.GONE
        }
        
        viewModel.error.observe(viewLifecycleOwner) { errorMsg ->
            if (errorMsg.isNotEmpty()) {
                Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_SHORT).show()
                viewModel.clearError()
            }
        }
        
        viewModel.operationSuccess.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let { message ->
                android.util.Log.d("CustomerStaffFragment", "操作成功: $message")
                Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                // 刷新列表
                customerId?.let { id ->
                    android.util.Log.d("CustomerStaffFragment", "开始刷新员工列表，客户ID: $id")
                    viewModel.getCustomerStaffList(id)
                } ?: run {
                    android.util.Log.e("CustomerStaffFragment", "客户ID为空，无法刷新列表")
                }
            }
        }
    }
    
    private fun initRoleOptions() {
        // 从API获取角色列表，参照Vue项目dictTreeByCodeApi(500)
        roleOptions.clear()
        
        // 先添加默认角色，以防API调用失败
        roleOptions.add(CustomerOption("501", "店长"))
        roleOptions.add(CustomerOption("502", "技术员"))
        roleOptions.add(CustomerOption("503", "销售"))
        
        // 通过ViewModel获取角色列表
        viewModel.getDictTreeByCode("500")
        
        // 观察角色列表数据变化
        viewModel.roleOptions.observe(viewLifecycleOwner) { options ->
            if (options.isNotEmpty()) {
                roleOptions.clear()
                roleOptions.addAll(options)
            }
        }
    }
    
    private fun showEditStaffDialog(staff: CustomerStaff) {
        val dialogBinding = DialogStaffEditBinding.inflate(layoutInflater)
        dialogBinding.dialogTitle.text = "编辑员工信息"
        
        // 设置初始值
        dialogBinding.etName.setText(staff.name)
        dialogBinding.etPhone.setText(staff.tel)
        
        // 设置角色下拉框
        setupRoleSpinner(dialogBinding, staff.role?.value)
        
        // 设置账号状态
        dialogBinding.rbEnabled.isChecked = staff.status == true
        dialogBinding.rbDisabled.isChecked = staff.status == false
        
        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setView(dialogBinding.root)
            .setCancelable(false)
            .create()
        
        dialogBinding.btnCancel.setOnClickListener {
            dialog.dismiss()
        }
        
        dialogBinding.btnSave.setOnClickListener {
            val name = dialogBinding.etName.text.toString().trim()
            val phone = dialogBinding.etPhone.text.toString().trim()
            val rolePosition = dialogBinding.spinnerRole.selectedItemPosition
            val isEnabled = when (dialogBinding.rgStatus.checkedRadioButtonId) {
                dialogBinding.rbEnabled.id -> true
                dialogBinding.rbDisabled.id -> false
                else -> true // 默认为在职状态
            }
            
            if (name.isEmpty()) {
                Toast.makeText(requireContext(), "姓名不能为空", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (phone.isEmpty()) {
                Toast.makeText(requireContext(), "电话不能为空", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (phone.length != 11) {
                Toast.makeText(requireContext(), "电话号码必须是11位", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (rolePosition < 0 || rolePosition >= roleOptions.size) {
                Toast.makeText(requireContext(), "请选择角色", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            val selectedRole = roleOptions[rolePosition]
            
            // 创建更新后的员工对象
            val updatedStaff = staff.copy(
                name = name,
                tel = phone,
                role = selectedRole,
                status = isEnabled
            )
            
            // 调用ViewModel更新员工信息
            viewModel.updateCustomerStaff(updatedStaff)
            
            dialog.dismiss()
        }
        
        dialog.show()
    }
    
    private fun showAddStaffDialog() {
        val dialogBinding = DialogStaffEditBinding.inflate(layoutInflater)
        dialogBinding.dialogTitle.text = "添加员工"
        
        // 设置角色下拉框
        setupRoleSpinner(dialogBinding, null)
        
        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setView(dialogBinding.root)
            .setCancelable(false)
            .create()
        
        dialogBinding.btnCancel.setOnClickListener {
            dialog.dismiss()
        }
        
        dialogBinding.btnSave.setOnClickListener {
            val name = dialogBinding.etName.text.toString().trim()
            val phone = dialogBinding.etPhone.text.toString().trim()
            val rolePosition = dialogBinding.spinnerRole.selectedItemPosition
            val isEnabled = dialogBinding.rbEnabled.isChecked
            
            if (name.isEmpty()) {
                Toast.makeText(requireContext(), "姓名不能为空", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (phone.isEmpty()) {
                Toast.makeText(requireContext(), "电话不能为空", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (phone.length != 11) {
                Toast.makeText(requireContext(), "电话号码必须是11位", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (rolePosition < 0 || rolePosition >= roleOptions.size) {
                Toast.makeText(requireContext(), "请选择角色", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            val selectedRole = roleOptions[rolePosition]
            
            // 创建新员工对象
            val newStaff = CustomerStaff(
                id = "", // ID由服务器生成
                customerId = customerId ?: "",
                name = name,
                tel = phone,
                role = selectedRole,
                status = isEnabled
            )
            
            // 调用ViewModel添加员工
            viewModel.addCustomerStaff(newStaff)
            
            dialog.dismiss()
        }
        
        dialog.show()
    }
    
    private fun setupRoleSpinner(dialogBinding: DialogStaffEditBinding, selectedRoleValue: String?) {
        val roleAdapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            roleOptions.map { it.label }
        )
        roleAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        dialogBinding.spinnerRole.adapter = roleAdapter
        
        // 设置选中项
        if (selectedRoleValue != null) {
            val selectedIndex = roleOptions.indexOfFirst { it.value == selectedRoleValue }
            if (selectedIndex >= 0) {
                dialogBinding.spinnerRole.setSelection(selectedIndex)
            }
        }
    }
    
    private fun showDeleteConfirmDialog(staff: CustomerStaff) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("删除确认")
            .setMessage("确定要删除员工 ${staff.name} 吗？")
            .setNegativeButton("取消", null)
            .setPositiveButton("确定") { _, _ ->
                viewModel.deleteCustomerStaff(staff.id)
            }
            .show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 
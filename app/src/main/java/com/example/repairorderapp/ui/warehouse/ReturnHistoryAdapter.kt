package com.example.repairorderapp.ui.warehouse

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.example.repairorderapp.R
import com.example.repairorderapp.model.warehouse.ReturnHistoryItem

/**
 * 退料记录适配器
 */
class ReturnHistoryAdapter : RecyclerView.Adapter<ReturnHistoryAdapter.ViewHolder>() {

    private var items: List<ReturnHistoryItem> = emptyList()
    private var onImageClickListener: ((String) -> Unit)? = null
    
    // 记录每个项目的展开状态
    private val expandStateMap = mutableMapOf<String, Boolean>()
    
    fun submitList(list: List<ReturnHistoryItem>) {
        this.items = list
        // 重置展开状态
        expandStateMap.clear()
        list.forEach { expandStateMap[it.id] = false }
        notifyDataSetChanged()
    }
    
    fun addItems(newItems: List<ReturnHistoryItem>) {
        val oldSize = items.size
        val newList = items.toMutableList()
        newList.addAll(newItems)
        items = newList
        // 设置新增项目的展开状态为折叠
        newItems.forEach { expandStateMap[it.id] = false }
        notifyItemRangeInserted(oldSize, newItems.size)
    }
    
    fun setOnImageClickListener(listener: (String) -> Unit) {
        this.onImageClickListener = listener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_return_history, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.bind(item)
    }

    override fun getItemCount(): Int = items.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvOrderCode: TextView = itemView.findViewById(R.id.tv_order_code)
        private val tvStatus: TextView = itemView.findViewById(R.id.tv_status)
        private val tvCreatedTime: TextView = itemView.findViewById(R.id.tv_created_time)
        private val rvItems: RecyclerView = itemView.findViewById(R.id.rv_items)
        private val detailAdapter = ReturnHistoryDetailAdapter()
        
        // 添加展开/折叠按钮
        private val btnExpandCollapse: TextView = itemView.findViewById(R.id.btn_expand_collapse)
        private val tvItemCount: TextView = itemView.findViewById(R.id.tv_item_count)

        init {
            rvItems.adapter = detailAdapter
            
            // 转发图片点击事件
            detailAdapter.setOnImageClickListener { url ->
                onImageClickListener?.invoke(url)
            }
            
            // 设置展开/折叠按钮点击事件
            btnExpandCollapse.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val record = items[position]
                    // 切换展开状态
                    val isExpanded = expandStateMap[record.id] ?: false
                    expandStateMap[record.id] = !isExpanded
                    // 更新视图
                    updateExpandState(record)
                }
            }
        }

        fun bind(item: ReturnHistoryItem) {
            tvOrderCode.text = "退料单号: ${item.code}"
            tvCreatedTime.text = "申请时间: ${item.createdTime}"
            
            // 设置状态文本和颜色
            setStatusView(item.status)
            
            // 设置物品总数
            val totalItems = item.detailList.size
            tvItemCount.text = "共${totalItems}种耗材"
            
            // 更新展开/折叠状态
            updateExpandState(item)
        }
        
        /**
         * 更新展开/折叠状态
         */
        private fun updateExpandState(record: ReturnHistoryItem) {
            val isExpanded = expandStateMap[record.id] ?: false
            
            if (isExpanded) {
                // 展开状态 - 显示全部物品
                btnExpandCollapse.text = "收起"
                detailAdapter.submitList(record.detailList)
            } else {
                // 折叠状态 - 只显示前3个物品
                btnExpandCollapse.text = "展开全部"
                val detailsToShow = if (record.detailList.size > 3) {
                    record.detailList.take(3)
                } else {
                    record.detailList
                }
                detailAdapter.submitList(detailsToShow)
            }
            
            // 物品列表非空时始终显示展开/折叠按钮
            btnExpandCollapse.visibility = if (record.detailList.isEmpty()) {
                View.GONE
            } else {
                View.VISIBLE
            }
            
            // 是否显示物品列表
            rvItems.visibility = if (record.detailList.isNotEmpty()) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
        
        /**
         * 设置状态视图
         */
        private fun setStatusView(status: String) {
            val context = itemView.context
            
            // 根据状态设置文本和背景色
            when (status) {
                "CREATE" -> {
                    tvStatus.text = "待处理"
                    tvStatus.backgroundTintList = ContextCompat.getColorStateList(context, R.color.status_warning)
                }
                "WAIT_IN_WAREHOUSE" -> {
                    tvStatus.text = "待入库"
                    tvStatus.backgroundTintList = ContextCompat.getColorStateList(context, R.color.status_primary)
                }
                "REJECT" -> {
                    tvStatus.text = "已驳回"
                    tvStatus.backgroundTintList = ContextCompat.getColorStateList(context, R.color.status_error)
                }
                "DONE" -> {
                    tvStatus.text = "已完成"
                    tvStatus.backgroundTintList = ContextCompat.getColorStateList(context, R.color.status_success)
                }
                else -> {
                    tvStatus.text = "未知状态"
                    tvStatus.backgroundTintList = ContextCompat.getColorStateList(context, R.color.status_secondary)
                }
            }
        }
    }
} 
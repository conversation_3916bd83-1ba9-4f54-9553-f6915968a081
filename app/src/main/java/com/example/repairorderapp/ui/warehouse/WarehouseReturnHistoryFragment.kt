package com.example.repairorderapp.ui.warehouse

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.databinding.FragmentWarehouseReturnHistoryBinding
import com.example.repairorderapp.model.PagedData
import com.example.repairorderapp.model.warehouse.ReturnHistoryItem
import com.example.repairorderapp.model.warehouse.WarehouseApplyRecord
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 退料记录页面
 */
class WarehouseReturnHistoryFragment : Fragment() {

    private var _binding: FragmentWarehouseReturnHistoryBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var adapter: ReturnHistoryAdapter
    private var currentPage = 1
    private val pageSize = 10
    private var isLoading = false
    private var isLastPage = false
    private var isFragmentDestroyed = false
    
    companion object {
        private const val TAG = "ReturnHistoryFragment"
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWarehouseReturnHistoryBinding.inflate(inflater, container, false)
        isFragmentDestroyed = false
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupToolbar()
        setupRecyclerView()
        
        // 加载退料记录
        loadReturnHistory()
    }
    
    /**
     * 设置工具栏
     */
    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }
    }
    
    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = ReturnHistoryAdapter()
        adapter.setOnImageClickListener { imageUrl ->
            showImagePreview(imageUrl)
        }
        
        binding.recyclerView.adapter = adapter
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // 添加滚动监听器实现分页加载
        binding.recyclerView.addOnScrollListener(object : PaginationScrollListener(
            binding.recyclerView.layoutManager as LinearLayoutManager
        ) {
            override fun loadMoreItems() {
                isLoading = true
                currentPage++
                loadReturnHistory()
            }
            
            override fun isLastPage(): Boolean = isLastPage
            override fun isLoading(): Boolean = isLoading
        })
        
        // 设置下拉刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            refreshReturnHistory()
        }
    }
    
    /**
     * 刷新退料记录
     */
    private fun refreshReturnHistory() {
        currentPage = 1
        isLastPage = false
        loadReturnHistory()
    }
    
    /**
     * 加载退料记录
     */
    private fun loadReturnHistory() {
        isLoading = true
        // 安全检查
        if (!isAdded || isFragmentDestroyed) return
        
        binding.swipeRefreshLayout.isRefreshing = true
        
        val params = mapOf(
            "pageNumber" to currentPage,
            "pageSize" to pageSize
        )
        
        ApiClient.workOrderApi.getReturnHistory(params).enqueue(object : 
            Callback<ApiResponse<PagedData<WarehouseApplyRecord>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<WarehouseApplyRecord>>>,
                response: Response<ApiResponse<PagedData<WarehouseApplyRecord>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                binding.swipeRefreshLayout.isRefreshing = false
                isLoading = false
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.let { pagedData ->
                        // 检查是否已经加载了所有页
                        isLastPage = currentPage * pageSize >= pagedData.total
                        
                        // 将通用记录转换为退料记录模型
                        val historyItems = pagedData.rows?.map { record ->
                            ReturnHistoryItem(
                                id = record.id,
                                code = record.code ?: "",
                                // 从API响应中获取status字段
                                status = record.status ?: "",
                                createdTime = record.createdAt ?: "",
                                // 使用API响应中的applyReturnDetailList字段
                                detailList = record.applyReturnDetailList?.map { detail ->
                                    com.example.repairorderapp.model.warehouse.ReturnHistoryDetail(
                                        id = detail.id ?: "",
                                        num = detail.num ?: 0,
                                        auditNum = detail.auditNum ?: 0,
                                        // 直接使用detail的itemStore字段
                                        item = detail.itemStore
                                    )
                                } ?: emptyList()
                            )
                        } ?: emptyList()
                        
                        if (historyItems.isNotEmpty()) {
                            binding.recyclerView.visibility = View.VISIBLE
                            binding.emptyView.visibility = View.GONE
                            
                            // 如果是刷新或第一页，直接替换列表
                            if (currentPage == 1) {
                                adapter.submitList(historyItems)
                            } else {
                                // 否则添加到现有列表
                                adapter.addItems(historyItems)
                            }
                        } else {
                            // 没有数据，显示空视图
                            if (currentPage == 1) {
                                binding.recyclerView.visibility = View.GONE
                                binding.emptyView.visibility = View.VISIBLE
                            }
                        }
                    }
                } else {
                    // 请求失败
                    context?.let {
                        Toast.makeText(it, "加载失败", Toast.LENGTH_SHORT).show()
                    }
                    if (currentPage == 1) {
                        binding.recyclerView.visibility = View.GONE
                        binding.emptyView.visibility = View.VISIBLE
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<WarehouseApplyRecord>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                binding.swipeRefreshLayout.isRefreshing = false
                isLoading = false
                
                // 安全地使用context
                context?.let {
                    Toast.makeText(it, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                }
                
                if (currentPage == 1) {
                    binding.recyclerView.visibility = View.GONE
                    binding.emptyView.visibility = View.VISIBLE
                }
            }
        })
    }
    
    /**
     * 显示图片预览
     */
    private fun showImagePreview(imageUrl: String) {
        val dialog = ImagePreviewDialogFragment.newInstance(imageUrl)
        dialog.show(childFragmentManager, "image_preview")
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        isFragmentDestroyed = true
        _binding = null
    }
}
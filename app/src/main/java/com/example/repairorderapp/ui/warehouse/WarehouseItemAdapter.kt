package com.example.repairorderapp.ui.warehouse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.repairorderapp.R

class WarehouseItemAdapter(
    private val items: MutableList<Map<String, Any>>
) : RecyclerView.Adapter<WarehouseItemAdapter.ViewHolder>() {

    // 图片点击监听器
    private var onImageClickListener: ((String) -> Unit)? = null

    // 设置图片点击监听器
    fun setOnImageClickListener(listener: (String) -> Unit) {
        onImageClickListener = listener
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val imgGoods: ImageView = view.findViewById(R.id.img_goods)
        val textArticleName: TextView = view.findViewById(R.id.text_article_name)
        val textArticleCode: TextView = view.findViewById(R.id.text_article_code)
        val textOemNumber: TextView = view.findViewById(R.id.text_oem_number)
        val textUserName: TextView = view.findViewById(R.id.text_user_name)
        val textTypeLabel: TextView = view.findViewById(R.id.text_type_label)
        val textManufacturerChannelLabel: TextView = view.findViewById(R.id.text_manufacturer_channel_label)
        val textNum: TextView = view.findViewById(R.id.text_num)
        val textSaleUnitPrice: TextView = view.findViewById(R.id.text_sale_unit_price)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_warehouse_goods, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount() = items.size

    /**
     * 添加新的物品数据到列表
     * @param newItems 要添加的新物品列表
     */
    fun addItems(newItems: List<Map<String, Any>>) {
        val startPosition = items.size
        items.addAll(newItems)
        notifyItemRangeInserted(startPosition, newItems.size)
    }

    /**
     * 清除所有物品数据
     */
    fun clearItems() {
        items.clear()
        notifyDataSetChanged()
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]

        // 设置物品名称
        val articleName = item["articleName"]?.toString() ?: "未知物品"
        holder.textArticleName.text = articleName

        // 设置物品编号
        val articleCode = item["articleCode"]?.toString() ?: ""
        holder.textArticleCode.text = "物品编号: $articleCode"

        // 设置OEM编号
        val oem = item["oemNumber"]?.toString() ?: ""
        holder.textOemNumber.text = "OEM编号: $oem"

        // 设置归属人信息
        val userName = item["userName"]?.toString() ?: ""
        holder.textUserName.text = "归属人: $userName"

        // 设置物品类型
        val type = (item["type"] as? Map<*, *>)?.get("label")?.toString() ?: "未知类型"
        holder.textTypeLabel.text = "类型: $type"

        // 获取skuInfo数据
        val skuInfo = item["skuInfo"] as? Map<*, *>
        
        // 设置规格信息 (原来的渠道)
        var specValue = "未知规格"
        if (skuInfo != null) {
            val saleAttrVals = skuInfo["saleAttrVals"] as? List<*>
            if (saleAttrVals != null && saleAttrVals.isNotEmpty()) {
                val specValues = mutableListOf<String>()
                for (attrVal in saleAttrVals) {
                    val attr = attrVal as? Map<*, *>
                    if (attr != null) {
                        val attrVal = attr["val"]?.toString() ?: ""
                        if (attrVal.isNotEmpty()) {
                            specValues.add("$attrVal")
                        }
                    }
                }
                specValue = if (specValues.isNotEmpty()) {
                    specValues.joinToString("/")
                } else {
                    "未知规格"
                }
            }
        }
        holder.textManufacturerChannelLabel.text = "$specValue"

        // 设置数量
        val num = item["num"]?.toString()?.toDoubleOrNull()?.toInt() ?: 0
        holder.textNum.text = "数量: $num"

        // 设置单价
        val saleUnitPrice = item["saleUnitPrice"]?.toString()?.toDoubleOrNull() ?: 0.0
        holder.textSaleUnitPrice.text = "单价: ¥${String.format("%.2f", saleUnitPrice)}"

        // 加载物品图片 - 使用skuInfo中的picUrl
        var imageUrl: String? = null
        if (skuInfo != null) {
            val picUrls = skuInfo["picUrl"] as? List<*>
            if (picUrls != null && picUrls.isNotEmpty()) {
                val firstImage = picUrls[0] as? Map<*, *>
                imageUrl = firstImage?.get("url")?.toString()

                if (!imageUrl.isNullOrEmpty()) {
                    Glide.with(holder.imgGoods.context)
                        .load(imageUrl)
                        .placeholder(R.drawable.ic_image_placeholder)
                        .error(R.drawable.ic_image_placeholder)
                        .into(holder.imgGoods)
                } else {
                    holder.imgGoods.setImageResource(R.drawable.ic_image_placeholder)
                }
            } else {
                holder.imgGoods.setImageResource(R.drawable.ic_image_placeholder)
            }
        } else {
            holder.imgGoods.setImageResource(R.drawable.ic_image_placeholder)
        }

        // 设置图片点击事件
        holder.imgGoods.setOnClickListener {
            if (!imageUrl.isNullOrEmpty()) {
                onImageClickListener?.invoke(imageUrl)
            }
        }
    }
}
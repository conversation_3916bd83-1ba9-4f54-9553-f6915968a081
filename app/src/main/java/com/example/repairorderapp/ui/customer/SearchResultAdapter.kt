package com.example.repairorderapp.ui.customer

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.repairorderapp.R
import com.example.repairorderapp.util.TencentMapSearchUtil

/**
 * 地点搜索结果适配器
 */
class SearchResultAdapter(
    private val onItemClick: (TencentMapSearchUtil.PlaceInfo) -> Unit
) : RecyclerView.Adapter<SearchResultAdapter.ViewHolder>() {

    private var places = mutableListOf<TencentMapSearchUtil.PlaceInfo>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvTitle: TextView = itemView.findViewById(R.id.tvPlaceTitle)
        val tvAddress: TextView = itemView.findViewById(R.id.tvPlaceAddress)
        val tvCategory: TextView = itemView.findViewById(R.id.tvPlaceCategory)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_search_result, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val place = places[position]
        
        holder.tvTitle.text = place.title
        holder.tvAddress.text = place.address
        
        // 显示分类信息
        if (!place.category.isNullOrEmpty()) {
            holder.tvCategory.text = place.category
            holder.tvCategory.visibility = android.view.View.VISIBLE
        } else {
            holder.tvCategory.visibility = android.view.View.GONE
        }
        
        // 设置点击事件
        holder.itemView.setOnClickListener {
            onItemClick(place)
        }
    }

    override fun getItemCount(): Int = places.size

    fun updatePlaces(newPlaces: List<TencentMapSearchUtil.PlaceInfo>) {
        places.clear()
        places.addAll(newPlaces)
        notifyDataSetChanged()
    }

    fun clearPlaces() {
        places.clear()
        notifyDataSetChanged()
    }
} 
package com.example.repairorderapp.ui.login

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.util.Base64
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.example.repairorderapp.MainActivity
import com.example.repairorderapp.R
import com.example.repairorderapp.data.model.Result
import com.example.repairorderapp.data.model.User
import com.example.repairorderapp.databinding.ActivityLoginBinding
import com.example.repairorderapp.network.ApiClient
import com.example.repairorderapp.network.service.LoginService
import com.example.repairorderapp.network.TokenManager
import com.example.repairorderapp.utils.PermissionManager
import com.example.repairorderapp.utils.PermissionUtils
import kotlinx.coroutines.launch
import okhttp3.ResponseBody
import org.json.JSONObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.math.BigInteger
import java.security.KeyFactory
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher
import org.bouncycastle.crypto.engines.SM2Engine
import org.bouncycastle.crypto.params.ECDomainParameters
import org.bouncycastle.crypto.params.ECPublicKeyParameters
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.jce.spec.ECParameterSpec
import org.bouncycastle.math.ec.ECCurve
import org.bouncycastle.math.ec.ECPoint
import org.bouncycastle.util.encoders.Hex
import java.security.Security
import java.nio.charset.StandardCharsets
import org.json.JSONArray
import android.content.pm.PackageManager
import android.widget.TextView

class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding
    private lateinit var preferences: SharedPreferences
    private lateinit var loginViewModel: LoginViewModel
    private var captchaToken: String = ""
    private var passwordToken: String = ""
    private var publicKey: String = ""
    private var btnLoading = false

    companion object {
        private const val PREF_NAME = "login_pref"
        private const val KEY_USERNAME = "username"
        private const val KEY_PASSWORD = "password"
        private const val KEY_REMEMBER_PASSWORD = "remember_password"
        private const val TAG = "LoginActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 调试服务器连接
        debugServerConnection()

        // 初始化ViewModel
        loginViewModel = ViewModelProvider(this)[LoginViewModel::class.java]

        // 初始化SharedPreferences
        preferences = getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

        // 初始化界面
        initView()
        
        // 获取验证码
        getCaptcha()
        
        // 观察登录结果
        observeLoginResult()

        // 检查是否是因为会话过期跳转过来的
        if (intent.getBooleanExtra("session_expired", false)) {
            findViewById<TextView>(R.id.error_message_text).apply {
                visibility = View.VISIBLE
                text = "登录会话已过期，请重新登录"
            }
        }
    }

    private fun debugServerConnection() {
        // 测试服务器基本连接
        Thread {
            try {
                val url1 = java.net.URL("https://plat.sczjzy.com.cn")
                val url2 = java.net.URL("https://plat.sczjzy.com.cn/api/magina/anno/captcha")
                
                try {
                    Log.d("ServerTest", "开始测试连接: $url1")
                    val connection1 = url1.openConnection() as java.net.HttpURLConnection
                    connection1.connectTimeout = 5000
                    connection1.readTimeout = 5000
                    connection1.requestMethod = "GET"
                    val responseCode1 = connection1.responseCode
                    Log.d("ServerTest", "服务器连接测试1结果: $responseCode1")
                } catch (e: Exception) {
                    Log.e("ServerTest", "服务器连接测试1失败: ${e.message}", e)
                }
                
                try {
                    Log.d("ServerTest", "开始测试连接: $url2")
                    val connection2 = url2.openConnection() as java.net.HttpURLConnection
                    connection2.connectTimeout = 5000
                    connection2.readTimeout = 5000
                    connection2.requestMethod = "GET"
                    val responseCode2 = connection2.responseCode
                    Log.d("ServerTest", "服务器连接测试2结果: $responseCode2")
                } catch (e: Exception) {
                    Log.e("ServerTest", "服务器连接测试2失败: ${e.message}", e)
                }
            } catch (e: Exception) {
                Log.e("ServerTest", "服务器连接线程异常: ${e.message}", e)
            }
        }.start()
    }

    private fun initView() {
        // 恢复保存的账号和密码
        val savedUsername = preferences.getString(KEY_USERNAME, "")
        val savedPassword = preferences.getString(KEY_PASSWORD, "")
        val rememberPassword = preferences.getBoolean(KEY_REMEMBER_PASSWORD, false)
        
        binding.etUsername.setText(savedUsername)
        if (rememberPassword && !savedPassword.isNullOrEmpty()) {
            binding.etPassword.setText(savedPassword)
            binding.cbRemember.isChecked = true
        }

        // 不再需要返回按钮
        // binding.btnBack.setOnClickListener {
        //    finish()
        // }
        
        // 设置密码可见性切换
        binding.btnTogglePassword.setOnClickListener {
            togglePasswordVisibility()
        }

        // 设置记住我勾选框
        binding.cbRemember.setOnClickListener {
            if (!binding.cbRemember.isChecked) {
                // 如果取消勾选，清除保存的密码
                preferences.edit()
                    .remove(KEY_PASSWORD)
                    .putBoolean(KEY_REMEMBER_PASSWORD, false)
                    .apply()
            }
        }

        // 设置协议勾选框
        binding.cbAgreement.setOnCheckedChangeListener { _, isChecked ->
            binding.btnLogin.isEnabled = isChecked
        }

        // 设置协议文本点击
        binding.tvProtocol.setOnClickListener {
            // 打开协议页面
            Toast.makeText(this, R.string.app_protocol, Toast.LENGTH_SHORT).show()
        }

        // 设置验证码图片点击刷新
        binding.ivCaptcha.setOnClickListener {
            getCaptcha()
        }

        // 设置登录按钮
        binding.btnLogin.setOnClickListener {
            if (validateInputs()) {
                login()
            }
        }
    }

    private fun togglePasswordVisibility() {
        val isPasswordVisible = binding.etPassword.inputType != android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD or android.text.InputType.TYPE_CLASS_TEXT
        
        if (isPasswordVisible) {
            // 切换为密码隐藏状态
            binding.etPassword.inputType = android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD or android.text.InputType.TYPE_CLASS_TEXT
            binding.btnTogglePassword.setImageResource(R.drawable.ic_visibility_off)
        } else {
            // 切换为密码明文状态
            binding.etPassword.inputType = android.text.InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD or android.text.InputType.TYPE_CLASS_TEXT
            binding.btnTogglePassword.setImageResource(R.drawable.ic_visibility)
        }
        
        // 保持光标位置在文本末尾
        val selection = binding.etPassword.text.length
        binding.etPassword.setSelection(selection)
    }

    private fun getCaptcha() {
        // 显示加载中
        binding.pbCaptchaLoading.visibility = View.VISIBLE
        binding.ivCaptcha.visibility = View.INVISIBLE
        
        Log.d("LoginActivity", "开始获取验证码...")
        // 请求验证码 - 完全按照UNI-APP实现
        ApiClient.loginService.getCaptcha().enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                try {
                    if (response.isSuccessful && response.body() != null) {
                        // 读取完整的响应内容
                        val responseString = response.body()?.string() ?: "{}"
                        Log.d("LoginActivity", "验证码原始响应长度: ${responseString.length}")
                        
                        val jsonObject = JSONObject(responseString)
                        val data = jsonObject.optJSONObject("data")
                        if (data != null) {
                            captchaToken = data.getString("first")
                            var captchaBase64 = data.getString("second")
                            
                            // 检查并修复Base64字符串
                            if (captchaBase64.startsWith("data:image/")) {
                                // 提取实际的Base64内容
                                captchaBase64 = captchaBase64.substring(captchaBase64.indexOf(",") + 1)
                            }
                            
                            Log.d("LoginActivity", "验证码token: $captchaToken")
                            Log.d("LoginActivity", "Base64字符串前20个字符: ${captchaBase64.take(20)}")
                            Log.d("LoginActivity", "完整Base64长度: ${captchaBase64.length}")
                            
                            try {
                                // 解码Base64
                                val decodedBytes = Base64.decode(captchaBase64, Base64.DEFAULT)
                                Log.d("LoginActivity", "解码后字节数: ${decodedBytes.size}")
                                
                                // 创建位图
                                val options = android.graphics.BitmapFactory.Options().apply {
                                    inJustDecodeBounds = false
                                }
                                val captchaBitmap = android.graphics.BitmapFactory.decodeByteArray(
                                    decodedBytes, 
                                    0, 
                                    decodedBytes.size,
                                    options
                                )
                                
                                if (captchaBitmap != null) {
                                    Log.d("LoginActivity", "验证码图片解码成功: ${captchaBitmap.width}x${captchaBitmap.height}")
                                    binding.pbCaptchaLoading.visibility = View.GONE
                                    binding.ivCaptcha.visibility = View.VISIBLE
                                    binding.ivCaptcha.setImageBitmap(captchaBitmap)
                                } else {
                                    throw Exception("验证码图片解码失败")
                                }
                            } catch (e: IllegalArgumentException) {
                                Log.e("LoginActivity", "Base64解码失败", e)
                                throw Exception("Base64解码失败: ${e.message}")
                            } catch (e: Exception) {
                                Log.e("LoginActivity", "图片处理失败", e)
                                throw e
                            }
                        } else {
                            throw Exception("响应中缺少data字段")
                        }
                    } else {
                        throw Exception("验证码请求失败: ${response.code()}")
                    }
                } catch (e: Exception) {
                    Log.e("LoginActivity", "验证码处理异常: ${e.message}", e)
                    binding.pbCaptchaLoading.visibility = View.GONE
                    binding.ivCaptcha.visibility = View.VISIBLE
                    binding.ivCaptcha.setImageResource(R.drawable.ic_captcha_error)
                    Toast.makeText(this@LoginActivity, "验证码获取失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    
                    // 延迟1秒后自动重试
                    binding.ivCaptcha.postDelayed({
                        getCaptcha()
                    }, 1000)
                }
            }

            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                Log.e("LoginActivity", "验证码网络请求失败", t)
                binding.pbCaptchaLoading.visibility = View.GONE
                binding.ivCaptcha.visibility = View.VISIBLE
                binding.ivCaptcha.setImageResource(R.drawable.ic_captcha_error)
                Toast.makeText(this@LoginActivity, R.string.network_error, Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun getEncryptionKey() {
        Log.d("LoginActivity", "开始获取加密密钥...")
        // 请求加密密钥 - 完全按照UNI-APP实现
        ApiClient.loginService.getEncryptionKey().enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                if (response.isSuccessful) {
                    try {
                        val responseString = response.body()?.string() ?: "{}"
                        Log.d("LoginActivity", "密钥响应: $responseString")
                        val jsonObject = JSONObject(responseString)
                        
                        // 与UNI-APP一致，检查code，仅在200时处理数据
                        if (jsonObject.optInt("code") == 200) {
                            val data = jsonObject.getJSONObject("data")
                            passwordToken = data.getString("first")
                            publicKey = data.getString("second")
                            Log.d("LoginActivity", "密钥token: $passwordToken")
                            Log.d("LoginActivity", "公钥: $publicKey")
                            
                            // 获取密钥后继续登录流程
                            proceedWithLogin()
                        } else {
                            Log.e("LoginActivity", "密钥接口返回非200状态: ${jsonObject.optInt("code")}")
                            btnLoading = false
                            updateLoadingState()
                            val message = jsonObject.optString("msg", getString(R.string.login_failed))
                            Toast.makeText(this@LoginActivity, message, Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Log.e("LoginActivity", "密钥数据解析失败", e)
                        btnLoading = false
                        updateLoadingState()
                        Toast.makeText(this@LoginActivity, R.string.login_failed, Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Log.e("LoginActivity", "密钥获取失败: ${response.code()}")
                    btnLoading = false
                    updateLoadingState()
                    Toast.makeText(this@LoginActivity, R.string.login_failed, Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                Log.e("LoginActivity", "密钥网络请求失败", t)
                btnLoading = false
                updateLoadingState()
                Toast.makeText(this@LoginActivity, R.string.network_error, Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun proceedWithLogin() {
        try {
            val username = binding.etUsername.text.toString()
            val password = binding.etPassword.text.toString()
            val captcha = binding.etCaptcha.text.toString()
            
            Log.d("LoginActivity", "开始登录流程...")
            Log.d("LoginActivity", "用户名: $username")
            Log.d("LoginActivity", "验证码: $captcha")
            Log.d("LoginActivity", "验证码token: $captchaToken")
            Log.d("LoginActivity", "密码token: $passwordToken")
            
            // 加密密码
            val encryptedPassword = encryptPassword(password)
            Log.d("LoginActivity", "密码加密完成")
            
            // 构建登录请求参数，与UNI-APP保持一致
            val params = HashMap<String, String>()
            params["code"] = username
            params["password"] = encryptedPassword
            params["captcha"] = captcha
            params["captchaToken"] = captchaToken
            params["passwordToken"] = passwordToken
            
            Log.d("LoginActivity", "发送登录请求...")
            // 发送登录请求
            ApiClient.loginService.login(params).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    btnLoading = false
                    updateLoadingState()
                    
                    try {
                        if (response.isSuccessful) {
                            val responseString = response.body()?.string() ?: "{}"
                            Log.d("LoginActivity", "登录响应: $responseString")
                            val jsonObject = JSONObject(responseString)
                            
                            if (jsonObject.optInt("code") == 200) {
                                // 登录成功
                                Log.d("LoginActivity", "登录成功")
                                Toast.makeText(this@LoginActivity, R.string.login_success, Toast.LENGTH_SHORT).show()
                                
                                // 保存用户名
                                if (binding.cbRemember.isChecked) {
                                    preferences.edit().putString(KEY_USERNAME, username).apply()
                                } else {
                                    preferences.edit().remove(KEY_USERNAME).apply()
                                }
                                
                                // 保存登录令牌
                                val data = jsonObject.getJSONObject("data")
                                saveLoginToken(data)
                                
                                // 获取用户权限 - 所有跳转都由这个方法处理
                                getUserPermissions()
                            } else {
                                // 登录失败，根据错误码提供更精确的错误信息
                                val errorCode = jsonObject.optInt("code")
                                val message = jsonObject.optString("message", jsonObject.optString("msg", getString(R.string.login_failed)))
                                
                                // 记录详细错误信息
                                Log.e("LoginActivity", "登录失败: 错误码=$errorCode, 错误信息=$message")
                                
                                // 根据常见错误码和错误消息分类显示错误
                                val errorMessage = when {
                                    // 错误码406特殊处理，根据消息内容区分验证码错误还是账号密码错误
                                    errorCode == 406 && (message.contains("验证码") || message.contains("captcha")) -> "验证码错误，请重新输入"
                                    errorCode == 406 && (message.contains("账号") || message.contains("帐号") || message.contains("密码")) -> getString(R.string.account_or_password_error)
                                    message.contains("验证码") || message.contains("captcha") -> "验证码错误，请重新输入"
                                    message.contains("密码") || message.contains("password") -> getString(R.string.password_error)
                                    message.contains("账号") || message.contains("帐号") || message.contains("用户") || message.contains("user") -> getString(R.string.account_error)
                                    message.contains("服务器") || errorCode in 500..599 -> getString(R.string.server_error)
                                    else -> getString(R.string.login_failed) // 使用通用错误提示
                                }
                                
                                // 在验证码错误的情况下，清空验证码输入框并自动聚焦
                                if ((errorCode == 406 && (message.contains("验证码") || message.contains("captcha"))) 
                                    || message.contains("验证码") || message.contains("captcha")) {
                                    binding.etCaptcha.setText("")
                                    binding.etCaptcha.requestFocus()
                                }
                                
                                // 显示友好的错误消息，同时包含具体错误
                                showErrorDialog(errorMessage, message)
                                getCaptcha() // 刷新验证码
                            }
                        } else {
                            // HTTP错误
                            Log.e("LoginActivity", "登录请求失败: HTTP ${response.code()}")
                            val errorMessage = when (response.code()) {
                                401 -> getString(R.string.account_error)
                                403 -> getString(R.string.password_error)
                                404 -> "服务接口不存在"
                                408 -> getString(R.string.connection_timeout)
                                500, 502, 503, 504 -> getString(R.string.server_error)
                                else -> getString(R.string.network_error)
                            }
                            
                            // 尝试获取错误响应体
                            val errorBody = response.errorBody()?.string() ?: ""
                            Log.e("LoginActivity", "错误响应: $errorBody")
                            
                            showErrorDialog(errorMessage, "HTTP ${response.code()}: ${response.message()}")
                            getCaptcha() // 刷新验证码
                        }
                    } catch (e: Exception) {
                        Log.e("LoginActivity", "登录响应处理失败", e)
                        showErrorDialog(getString(R.string.login_failed), e.message ?: "未知错误")
                        getCaptcha() // 刷新验证码
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    Log.e("LoginActivity", "登录网络请求失败", t)
                    btnLoading = false
                    updateLoadingState()
                    
                    // 根据异常类型提供更详细的错误信息
                    val errorMessage = when {
                        t is java.net.SocketTimeoutException -> getString(R.string.connection_timeout)
                        t is java.net.UnknownHostException -> "服务器地址无法访问"
                        t is java.net.ConnectException -> "无法连接到服务器"
                        else -> getString(R.string.network_error)
                    }
                    
                    showErrorDialog(errorMessage, t.message ?: "")
                }
            })
        } catch (e: Exception) {
            Log.e("LoginActivity", "登录过程异常", e)
            btnLoading = false
            updateLoadingState()
            showErrorDialog("登录失败", e.message ?: "未知错误")
        }
    }

    private fun encryptPassword(password: String): String {
        try {
            Log.d("LoginActivity", "开始使用SM2加密密码...")
            
            // 确保公钥不为空
            if (publicKey.isEmpty()) {
                throw Exception("公钥为空")
            }
            
            // 移除可能的头尾空白字符
            val trimmedKey = publicKey.trim()
            Log.d("LoginActivity", "处理后的公钥长度: ${trimmedKey.length}, 公钥前10个字符: ${trimmedKey.take(10)}")
            
            // 注册BouncyCastle提供者
            Security.addProvider(BouncyCastleProvider())
            
            // 准备SM2加密所需参数 - 与UNI-APP一致，在数据前添加字符"0"
            val dataToEncrypt = if (password.isNotEmpty()) "0$password" else "0"
            Log.d("LoginActivity", "加密前数据长度: ${dataToEncrypt.length}")
            
            // 使用SM2加密
            val encryptedData = sm2Encrypt(dataToEncrypt, trimmedKey)
            
            Log.d("LoginActivity", "密码加密完成，加密后长度: ${encryptedData.length}, 加密后前20个字符: ${encryptedData.take(20)}")
            return encryptedData
            
        } catch (e: Exception) {
            Log.e("LoginActivity", "密码加密失败", e)
            Log.e("LoginActivity", "公钥内容: $publicKey")
            throw Exception("密码加密失败: ${e.message}")
        }
    }
    
    /**
     * SM2加密实现 - 基于BouncyCastle库
     */
    private fun sm2Encrypt(data: String, publicKeyHex: String): String {
        try {
            // 确保公钥格式正确
            var pubKeyHex = publicKeyHex
            if (!pubKeyHex.startsWith("04")) {
                Log.d("LoginActivity", "公钥不以04开头，添加前缀")
                pubKeyHex = "04$pubKeyHex"
            }
            Log.d("LoginActivity", "格式化后的公钥前10个字符: ${pubKeyHex.take(10)}")
            
            // 解析SM2公钥
            // 使用固定的SM2曲线参数，而不是依赖GMNamedCurves
            val p = BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF", 16)
            val a = BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC", 16)
            val b = BigInteger("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93", 16)
            val n = BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123", 16)
            val h = BigInteger.ONE
            val x = BigInteger("32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7", 16)
            val y = BigInteger("BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0", 16)
            
            Log.d("LoginActivity", "创建SM2曲线参数")
            val curve = ECCurve.Fp(p, a, b)
            val g = curve.createPoint(x, y)
            val domainParams = ECDomainParameters(curve, g, n, h)
            
            // 把十六进制公钥转换为ECPoint
            try {
                Log.d("LoginActivity", "解码公钥")
                val pubKeyBytes = Hex.decode(pubKeyHex)
                Log.d("LoginActivity", "公钥字节长度: ${pubKeyBytes.size}")
                val pubKeyPoint = curve.decodePoint(pubKeyBytes)
                
                // 创建SM2加密引擎
                Log.d("LoginActivity", "创建SM2加密引擎")
                val pubKeyParams = ECPublicKeyParameters(pubKeyPoint, domainParams)
                val sm2Engine = SM2Engine(SM2Engine.Mode.C1C3C2)
                sm2Engine.init(true, org.bouncycastle.crypto.params.ParametersWithRandom(
                    pubKeyParams, 
                    java.security.SecureRandom()
                ))
                
                // 加密数据
                Log.d("LoginActivity", "执行SM2加密")
                val inputData = data.toByteArray(StandardCharsets.UTF_8)
                val ciphertext = sm2Engine.processBlock(inputData, 0, inputData.size)
                
                // 返回十六进制表示
                Log.d("LoginActivity", "加密完成，密文长度: ${ciphertext.size}")
                return Hex.toHexString(ciphertext)
            } catch (e: Exception) {
                Log.e("LoginActivity", "公钥处理或加密过程失败", e)
                throw Exception("公钥处理或加密失败: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e("LoginActivity", "SM2加密失败", e)
            throw e
        }
    }

    private fun saveLoginCredentials() {
        val username = binding.etUsername.text.toString()
        val password = binding.etPassword.text.toString()
        val rememberPassword = binding.cbRemember.isChecked
        
        val editor = preferences.edit()
        
        // 总是保存用户名
        editor.putString(KEY_USERNAME, username)
        
        // 根据"记住我"选项决定是否保存密码
        if (rememberPassword) {
            editor.putString(KEY_PASSWORD, password)
            editor.putBoolean(KEY_REMEMBER_PASSWORD, true)
        } else {
            editor.remove(KEY_PASSWORD)
            editor.putBoolean(KEY_REMEMBER_PASSWORD, false)
        }
        
        editor.apply()
        Log.d("LoginActivity", "保存登录凭据 - 用户名: $username, 记住密码: $rememberPassword")
    }

    private fun saveLoginToken(data: JSONObject) {
        try {
            val tokenPrefs = getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            val editor = tokenPrefs.edit()
            
            
            // 获取令牌信息
            val token = data.optString("token", "")
            val refreshToken = data.optString("refreshToken", "")
            // 正确解析user对象
            val userObj = data.optJSONObject("user")
            val userId = userObj?.optString("id", "") ?: ""  // 修复：使用id而不是code作为userId
            val userCode = userObj?.optString("code", "") ?: ""  // 用户编码，如B0000003
            val userName = userObj?.optString("name", "") ?: ""
            val engineerId = userId  // 工程师ID就是用户ID
            
            // 日志记录
            Log.d("LoginActivity", "保存令牌信息:")
            Log.d("LoginActivity", "- userId: $userId")
            Log.d("LoginActivity", "- userCode: $userCode") 
            Log.d("LoginActivity", "- userName: $userName")
            Log.d("LoginActivity", "- engineerId: $engineerId")
            
            // 设置令牌过期时间（当前时间 + 过期时间）
            // 如果服务器没有返回过期时间，默认设置为7天
            val expiresIn = data.optLong("expires_in", 0)
            val expiresAt = if (expiresIn > 0) {
                System.currentTimeMillis() + (expiresIn * 1000)
            } else {
                // 默认7天过期
                System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000)
            }
            
            Log.d("LoginActivity", "设置令牌过期时间: ${expiresAt}, 当前时间: ${System.currentTimeMillis()}")
            
            // 保存令牌信息到SharedPreferences - 使用accessToken键名与UNI-APP保持一致
            editor.putString("accessToken", token)
            editor.putString("refreshToken", refreshToken)
            editor.putString("userId", userId)
            editor.putString("userCode", userCode)  // 添加保存userCode
            editor.putString("userName", userName)
            editor.putString("engineerId", engineerId)
            editor.putLong("expiresAt", expiresAt)
            editor.commit() // 使用同步提交，确保令牌立即落盘
            
            // 同时保存到SharedPrefsManager，确保位置服务也能访问
            val sharedPrefsManager = com.example.repairorderapp.util.SharedPrefsManager(this)
            sharedPrefsManager.saveUserId(userId)  // 现在保存的是正确的用户ID而不是用户编码
            sharedPrefsManager.saveUserName(userName)
            sharedPrefsManager.saveLoginStatus(true)
            
            // 验证保存结果
            val savedToken = tokenPrefs.getString("accessToken", "")
            Log.d("LoginActivity", "令牌保存验证: ${if (savedToken == token) "成功" else "失败"}")
            
            
            // 保存登录凭据
            saveLoginCredentials()
            
            Log.d("LoginActivity", "登录令牌保存成功")
        } catch (e: Exception) {
            Log.e("LoginActivity", "保存令牌信息异常: ${e.message}", e)
        }
    }

    private fun getUserPermissions() {
        // 显示正在加载权限信息的提示
        Toast.makeText(this, "正在获取用户权限信息...", Toast.LENGTH_SHORT).show()
        
        ApiClient.loginService.getResources().enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                if (response.isSuccessful) {
                    try {
                        val jsonObject = JSONObject(response.body()?.string() ?: "{}")
                        
                        if (jsonObject.optInt("code") == 200) {
                            val data = jsonObject.getJSONArray("data")
                            Log.d("LoginActivity", "获取到权限数据: ${data.length()} 项")
                            
                            // 保存权限信息
                            val permPrefs = getSharedPreferences("perm_pref", Context.MODE_PRIVATE)
                            permPrefs.edit().putString("powerMenu", data.toString()).apply()
                            
                            // 提取用户角色信息
                            val roleSet = mutableSetOf<String>()
                            for (i in 0 until data.length()) {
                                val item = data.optJSONObject(i)
                                val role = item?.optString("role") ?: continue
                                if (role.isNotEmpty()) {
                                    roleSet.add(role)
                                }
                            }
                            
                            // 保存角色信息
                            val tokenPrefs = getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                            tokenPrefs.edit().putStringSet("roles", roleSet).apply()
                            
                            // 使用优化后的 PermissionManager 异步初始化权限
                            val permissionManager = PermissionManager.getInstance(applicationContext)
                            
                            // 添加初始化完成回调，权限加载完成后再跳转
                            permissionManager.initializePermissionsWithCallback {
                                // 启动位置服务
                                startLocationService()

                                // 登录成功后上传设备信息
                                uploadDeviceInfoAfterLogin()

                                // 登录成功后更新远程配置
                                updateConfigAfterLogin()

                                // 跳转到主页
                                Log.d("LoginActivity", "权限加载完成，正在跳转到主页")
                                startActivity(Intent(this@LoginActivity, MainActivity::class.java))
                                finish()
                            }
                        } else {
                            Log.e("LoginActivity", "获取权限失败: ${jsonObject.optString("message")}")
                            // 权限请求失败，不再生成测试数据
                            Toast.makeText(this@LoginActivity, "获取用户权限信息失败，部分功能可能不可用", Toast.LENGTH_SHORT).show()
                            
                            // 即使权限获取失败，也允许用户进入主页
                            startLocationService()

                            // 登录成功后上传设备信息
                            uploadDeviceInfoAfterLogin()

                            // 登录成功后更新远程配置
                            updateConfigAfterLogin()

                            startActivity(Intent(this@LoginActivity, MainActivity::class.java))
                            finish()
                        }
                    } catch (e: Exception) {
                        Log.e("LoginActivity", "解析权限数据异常", e)
                        // 解析异常，不再生成测试数据
                        Toast.makeText(this@LoginActivity, "获取用户权限信息失败，部分功能可能不可用", Toast.LENGTH_SHORT).show()
                        
                        // 即使权限解析失败，也允许用户进入主页
                        startLocationService()

                        // 登录成功后上传设备信息
                        uploadDeviceInfoAfterLogin()

                        // 登录成功后更新远程配置
                        updateConfigAfterLogin()

                        startActivity(Intent(this@LoginActivity, MainActivity::class.java))
                        finish()
                    }
                } else {
                    Log.e("LoginActivity", "获取权限请求失败: ${response.code()} ${response.message()}")
                    // 请求异常，不再生成测试数据
                    Toast.makeText(this@LoginActivity, "获取用户权限信息失败，部分功能可能不可用", Toast.LENGTH_SHORT).show()
                    
                    // 即使权限请求失败，也允许用户进入主页
                    startLocationService()

                    // 登录成功后上传设备信息
                    uploadDeviceInfoAfterLogin()

                    // 登录成功后更新远程配置
                    updateConfigAfterLogin()

                    startActivity(Intent(this@LoginActivity, MainActivity::class.java))
                    finish()
                }
            }

            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                Log.e("LoginActivity", "获取权限网络异常", t)
                Toast.makeText(this@LoginActivity, "获取用户权限信息失败，部分功能可能不可用", Toast.LENGTH_SHORT).show()
                
                // 即使权限获取失败，也允许用户进入主页
                startLocationService()
                startActivity(Intent(this@LoginActivity, MainActivity::class.java))
                finish()
            }
        })
    }
    
    /**
     * 生成测试权限数据（仅用于开发测试）
     */
    private fun generateTestPermissionData() {
        try {
            Log.d("LoginActivity", "生成测试权限数据")
            
            // 创建测试权限数据
            val testPermissions = JSONArray()
            
            // 工单管理权限
            val workPoolPerm = JSONObject()
            workPoolPerm.put("id", "1")
            workPoolPerm.put("path", "/workPool")
            workPoolPerm.put("name", "工单管理")
            testPermissions.put(workPoolPerm)
            
            // 地图权限
            val mapPerm = JSONObject()
            mapPerm.put("id", "2")
            mapPerm.put("path", "/map")
            mapPerm.put("name", "地图")
            testPermissions.put(mapPerm)
            
            // 待接工单权限
            val pendingOrderPerm = JSONObject()
            pendingOrderPerm.put("id", "3")
            pendingOrderPerm.put("path", "/pendingOrder")
            pendingOrderPerm.put("name", "待接工单")
            testPermissions.put(pendingOrderPerm)
            
            // 我的工单权限
            val myWorkOrderPerm = JSONObject()
            myWorkOrderPerm.put("id", "4")
            myWorkOrderPerm.put("path", "/myWorkOrder")
            myWorkOrderPerm.put("name", "我的工单")
            testPermissions.put(myWorkOrderPerm)
            
            // 保存测试权限数据
            val permPrefs = getSharedPreferences("perm_pref", Context.MODE_PRIVATE)
            permPrefs.edit().putString("powerMenu", testPermissions.toString()).apply()
            
            Log.d("LoginActivity", "已保存测试权限数据: $testPermissions")
            Toast.makeText(this, "已加载测试权限数据", Toast.LENGTH_SHORT).show()
            
        } catch (e: Exception) {
            Log.e("LoginActivity", "生成测试权限数据失败", e)
        }
    }

    private fun login() {
        btnLoading = true
        updateLoadingState()
        getEncryptionKey()
    }

    private fun validateInputs(): Boolean {
        val username = binding.etUsername.text.toString()
        val password = binding.etPassword.text.toString()
        val captcha = binding.etCaptcha.text.toString()
        
        if (TextUtils.isEmpty(username)) {
            Toast.makeText(this, R.string.please_input_account, Toast.LENGTH_SHORT).show()
            return false
        }
        
        if (TextUtils.isEmpty(password)) {
            Toast.makeText(this, R.string.please_input_password, Toast.LENGTH_SHORT).show()
            return false
        }
        
        if (TextUtils.isEmpty(captcha)) {
            Toast.makeText(this, R.string.please_input_captcha, Toast.LENGTH_SHORT).show()
            return false
        }
        
        if (!binding.cbAgreement.isChecked) {
            Toast.makeText(this, R.string.please_agree_protocol, Toast.LENGTH_SHORT).show()
            return false
        }
        
        return true
    }

    private fun updateLoadingState() {
        binding.loadingView.visibility = if (btnLoading) View.VISIBLE else View.GONE
        binding.btnLogin.isEnabled = !btnLoading && binding.cbAgreement.isChecked
    }

    private fun observeLoginResult() {
        loginViewModel.loginResult.observe(this) { result ->
            when (result) {
                is Result.Loading -> {
                    // 显示加载中
                    binding.loadingView.visibility = View.VISIBLE
                    binding.btnLogin.isEnabled = false
                }
                is Result.Success -> {
                    // 登录成功
                    binding.loadingView.visibility = View.GONE
                    binding.btnLogin.isEnabled = true

                    // 保存用户名
                    if (binding.cbRemember.isChecked) {
                        preferences.edit().putString(KEY_USERNAME, binding.etUsername.text.toString()).apply()
                    } else {
                        preferences.edit().remove(KEY_USERNAME).apply()
                    }

                    // 启动位置服务
                    startLocationService()

                    // 登录成功后上传设备信息
                    uploadDeviceInfoAfterLogin()

                    // 登录成功后更新远程配置
                    updateConfigAfterLogin()

                    // 跳转到主页
                    startActivity(Intent(this, MainActivity::class.java))
                    finish()
                }
                is Result.Error -> {
                    // 登录失败
                    binding.loadingView.visibility = View.GONE
                    binding.btnLogin.isEnabled = true
                    
                    // 显示错误信息
                    showNetworkErrorDialog(result.exception.message ?: getString(R.string.login_failed))
                    
                    // 刷新验证码
                    refreshCaptcha()
                }
            }
        }
        
        loginViewModel.captchaResult.observe(this) { result ->
            when (result) {
                is Result.Loading -> {
                    // 显示加载中
                    binding.ivCaptcha.setImageResource(android.R.color.darker_gray)
                }
                is Result.Success -> {
                    // 显示验证码图片
                    binding.ivCaptcha.setImageBitmap(result.data)
                }
                is Result.Error -> {
                    // 获取验证码失败
                    binding.ivCaptcha.setImageResource(R.drawable.ic_captcha_error)
                    showNetworkErrorDialog(getString(R.string.captcha_fetch_failed))
                }
            }
        }
    }

    private fun handleLoginResult(result: Result<User>) {
        binding.loadingView.visibility = View.GONE
        when (result) {
            is Result.Success -> {
                // 登录成功，保存用户信息
                val user = result.data
                loginViewModel.saveUserInfo(user)

                // 启动位置服务
                startLocationService()

                // 登录成功后上传设备信息
                uploadDeviceInfoAfterLogin()

                // 登录成功后更新远程配置
                updateConfigAfterLogin()

                // 跳转到主页面
                val intent = Intent(this, MainActivity::class.java)
                startActivity(intent)
                finish()
            }
            is Result.Error -> {
                // 登录失败，显示错误信息
                showNetworkErrorDialog(result.exception.message ?: getString(R.string.login_failed))
            }
            else -> {
                // 加载中，不处理
            }
        }
    }
    
    private fun showNetworkErrorDialog(errorMessage: String) {
        val builder = AlertDialog.Builder(this)
        builder.setTitle(getString(R.string.network_error))
            .setMessage(errorMessage)
            .setPositiveButton(getString(R.string.confirm)) { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }

    private fun refreshCaptcha() {
        loginViewModel.getCaptcha()
    }

    /**
     * 显示详细错误对话框
     */
    private fun showErrorDialog(errorTitle: String, errorDetail: String) {
        // 对于验证码错误和账号密码错误，直接使用Toast提示，无需展示对话框
        if (errorTitle.contains("验证码错误") || 
            errorTitle.contains("账号或密码错误") || 
            errorTitle == getString(R.string.account_or_password_error)) {
            Toast.makeText(this, errorTitle, Toast.LENGTH_LONG).show()
            return
        }
        
        val builder = AlertDialog.Builder(this)
        builder.setTitle(errorTitle)
        
        // 如果有详细错误信息且与标题不同，则显示详细信息
        if (errorDetail.isNotEmpty() && errorDetail != errorTitle) {
            builder.setMessage(errorDetail)
        }
        
        builder.setPositiveButton(getString(R.string.confirm)) { dialog, _ ->
            dialog.dismiss()
        }
        builder.setCancelable(false)
        builder.show()
    }

    /**
     * 登录成功后上传设备信息
     */
    private fun uploadDeviceInfoAfterLogin() {
        try {
            lifecycleScope.launch {
                try {
                    val deviceDataUploadManager = com.example.repairorderapp.manager.DeviceDataUploadManager.getInstance(this@LoginActivity)
                    deviceDataUploadManager.uploadDeviceInfoOnLogin()
                    Log.i(TAG, "登录成功，设备信息上传已触发")
                } catch (e: Exception) {
                    Log.e(TAG, "登录后上传设备信息失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动设备信息上传任务失败", e)
        }
    }

    /**
     * 登录成功后更新远程配置
     */
    private fun updateConfigAfterLogin() {
        try {
            val remoteConfigManager = com.example.repairorderapp.manager.RemoteConfigManager.getInstance(this)
            remoteConfigManager.updateConfigAfterLogin()
            Log.i(TAG, "登录成功，远程配置更新已触发")
        } catch (e: Exception) {
            Log.e(TAG, "登录后更新远程配置失败", e)
        }
    }

    /**
     * 启动位置更新服务
     * 增强权限检查和状态监控
     */
    private fun startLocationService() {
        try {
            // 使用LocationServiceStarter统一启动逻辑
            val serviceStarter = com.example.repairorderapp.service.LocationServiceStarter(this)
            serviceStarter.startLocationService()
        } catch (e: Exception) {
            Log.e("LoginActivity", "启动位置服务失败: ${e.message}")
        }
    }

    /**
     * 检查位置权限
     */
    private fun checkLocationPermissions(): Boolean {
        val fineLocation = ActivityCompat.checkSelfPermission(
            this, 
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        val coarseLocation = ActivityCompat.checkSelfPermission(
            this, 
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        return fineLocation || coarseLocation
    }
} 
package com.example.repairorderapp.ui.customer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.bumptech.glide.Glide
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.CustomerApi
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.databinding.FragmentCustomerDeviceDetailBinding
import com.example.repairorderapp.model.customer.CustomerDevice
import com.example.repairorderapp.viewmodel.customer.CustomerDeviceViewModel
import com.example.repairorderapp.viewmodel.customer.CustomerViewModelFactory
import com.google.android.material.dialog.MaterialAlertDialogBuilder

/**
 * 客户设备详情Fragment
 */
class CustomerDeviceDetailFragment : Fragment() {

    private var _binding: FragmentCustomerDeviceDetailBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var viewModel: CustomerDeviceViewModel
    private var deviceId: String? = null
    private var customerId: String? = null
    private var customerName: String? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCustomerDeviceDetailBinding.inflate(inflater, container, false)
        
        // 获取设备ID和客户ID
        deviceId = arguments?.getString("deviceId")
        customerId = arguments?.getString("customerId")
        customerName = arguments?.getString("customerName")
        
        // 创建CustomerRepository
        val customerApi = ApiClient.createService(CustomerApi::class.java)
        val customerRepository = CustomerRepository(customerApi, null)
        
        // 使用ViewModelFactory创建ViewModel
        val factory = CustomerViewModelFactory(customerRepository)
        viewModel = ViewModelProvider(this, factory)[CustomerDeviceViewModel::class.java]
        
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupToolbar()
        setupStatusSwitch()
        setupButtons()
        observeViewModel()
        
        // 加载设备详情
        deviceId?.let {
            viewModel.getCustomerDeviceDetail(it)
        } ?: run {
            Toast.makeText(requireContext(), "设备ID不能为空", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
        }
    }
    
    private fun setupToolbar() {

        binding.toolbar.title = "设备详情"
    }
    
    private fun setupStatusSwitch() {
        binding.switchDeviceStatus.setOnCheckedChangeListener { _, isChecked ->
            deviceId?.let { id ->
                viewModel.device.value?.let { device ->
                    if (device.status != isChecked) {
                        showStatusConfirmDialog(id, isChecked)
                    }
                }
            }
        }
        
        // 添加状态文本的点击事件
        binding.tvStatusLabel.setOnClickListener {
            deviceId?.let { id ->
                viewModel.device.value?.let { device ->
                    val newStatus = !binding.switchDeviceStatus.isChecked
                    binding.switchDeviceStatus.isChecked = newStatus
                    showStatusConfirmDialog(id, newStatus)
                }
            }
        }
    }
    
    private fun setupButtons() {
        // 基本信息按钮
        binding.btnBasicInfo.setOnClickListener {
            navigateToBasicInfo()
        }
        
        // 维修记录按钮（第一行右侧，之前是合约信息）
        binding.btnMaintenanceRecords.setOnClickListener {
            navigateToRecordList("maintenance")
        }
        
        // 换件记录按钮（第二行左侧）
        binding.btnPartRecords.setOnClickListener {
            navigateToRecordList("replace")
        }
        
        // 单张成本按钮（第二行右侧）
        binding.btnCostPerPage.setOnClickListener {
            Toast.makeText(requireContext(), "功能开发中", Toast.LENGTH_SHORT).show()
            // 实现单张成本查看逻辑
        }
        
        // 印量记录按钮（第三行左侧）
        binding.btnPrintRecords.setOnClickListener {
            Toast.makeText(requireContext(), "功能开发中", Toast.LENGTH_SHORT).show()
        }
        
        // 合约信息按钮（第三行右侧）
        binding.btnContractInfo.setOnClickListener {
            Toast.makeText(requireContext(), "功能开发中", Toast.LENGTH_SHORT).show()
            // 实现合约信息查看逻辑
        }
    }
    
    private fun navigateToBasicInfo() {
        val bundle = Bundle().apply {
            putString("deviceId", deviceId)
            putString("customerId", customerId)
            putString("customerName", customerName)
        }
        
        findNavController().navigate(
            R.id.action_customerDeviceDetailFragment_to_customerDeviceBasicInfoFragment,
            bundle
        )
    }
    
    private fun navigateToRecordList(recordType: String) {
        val bundle = Bundle().apply {
            putString("deviceId", deviceId)
            putString("customerId", customerId)
            putString("customerName", customerName)
            putString("recordType", recordType)
        }
        
        when (recordType) {
            "maintenance" -> findNavController().navigate(
                R.id.action_customerDeviceDetailFragment_to_deviceMaintenanceListFragment,
                bundle
            )
            "fault" -> findNavController().navigate(
                R.id.action_customerDeviceDetailFragment_to_deviceFaultListFragment,
                bundle
            )
            "print" -> findNavController().navigate(
                R.id.action_customerDeviceDetailFragment_to_devicePrintListFragment,
                bundle
            )
            "replace" -> findNavController().navigate(
                R.id.action_customerDeviceDetailFragment_to_deviceReplaceListFragment,
                bundle
            )
            else -> Toast.makeText(requireContext(), "未知记录类型", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun navigateToRecordEdit(recordType: String, isEdit: Boolean) {
        val bundle = Bundle().apply {
            putString("deviceId", deviceId)
            putString("customerId", customerId)
            putString("customerName", customerName)
            putString("recordType", recordType)
            putBoolean("isEdit", isEdit)
        }
        
        when (recordType) {
            "maintenance" -> findNavController().navigate(
                R.id.action_customerDeviceDetailFragment_to_deviceMaintenanceEditFragment,
                bundle
            )
            "fault" -> findNavController().navigate(
                R.id.action_customerDeviceDetailFragment_to_deviceFaultEditFragment,
                bundle
            )
            "print" -> findNavController().navigate(
                R.id.action_customerDeviceDetailFragment_to_devicePrintEditFragment,
                bundle
            )
            else -> Toast.makeText(requireContext(), "未知记录类型", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun observeViewModel() {
        viewModel.device.observe(viewLifecycleOwner) { device ->
            updateUI(device)
        }
        
        viewModel.loading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.contentLayout.visibility = if (isLoading) View.GONE else View.VISIBLE
        }
        
        viewModel.error.observe(viewLifecycleOwner) { errorMsg ->
            if (errorMsg.isNotEmpty()) {
                Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_SHORT).show()
                viewModel.clearError()
            }
        }
        
        viewModel.operationSuccess.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let { message ->
                Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun updateUI(device: CustomerDevice) {
        // 设置设备图片
        Glide.with(this)
            .load(device.deviceGroupImg?.url)
            .placeholder(R.drawable.ic_empty_data)
            .error(R.drawable.ic_empty_data)
            .into(binding.ivDeviceImage)
        
        // 设置设备图片点击预览功能
        binding.ivDeviceImage.setOnClickListener {
            if (device.deviceGroupImg?.url?.isNotEmpty() == true) {
                val dialogFragment = com.example.repairorderapp.ui.common.ImagePreviewDialogFragment.newInstance(device.deviceGroupImg.url!!)
                dialogFragment.show(parentFragmentManager, "ImagePreview")
            }
        }
        
        // 设置设备状态
        binding.switchDeviceStatus.isChecked = device.status
        updateStatusSwitch(device.status)
        
        // 设置基本信息
        binding.tvProductInfo.text = device.productInfo
        binding.tvDeviceModel.text = device.deviceGroup.label
        
        // 修改：将序列号显示改为显示服务类型，不需要前缀"服务类型:"，因为布局中已添加
        binding.tvSerialNumber.text = device.serType?.label ?: "未知"
        
        // 修改：在顶部信息区域显示维修状态
        binding.tvFixStatusInHeader.text = device.deviceStatus?.label ?: "未知"
        
        // 设置计数器信息
        binding.tvBlackWhiteCounter.text = device.blackWhiteCounter?.toString() ?: "0"
        binding.tvColorCounter.text = device.colorCounter?.toString() ?: "0"
    }
    
    private fun updateStatusSwitch(isEnabled: Boolean) {
        if (isEnabled) {
            binding.tvStatusLabel.text = "正常使用"
            binding.tvStatusLabel.setTextColor(ContextCompat.getColor(requireContext(), R.color.primary))
            binding.switchDeviceStatus.trackTintList = ContextCompat.getColorStateList(requireContext(), R.color.primary)
        } else {
            binding.tvStatusLabel.text = "停止使用"
            binding.tvStatusLabel.setTextColor(ContextCompat.getColor(requireContext(), R.color.text_tertiary))
            binding.switchDeviceStatus.trackTintList = ContextCompat.getColorStateList(requireContext(), R.color.text_tertiary)
        }
    }
    
    private fun showStatusConfirmDialog(deviceId: String, newStatus: Boolean) {
        val statusText = if (newStatus) "正常使用" else "停止使用"
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("设备${statusText}")
            .setMessage("确定该设备${statusText}吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.updateDeviceStatus(deviceId, newStatus) { success ->
                    if (!success) {
                        // 状态更新失败，恢复原状态
                        binding.switchDeviceStatus.isChecked = !newStatus
                        updateStatusSwitch(!newStatus)
                    }
                }
            }
            .setNegativeButton("取消") { _, _ ->
                // 取消操作，恢复原状态
                binding.switchDeviceStatus.isChecked = !newStatus
                updateStatusSwitch(!newStatus)
            }
            .show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 
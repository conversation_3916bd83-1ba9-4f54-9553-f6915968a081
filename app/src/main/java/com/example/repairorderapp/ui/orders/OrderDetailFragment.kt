package com.example.repairorderapp.ui.orders

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.example.repairorderapp.R

class OrderDetailFragment : Fragment() {
    
    private var orderId: String? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            orderId = it.getString("orderId")
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_order_detail, container, false)
        
        // 模拟从数据库或API获取工单详情
        val orderDetail = getMockOrderDetail(orderId)
        
        // 设置工单详情数据
        view.findViewById<TextView>(R.id.tv_order_id).text = "工单号: ${orderDetail.id}"
        view.findViewById<TextView>(R.id.tv_customer_name).text = "客户名称: ${orderDetail.customerName}"
        view.findViewById<TextView>(R.id.tv_status).text = "状态: ${orderDetail.status}"
        view.findViewById<TextView>(R.id.tv_create_time).text = "创建时间: ${orderDetail.createTime}"
        view.findViewById<TextView>(R.id.tv_address).text = "地址: ${orderDetail.address}"
        view.findViewById<TextView>(R.id.tv_description).text = "问题描述: ${orderDetail.problemDesc}"
        
        // 设置返回按钮点击事件
        view.findViewById<Button>(R.id.btn_back).setOnClickListener {
            findNavController().navigateUp()
        }
        
        return view
    }
    
    // 模拟工单详情数据
    private fun getMockOrderDetail(orderId: String?): OrderDetail {
        return when(orderId) {
            "O001" -> OrderDetail("O001", "深圳科技有限公司", "待维修", "2023-05-15 10:30", 
                "广东省深圳市南山区科技园路1号", "打印机无法正常工作，需要检查墨盒和纸张供应系统")
            "O002" -> OrderDetail("O002", "广州贸易公司", "进行中", "2023-05-14 09:15", 
                "广东省广州市天河区天河路385号", "空调制冷效果差，可能是冷媒泄漏")
            "O003" -> OrderDetail("O003", "东莞电子厂", "已完成", "2023-05-13 14:20", 
                "广东省东莞市松山湖高新区科技路2号", "生产线设备故障，需要更换传送带")
            "O004" -> OrderDetail("O004", "惠州机械公司", "待维修", "2023-05-12 11:40", 
                "广东省惠州市仲恺高新区惠风七路20号", "机械臂定位不准确，需要重新校准")
            "O005" -> OrderDetail("O005", "佛山家具厂", "待维修", "2023-05-11 16:35", 
                "广东省佛山市顺德区乐从镇家具大道10号", "自动锯床出现异常噪音，需要检查电机")
            else -> OrderDetail("未知", "未知客户", "未知", "未知", "未知地址", "未知问题")
        }
    }
    
    // 工单详情数据类
    data class OrderDetail(
        val id: String,
        val customerName: String,
        val status: String,
        val createTime: String,
        val address: String,
        val problemDesc: String
    )
} 
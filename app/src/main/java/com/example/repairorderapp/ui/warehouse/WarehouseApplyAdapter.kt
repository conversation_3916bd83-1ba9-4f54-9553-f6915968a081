package com.example.repairorderapp.ui.warehouse

import android.annotation.SuppressLint
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.repairorderapp.R
import com.example.repairorderapp.databinding.ItemWarehouseApplyBinding
import com.example.repairorderapp.model.warehouse.WarehouseItem

/**
 * 申请领料适配器
 */
class WarehouseApplyAdapter : RecyclerView.Adapter<WarehouseApplyAdapter.ViewHolder>() {

    private val items = mutableListOf<WarehouseItem>()
    private val selectedItems = mutableSetOf<String>() // 存储选中项的ID
    private val itemQuantities = mutableMapOf<String, Int>() // 存储每个物品的数量
    
    private var onImageClickListener: ((String) -> Unit)? = null
    private var onItemSelectListener: ((WarehouseItem, Boolean, Int) -> Unit)? = null
    private var onQuantityChangeListener: ((WarehouseItem, Int) -> Unit)? = null
    
    /**
     * 设置图片点击监听
     */
    fun setOnImageClickListener(listener: (String) -> Unit) {
        onImageClickListener = listener
    }
    
    /**
     * 设置物品选择监听
     */
    fun setOnItemSelectListener(listener: (WarehouseItem, Boolean, Int) -> Unit) {
        onItemSelectListener = listener
    }
    
    /**
     * 设置数量变更监听
     */
    fun setOnQuantityChangeListener(listener: (WarehouseItem, Int) -> Unit) {
        onQuantityChangeListener = listener
    }

    /**
     * 提交数据列表
     */
    fun submitList(newItems: List<WarehouseItem>) {
        items.clear()
        items.addAll(newItems)
        notifyDataSetChanged()
    }
    
    /**
     * 添加数据项
     */
    fun addItems(newItems: List<WarehouseItem>) {
        val startPosition = items.size
        items.addAll(newItems)
        notifyItemRangeInserted(startPosition, newItems.size)
    }
    
    /**
     * 清空所有选中状态
     */
    fun clearSelections() {
        selectedItems.clear()
        itemQuantities.clear()
        notifyDataSetChanged()
    }

    /**
     * 重置所有选中状态和数量
     * 用于申请成功后重置界面
     */
    fun resetSelections() {
        selectedItems.clear()
        itemQuantities.clear()
        notifyDataSetChanged()
    }
    
    /**
     * 移除指定ID列表中物品的选中状态
     * 用于确认页面删除物品后同步状态
     */
    fun removeSelections(itemIds: List<String>) {
        if (itemIds.isEmpty()) return
        
        // 记录日志，便于调试
        Log.d("WarehouseApplyAdapter", "准备移除选中状态: ${itemIds.joinToString()}")
        Log.d("WarehouseApplyAdapter", "当前选中状态数量: ${selectedItems.size}")
        
        // 记录原始选中状态
        val originalSelected = selectedItems.toSet()
        
        // 需要更新的项目位置列表
        val positionsToUpdate = mutableListOf<Int>()
        
        // 移除所有需要删除的ID
        for (id in itemIds) {
            if (selectedItems.contains(id)) {
                selectedItems.remove(id)
                // 同时移除对应的数量记录
                itemQuantities.remove(id)
                
                // 查找并记录需要更新的位置
                for (i in items.indices) {
                    if (items[i].id == id) {
                        positionsToUpdate.add(i)
                        break
                    }
                }
                
                Log.d("WarehouseApplyAdapter", "成功移除ID: $id")
            } else {
                Log.d("WarehouseApplyAdapter", "ID不存在于选中列表: $id")
            }
        }
        
        // 记录变更后状态
        Log.d("WarehouseApplyAdapter", "移除后选中状态数量: ${selectedItems.size}")
        Log.d("WarehouseApplyAdapter", "已移除数量: ${originalSelected.size - selectedItems.size}")
        
        // 精确更新发生变化的项目
        for (position in positionsToUpdate) {
            notifyItemChanged(position)
        }
        
        Log.d("WarehouseApplyAdapter", "已通知${positionsToUpdate.size}个项目变更")
    }
    
    /**
     * 同步已选物品状态
     * 用于从确认页面返回时，显示哪些物品已被添加到清单中
     */
    fun syncSelectedItems(selectedItemIds: List<String>) {
        // 需要更新的项目位置列表
        val positionsToUpdate = mutableListOf<Int>()
        
        // 获取当前选中状态快照
        val oldSelectedItems = selectedItems.toSet()
        
        // 检查是否有变更
        val hasAnyChange = oldSelectedItems != selectedItemIds.toSet()
        
        // 如果没有任何变更，直接返回
        if (!hasAnyChange) {
            Log.d("WarehouseApplyAdapter", "选中状态没有变化，跳过更新")
            return
        }
        
        // 先清空当前选中状态
        selectedItems.clear()
        
        // 设置新的选中状态
        for (id in selectedItemIds) {
            selectedItems.add(id)
            
            // 确保每个选中的物品都有默认数量
            if (!itemQuantities.containsKey(id)) {
                itemQuantities[id] = 1
            }
        }
        
        // 计算需要更新的位置
        for (i in items.indices) {
            val itemId = items[i].id
            // 如果物品的选中状态发生了变化，就需要更新这个位置
            if ((itemId in selectedItems && itemId !in oldSelectedItems) || 
                (itemId !in selectedItems && itemId in oldSelectedItems)) {
                positionsToUpdate.add(i)
            }
        }
        
        if (positionsToUpdate.isEmpty()) {
            Log.d("WarehouseApplyAdapter", "没有找到需要更新的项目，可能是ID不匹配")
            return
        }
        
        // 优先使用精确更新
        if (positionsToUpdate.size < items.size / 3) { // 如果变更小于总数的1/3，使用精确更新
            for (position in positionsToUpdate) {
                notifyItemChanged(position)
            }
            Log.d("WarehouseApplyAdapter", "精确通知${positionsToUpdate.size}个项目变更")
        } else {
            // 变更太多，直接全量更新，但添加警告日志
            Log.d("WarehouseApplyAdapter", "变更项目较多(${positionsToUpdate.size}/${items.size})，执行全量更新")
            notifyDataSetChanged()
            Log.d("WarehouseApplyAdapter", "全量更新适配器数据")
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemWarehouseApplyBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.bind(item)
    }

    override fun getItemCount() = items.size

    inner class ViewHolder(private val binding: ItemWarehouseApplyBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        @SuppressLint("SetTextI18n")
        fun bind(item: WarehouseItem) {
            // 设置商品名称 - 使用当前项目的itemName，该值已在处理多SKU时设置为invSkuName
            binding.tvItemName.text = item.itemName
            
            // 设置OEM编号 - 使用当前项目的oemNumber，该值已在处理多SKU时设置
            binding.tvOemNumber.text = "OEM编号：" + (item.oemNumber ?: "未知")
            
            // 设置物品编码 - 使用当前项目的articleCode，该值已在处理多SKU时设置
            binding.tvArticleCode.text = "物品编码：" + (item.articleCode ?: "未知")
            
            // 设置商品规格 - 优先获取SKU信息中的规格
            if (item.skuInfo?.saleAttrVals?.isNotEmpty() == true) {
                // 从SKU信息中获取规格，只显示属性值，不显示属性名
                val attrString = item.skuInfo.saleAttrVals.joinToString(", ") { it.`val` }
                binding.tvSpecification.text = attrString
                binding.tvSpecification.visibility = View.VISIBLE
            } else if (item.categoryName?.isNotEmpty() == true) {
                binding.tvSpecification.text = item.categoryName
                binding.tvSpecification.visibility = View.VISIBLE
            } else {
                binding.tvSpecification.visibility = View.GONE
            }
            
            // 设置价格 - 使用当前项目的价格
            val priceValue = item.saleUnitPrice
            binding.tvPrice.text = "￥${String.format("%.2f", priceValue)}"
            
            // 设置商品图片 - 优先使用当前项目的图片
            val imageUrl = when {
                item.picUrl?.isNotEmpty() == true -> item.picUrl?.get(0)?.url
                item.skuInfo?.picUrl?.isNotEmpty() == true -> item.skuInfo.picUrl[0].url
                else -> null
            }
            
            if (imageUrl != null) {
                binding.ivItemImage.visibility = View.VISIBLE
                Glide.with(binding.root.context)
                    .load(imageUrl)
                    .placeholder(R.drawable.ic_image_placeholder)
                    .error(R.drawable.ic_image_placeholder)
                    .into(binding.ivItemImage)
                
                // 设置图片点击监听
                binding.ivItemImage.setOnClickListener {
                    onImageClickListener?.invoke(imageUrl)
                }
            } else {
                binding.ivItemImage.visibility = View.VISIBLE
                binding.ivItemImage.setImageResource(R.drawable.ic_image_placeholder)
            }
            
            // 获取可用数量 - 使用当前项目的可用数量
            val maxAvailableNum = item.availableNum
            
            // 初始化数量
            if (!itemQuantities.containsKey(item.id)) {
                itemQuantities[item.id] = 1
            }
//            binding.tvQuantity.text = itemQuantities[item.id].toString()

            // 设置选中状态 - 每次绑定时从selectedItems集合获取当前状态
            val isSelected = selectedItems.contains(item.id)
            binding.checkBox.isSelected = isSelected
            binding.checkBox.setBackgroundResource(
                if (isSelected) R.drawable.checkbox_selected else R.drawable.checkbox_normal
            )
            
            // 设置选择按钮点击事件
            binding.checkBox.setOnClickListener {
                // 直接从集合中获取当前状态，而不是使用闭包中的isSelected变量
                val currentSelected = selectedItems.contains(item.id)
                val newCheckedState = !currentSelected
                
                if (newCheckedState) {
                    selectedItems.add(item.id)
                } else {
                    selectedItems.remove(item.id)
                }
                binding.checkBox.isSelected = newCheckedState
                binding.checkBox.setBackgroundResource(
                    if (newCheckedState) R.drawable.checkbox_selected else R.drawable.checkbox_normal
                )
                
                // 通知监听器
                onItemSelectListener?.invoke(item, newCheckedState, itemQuantities[item.id] ?: 1)
            }
            
            // 设置数量按钮点击事件
//            binding.btnDecrease.setOnClickListener {
//                val currentQuantity = itemQuantities[item.id] ?: 1
//                if (currentQuantity > 1) {
//                    val newQuantity = currentQuantity - 1
//                    itemQuantities[item.id] = newQuantity
////                    binding.tvQuantity.text = newQuantity.toString()
//
//                    // 通知监听器
//                    if (isSelected) {
//                        onQuantityChangeListener?.invoke(item, newQuantity)
//                    }
//                }
//            }
//
//            binding.btnIncrease.setOnClickListener {
//                val currentQuantity = itemQuantities[item.id] ?: 1
//
//                if (currentQuantity < maxAvailableNum) {
//                    val newQuantity = currentQuantity + 1
//                    itemQuantities[item.id] = newQuantity
//                    binding.tvQuantity.text = newQuantity.toString()
//
//                    // 通知监听器
//                    if (isSelected) {
//                        onQuantityChangeListener?.invoke(item, newQuantity)
//                    }
//                }
//
//                // 禁用增加按钮，如果已达最大可用数量
//                binding.btnIncrease.isEnabled = currentQuantity < maxAvailableNum
//            }
//
//            // 初始状态下，设置增加按钮的启用状态
//            binding.btnIncrease.isEnabled = (itemQuantities[item.id] ?: 1) < maxAvailableNum
        }
    }
} 
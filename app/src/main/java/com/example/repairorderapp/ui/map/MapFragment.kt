package com.example.repairorderapp.ui.map

import android.Manifest
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.location.LocationManager
import android.net.ConnectivityManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.RetrofitClient
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.model.Engineer
import com.example.repairorderapp.model.EngineerWorkData
import com.example.repairorderapp.model.MapMarker
import com.example.repairorderapp.model.MarkerType
import com.example.repairorderapp.model.RepairOrder
import com.example.repairorderapp.service.LocationUpdateService
import com.example.repairorderapp.util.SharedPrefsManager
import com.example.repairorderapp.utils.PermissionUtils

import com.tencent.map.geolocation.TencentLocation
import com.tencent.map.geolocation.TencentLocationListener
import com.tencent.map.geolocation.TencentLocationManager
import com.tencent.map.geolocation.TencentLocationRequest
import com.tencent.tencentmap.mapsdk.maps.*
import com.tencent.tencentmap.mapsdk.maps.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.withPermit
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

class MapFragment : Fragment() {

    private lateinit var mapView: MapView
    private lateinit var tencentMap: TencentMap
    private lateinit var checkboxEngineers: CheckBox
    private lateinit var checkboxIdleEngineers: CheckBox
    private lateinit var checkboxCustomers: CheckBox
    private lateinit var checkboxOverdueCustomers: CheckBox
    private lateinit var fabLocation: ImageView
    private lateinit var fabRefresh: ImageView
    private lateinit var progressLoading: ProgressBar
    private lateinit var btnZoomIn: ImageView
    private lateinit var btnZoomOut: ImageView
    private lateinit var btnZoomFitAll: ImageView
    private lateinit var panelFilter: LinearLayout
    private lateinit var panelFilterContent: LinearLayout
    private lateinit var panelFilterHeader: LinearLayout
    private lateinit var filterDivider: View
    private lateinit var expandIcon: ImageView
    private lateinit var imageFilterIcon: ImageView
    private lateinit var statisticsFilterIcon: ImageView
    private var isFilterPanelExpanded = false

    private lateinit var viewModel: MapViewModel
    private var isRestoringState = false

    private lateinit var panelStatisticsHeader: LinearLayout
    private lateinit var panelStatisticsContent: LinearLayout
    private lateinit var statisticsDivider: View
    private lateinit var statisticsExpandIcon: ImageView
    private lateinit var textStatisticsHeader: TextView
    private var isStatisticsPanelExpanded = false

    private lateinit var panelEngineersHeader: LinearLayout
    private lateinit var panelEngineersContent: LinearLayout
    private lateinit var engineersDivider: View
    private lateinit var engineersExpandIcon: ImageView
    private lateinit var textEngineersHeader: TextView
    private var isEngineersPanelExpanded = false

    private lateinit var markerManager: CustomMarkerManager

    private val engineerMarkers = mutableListOf<MapMarker>()
    private val customerMarkers = mutableListOf<MapMarker>()

    private var expandedMarkerId: String? = null

    private var visibleEngineers = 0
    private var visibleCustomers = 0

    private val initialPosition = LatLng(30.66, 104.07)

    // 地图缩放等级常量 - 符合地图预览习惯的缩放挡位
    companion object {
        private const val ZOOM_LEVEL_WORLD = 3f      // 世界级别
        private const val ZOOM_LEVEL_COUNTRY = 6f    // 国家级别
        private const val ZOOM_LEVEL_PROVINCE = 8f   // 省份级别
        private const val ZOOM_LEVEL_CITY = 11f      // 城市级别
        private const val ZOOM_LEVEL_DISTRICT = 13f  // 区县级别
        private const val ZOOM_LEVEL_STREET = 15f    // 街道级别
        private const val ZOOM_LEVEL_BUILDING = 17f  // 建筑级别
        private const val ZOOM_LEVEL_DETAIL = 19f    // 详细级别

        private val ZOOM_LEVELS = arrayOf(
            ZOOM_LEVEL_WORLD,
            ZOOM_LEVEL_COUNTRY,
            ZOOM_LEVEL_PROVINCE,
            ZOOM_LEVEL_CITY,
            ZOOM_LEVEL_DISTRICT,
            ZOOM_LEVEL_STREET,
            ZOOM_LEVEL_BUILDING,
            ZOOM_LEVEL_DETAIL
        )
    }

    private var mapSavedInstanceState: Bundle? = null

    private var isFragmentActive = false

    private var isMapInitialized = false

    private var isReturningFromDetailPage = false
    
    /**
     * 安全显示Toast，避免context为null的崩溃
     */
    private fun safeShowToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
        try {
            context?.let { ctx ->
                if (isAdded && !isDetached && isResumed) {
                    activity?.runOnUiThread {
                        Toast.makeText(ctx, message, duration).show()
                    }
                }
            }
        } catch (e: Exception) {
            Log.w("MapFragment", "显示Toast失败: ${e.message}")
        }
    }

    private var orderDetailClickListener: CustomMarkerManager.OnOrderDetailClickListener? = null

    private var isFragmentDestroyed = false

    private var overlayView: View? = null

    // 缩放操作防抖动
    private var lastZoomTime: Long = 0
    private val zoomDebounceInterval = 300L // 300ms防抖动间隔

    private var isMapResourceReleasing = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.mapSavedInstanceState = savedInstanceState

        viewModel = ViewModelProvider(requireActivity()).get(MapViewModel::class.java)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_map, container, false)

        orderDetailClickListener = object : CustomMarkerManager.OnOrderDetailClickListener {
            override fun onOrderDetailClick(orderId: String) {
                isReturningFromDetailPage = true
                findNavController().navigate(
                    R.id.action_mapFragment_to_repairOrderDetailFragmentNew,
                    Bundle().apply {
                        putString("orderId", orderId)
                    }
                )
            }
        }

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        isFragmentActive = true
        isMapInitialized = false

        bindViews(view)
        setupUI()
        initializeMap()
    }

    private fun bindViews(view: View) {
        mapView = view.findViewById(R.id.map_view)
        progressLoading = view.findViewById(R.id.progress_loading)
        checkboxEngineers = view.findViewById(R.id.checkbox_engineers)
        checkboxIdleEngineers = view.findViewById(R.id.checkbox_idle_engineers)
        checkboxCustomers = view.findViewById(R.id.checkbox_customers)
        checkboxOverdueCustomers = view.findViewById(R.id.checkbox_overdue_customers)
        fabLocation = view.findViewById(R.id.btn_location)
        fabRefresh = view.findViewById(R.id.btn_refresh)
        btnZoomIn = view.findViewById(R.id.btn_zoom_in)
        btnZoomOut = view.findViewById(R.id.btn_zoom_out)
        btnZoomFitAll = view.findViewById(R.id.btn_zoom_fit_all)
        panelFilter = view.findViewById(R.id.panel_filter)
        panelFilterContent = view.findViewById(R.id.panel_filter_content)
        panelFilterHeader = view.findViewById(R.id.panel_filter_header)
        filterDivider = view.findViewById(R.id.filter_divider)
        expandIcon = view.findViewById(R.id.expand_icon)
        imageFilterIcon = view.findViewById(R.id.image_filter_icon)
        statisticsFilterIcon = view.findViewById(R.id.statistics_filter_icon)
        panelStatisticsHeader = view.findViewById(R.id.panel_statistics_header)
        panelStatisticsContent = view.findViewById(R.id.panel_statistics_content)
        statisticsDivider = view.findViewById(R.id.statistics_divider)
        statisticsExpandIcon = view.findViewById(R.id.statistics_expand_icon)
        textStatisticsHeader = view.findViewById(R.id.text_statistics_header)
        panelEngineersHeader = view.findViewById(R.id.panel_engineers_header)
        panelEngineersContent = view.findViewById(R.id.panel_engineers_content)
        engineersDivider = view.findViewById(R.id.engineers_divider)
        engineersExpandIcon = view.findViewById(R.id.engineers_expand_icon)
        textEngineersHeader = view.findViewById(R.id.text_engineers_header)
    }

    private fun setupUI() {
        progressLoading.visibility = View.VISIBLE

        expandIcon.setColorFilter(Color.parseColor("#2196F3"))
        imageFilterIcon.setColorFilter(Color.parseColor("#2196F3"))
        statisticsExpandIcon.setColorFilter(Color.parseColor("#2196F3"))
        statisticsFilterIcon.setColorFilter(Color.parseColor("#2196F3"))
        engineersExpandIcon.setColorFilter(Color.parseColor("#2196F3"))
        view?.findViewById<ImageView>(R.id.engineers_filter_icon)?.setColorFilter(Color.parseColor("#2196F3"))

        setupFilters()
        setupFilterPanel()
        setupStatisticsPanel()
        setupEngineersPanel()

        checkboxEngineers.jumpDrawablesToCurrentState()
        checkboxIdleEngineers.jumpDrawablesToCurrentState()
        checkboxCustomers.jumpDrawablesToCurrentState()
        checkboxOverdueCustomers.jumpDrawablesToCurrentState()

        fabLocation.setOnClickListener {
            requestLocationAndMoveTo()
        }

        fabLocation.setOnLongClickListener {
            showLocationPermissionStatus()
            true
        }

        fabRefresh.setOnClickListener {
            refreshMapData()
        }

        // 设置缩放控制按钮监听器
        setupZoomControls()
    }

    /**
     * 设置地图缩放控制按钮
     */
    private fun setupZoomControls() {
        // 放大按钮
        btnZoomIn.setOnClickListener {
            zoomIn()
        }
        
        // 缩小按钮
        btnZoomOut.setOnClickListener {
            zoomOut()
        }
        
        // 适应所有标记按钮
        btnZoomFitAll.setOnClickListener {
            fitAllMarkersWithDebounce()
        }
    }

    /**
     * 地图放大 - 使用预设的缩放等级
     */
    private fun zoomIn() {
        try {
            if (!::tencentMap.isInitialized) return
            
            // 防抖动检查
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastZoomTime < zoomDebounceInterval) {
                return
            }
            lastZoomTime = currentTime
            
            val currentZoom = tencentMap.cameraPosition.zoom
            val nextZoomLevel = getNextZoomLevel(currentZoom, true)
            
            if (nextZoomLevel != currentZoom) {
                // 添加缩放动画的回调
                val animationCallback = object : TencentMap.CancelableCallback {
                    override fun onCancel() {}
                    override fun onFinish() {
                        // 动画完成后显示缩放等级信息
                        val levelName = getZoomLevelName(nextZoomLevel)
                        safeShowToast("缩放: $levelName")
                    }
                }
                
                tencentMap.animateCamera(
                    CameraUpdateFactory.zoomTo(nextZoomLevel),
                    400,
                    animationCallback
                )
            } else {
                safeShowToast("已达到最大缩放级别")
            }
        } catch (e: Exception) {
            Log.e("MapFragment", "放大地图失败", e)
        }
    }

    /**
     * 地图缩小 - 使用预设的缩放等级
     */
    private fun zoomOut() {
        try {
            if (!::tencentMap.isInitialized) return
            
            // 防抖动检查
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastZoomTime < zoomDebounceInterval) {
                return
            }
            lastZoomTime = currentTime
            
            val currentZoom = tencentMap.cameraPosition.zoom
            val nextZoomLevel = getNextZoomLevel(currentZoom, false)
            
            if (nextZoomLevel != currentZoom) {
                // 添加缩放动画的回调
                val animationCallback = object : TencentMap.CancelableCallback {
                    override fun onCancel() {}
                    override fun onFinish() {
                        // 动画完成后显示缩放等级信息
                        val levelName = getZoomLevelName(nextZoomLevel)
                        safeShowToast("缩放: $levelName")
                    }
                }
                
                tencentMap.animateCamera(
                    CameraUpdateFactory.zoomTo(nextZoomLevel),
                    400,
                    animationCallback
                )
            } else {
                safeShowToast("已达到最小缩放级别")
            }
        } catch (e: Exception) {
            Log.e("MapFragment", "缩小地图失败", e)
        }
    }

    /**
     * 获取下一个缩放等级
     * @param currentZoom 当前缩放级别
     * @param zoomIn 是否放大(true为放大，false为缩小)
     * @return 下一个缩放级别
     */
    private fun getNextZoomLevel(currentZoom: Float, zoomIn: Boolean): Float {
        var targetLevel = currentZoom
        
        if (zoomIn) {
            // 查找比当前缩放级别大的最小等级
            for (level in ZOOM_LEVELS) {
                if (level > currentZoom) {
                    targetLevel = level
                    break
                }
            }
        } else {
            // 查找比当前缩放级别小的最大等级
            for (i in ZOOM_LEVELS.indices.reversed()) {
                val level = ZOOM_LEVELS[i]
                if (level < currentZoom) {
                    targetLevel = level
                    break
                }
            }
        }
        
        return targetLevel
    }

    /**
     * 获取缩放等级的名称
     */
    private fun getZoomLevelName(zoomLevel: Float): String {
        return when {
            zoomLevel <= ZOOM_LEVEL_WORLD -> "世界"
            zoomLevel <= ZOOM_LEVEL_COUNTRY -> "国家"
            zoomLevel <= ZOOM_LEVEL_PROVINCE -> "省份"
            zoomLevel <= ZOOM_LEVEL_CITY -> "城市"
            zoomLevel <= ZOOM_LEVEL_DISTRICT -> "区县"
            zoomLevel <= ZOOM_LEVEL_STREET -> "街道"
            zoomLevel <= ZOOM_LEVEL_BUILDING -> "建筑"
            else -> "详细"
        }
    }

    /**
     * 带防抖动的适应所有标记功能
     */
    private fun fitAllMarkersWithDebounce() {
        try {
            if (!::tencentMap.isInitialized) return
            
            // 防抖动检查
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastZoomTime < zoomDebounceInterval) {
                return
            }
            lastZoomTime = currentTime
            
            zoomToFitAllMarkers()
            safeShowToast("已适应所有标记")
        } catch (e: Exception) {
            Log.e("MapFragment", "适应所有标记失败", e)
        }
    }

    private fun initializeMap() {
        val tMap = mapView.map
        if (tMap == null) {
            Handler(Looper.getMainLooper()).postDelayed({
                if (!isFragmentActive || isFragmentDestroyed) return@postDelayed
                val retryMap = mapView.map
                if (retryMap != null) {
                    tencentMap = retryMap
                    onMapReady()
                } else {
                    progressLoading.visibility = View.GONE
                    safeShowToast("地图加载失败，请重启应用", Toast.LENGTH_LONG)
                }
            }, 2000)
        } else {
            tencentMap = tMap
            onMapReady()
        }
    }

    private fun onMapReady() {
        if (!isAdded) return

        try {
            tencentMap.uiSettings.isZoomControlsEnabled = false
            markerManager = CustomMarkerManager(requireContext(), tencentMap)
            markerManager.orderDetailClickListener = orderDetailClickListener

            setupMap()
            isMapInitialized = true

            if (viewModel.checkNeedRefresh()) {
                transparentLoadData()
            } else {
                restoreMapStateFromCache()
            }

            view?.postDelayed({
                if (!isFragmentActive) return@postDelayed
                showLocationHelp()
            }, 2000)

        } catch (e: Exception) {
            progressLoading.visibility = View.GONE
            safeShowToast("地图初始化失败: ${e.message}")
        }
    }

    private fun requestLocationAndMoveTo() {
        if (!PermissionUtils.hasLocationPermissions(requireContext())) {
            showLocationPermissionStatus()
            PermissionUtils.requestLocationPermissions(this)
            safeShowToast("需要位置权限才能定位到您的位置")
            return
        }

        try {
            progressLoading.visibility = View.VISIBLE
            safeShowToast("正在定位...")
            
            val locationManager = requireContext().getSystemService(Context.LOCATION_SERVICE) as LocationManager
            val isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
            val isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
            
            if (!isGpsEnabled && !isNetworkEnabled) {
                progressLoading.visibility = View.GONE
                AlertDialog.Builder(requireContext())
                    .setTitle("位置服务未开启")
                    .setMessage("请开启设备的GPS和网络位置服务以使用定位功能")
                    .setPositiveButton("去设置") { dialog: DialogInterface, which: Int ->
                        startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                    }
                    .setNegativeButton("取消", null)
                    .show()
                return
            }

            if (!tencentMap.isMyLocationEnabled) {
                tencentMap.isMyLocationEnabled = true
            }

            val myLocationStyle = MyLocationStyle()
            myLocationStyle.anchor(0.5f, 0.5f)
            myLocationStyle.strokeColor(Color.TRANSPARENT)
            myLocationStyle.strokeWidth(0)
            myLocationStyle.fillColor(Color.parseColor("#4285F4"))
            tencentMap.setMyLocationStyle(myLocationStyle)

            tencentMap.setOnMyLocationChangeListener { location ->
                if (location != null) {
                    val latLng = LatLng(location.latitude, location.longitude)
                    moveToLocation(latLng)

                    Toast.makeText(
                        context,
                        "已定位到当前位置: ${location.latitude}, ${location.longitude}",
                        Toast.LENGTH_SHORT
                    ).show()

                    if (::progressLoading.isInitialized && !isFragmentDestroyed) {
                        progressLoading.visibility = View.GONE
                    }

                    tencentMap.setOnMyLocationChangeListener(null)
                }
            }

            view?.postDelayed({
                if (progressLoading.visibility == View.VISIBLE) {
                    progressLoading.visibility = View.GONE
                    tryTencentLocationSdk()
                }
            }, 5000)

        } catch (e: Exception) {
            progressLoading.visibility = View.GONE
            Toast.makeText(context, "获取位置失败: ${e.message}", Toast.LENGTH_SHORT).show()
            tryTencentLocationSdk()
        }
    }

    private fun tryTencentLocationSdk() {
        try {
            val connectivityManager = requireContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val networkInfo = connectivityManager.activeNetworkInfo
            if (networkInfo == null || !networkInfo.isConnected) {
                Toast.makeText(context, "网络连接不可用，请检查网络设置", Toast.LENGTH_SHORT).show()
                progressLoading.visibility = View.GONE
                return
            }

            try {
                val privacyClass = Class.forName("com.tencent.map.geolocation.TencentLocationManager")
                val privacyMethod = privacyClass.getDeclaredMethod("setUserAgreePrivacy", Boolean::class.java)
                privacyMethod.invoke(null, true)
            } catch (e: Exception) {
            }

            val tencentLocationManager = TencentLocationManager.getInstance(requireContext())
            val tencentLocationRequest = TencentLocationRequest.create()
                .setRequestLevel(TencentLocationRequest.REQUEST_LEVEL_ADMIN_AREA)
                .setAllowGPS(true)
                .setAllowDirection(true)
                .setAllowCache(true)
                .setIndoorLocationMode(true)

            val locationListener = object : TencentLocationListener {
                override fun onLocationChanged(location: TencentLocation?, error: Int, reason: String?) {
                    tencentLocationManager.removeUpdates(this)
                    
                    if (error == TencentLocation.ERROR_OK && location != null) {
                        val latLng = LatLng(location.latitude, location.longitude)
                        moveToLocation(latLng)
                        progressLoading.visibility = View.GONE
                        Toast.makeText(
                            context,
                            "已定位到当前位置: ${location.latitude}, ${location.longitude}",
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        fallbackToDefaultPosition()
                    }
                }
                
                override fun onStatusUpdate(name: String?, status: Int, desc: String?) {
                }
            }

            tencentLocationManager.requestSingleFreshLocation(
                tencentLocationRequest, locationListener, Looper.getMainLooper()
            )

            Handler(Looper.getMainLooper()).postDelayed({
                if (progressLoading.visibility == View.VISIBLE) {
                    tencentLocationManager.removeUpdates(locationListener)
                    fallbackToDefaultPosition()
                }
            }, 10000)

        } catch (e: Exception) {
            fallbackToDefaultPosition()
        }
    }

    private fun setupMap() {
        try {
            progressLoading.visibility = View.VISIBLE
            
            tencentMap.apply {
                uiSettings.isCompassEnabled = true
                mapType = TencentMap.MAP_TYPE_NORMAL
                try {
                    javaClass.getMethod("set3DBuildingEnable", Boolean::class.java)
                        .invoke(this, false)
                } catch (e: Exception) {
                }
                
                setOnCameraChangeListener(object : TencentMap.OnCameraChangeListener {
                    override fun onCameraChange(cameraPosition: CameraPosition) {
                        val zoomLevel = cameraPosition.zoom
                        markerManager.updateInfoWindowVisibility(zoomLevel)
                    }

                    override fun onCameraChangeFinished(cameraPosition: CameraPosition) {
                    }
                })

                setOnMarkerClickListener { marker ->
                    try {
                        val userData = marker.tag as? MapMarker
                        if (userData != null) {
                            val markerId = userData.id
                            if (markerId.startsWith("info_")) {
                                val originalMarkerId = markerId.substring(5)
                                val mapMarker = markerManager.findMapMarkerById(originalMarkerId)
                                if (mapMarker != null && mapMarker.type == MarkerType.CUSTOMER) {
                                    if (expandedMarkerId == originalMarkerId) {
                                        markerManager.orderDetailClickListener?.onOrderDetailClick(originalMarkerId)
                                    } else {
                                        expandOrCollapseMarker(originalMarkerId, false)
                                    }
                                    return@setOnMarkerClickListener true
                                } else {
                                    expandOrCollapseMarker(originalMarkerId, false)
                                    return@setOnMarkerClickListener true
                                }
                            } else {
                                val mapMarker = markerManager.findMapMarkerById(markerId)
                                if (mapMarker != null) {
                                    val targetPosition = LatLng(mapMarker.latitude, mapMarker.longitude)
                                    tencentMap.animateCamera(
                                        CameraUpdateFactory.newLatLngZoom(targetPosition, 16f)
                                    )
                                }
                                return@setOnMarkerClickListener true
                            }
                        }
                    } catch (e: Exception) {
                    }
                    false
                }

                setOnMapClickListener { latLng ->
                    var handled = false
                    
                    if (isStatisticsPanelExpanded) {
                        isStatisticsPanelExpanded = false
                        val statisticsScrollView = panelStatisticsContent.parent as ScrollView
                        toggleStatisticsPanel(statisticsScrollView)
                        handled = true
                    }
                    
                    if (isEngineersPanelExpanded) {
                        isEngineersPanelExpanded = false
                        val engineersScrollView = panelEngineersContent.parent as ScrollView
                        toggleEngineersPanel(engineersScrollView)
                        handled = true
                    }

                    if (isFilterPanelExpanded) {
                        isFilterPanelExpanded = false
                        panelFilterContent.visibility = View.GONE
                        filterDivider.visibility = View.GONE
                        expandIcon.setImageResource(R.drawable.ic_expand_more)
                        expandIcon.jumpDrawablesToCurrentState()
                        handled = true
                    }
                    
                    // 只有在没有折叠任何面板的情况下，才折叠标记信息窗口
                    if (!handled) {
                        expandedMarkerId?.let { markerId ->
                            // 检查点击的位置是否在展开的信息窗口附近
                            val marker = markerManager.findMapMarkerById(markerId)
                            if (marker != null) {
                                val markerLatLng = LatLng(marker.latitude, marker.longitude)
                                val screenPoint = tencentMap.projection.toScreenLocation(markerLatLng)
                                val clickPoint = tencentMap.projection.toScreenLocation(latLng)
                                
                                // 计算点击位置与标记的距离（像素）
                                val distance = Math.sqrt(
                                    Math.pow((screenPoint.x - clickPoint.x).toDouble(), 2.0) +
                                    Math.pow((screenPoint.y - clickPoint.y).toDouble(), 2.0)
                                )
                                
                                // 如果点击距离标记太近（比如200像素内），不折叠信息窗口
                                if (distance > 200) {
                                    markerManager.toggleMarkerExpanded(markerId)
                                    expandedMarkerId = null
                                }
                            }
                        }
                    }
                    
                    false
                }
                
                addOnMapLoadedCallback {
                    if (!isFragmentActive) {
                        return@addOnMapLoadedCallback
                    }

                    val cachedState = viewModel.getCachedState()
                    if (cachedState != null) {
                        refreshEngineerMarkerData()
                        progressLoading.visibility = View.GONE
                        return@addOnMapLoadedCallback
                    }
                }
            }
            
        } catch (e: Exception) {
            progressLoading.visibility = View.GONE
            Toast.makeText(context, "地图初始化失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun initMapData() {
        try {
            engineerMarkers.clear()
            customerMarkers.clear()
        } catch (e: Exception) {
        }
    }
    
    private fun prepareEngineerData(): List<MapMarker> {
        try {
            val sharedPrefs = SharedPrefsManager(requireContext())
            val token = sharedPrefs.getAuthToken()
            
            val retrofit = if (token.isNotEmpty()) {
                RetrofitClient.createWithAuth(token)
            } else {
                RetrofitClient.getInstance()
            }
            val apiService = retrofit.create(WorkOrderApi::class.java)
            
            val params = mapOf(
                "pageNumber" to "1",
                "pageSize" to "1000"
            )
            
            val engineersResponse = apiService.getAllEngineerList("1002", params).execute()
            
            if (!engineersResponse.isSuccessful || engineersResponse.body()?.code != 200) {
                return emptyList()
            }
            
            val responseBody = engineersResponse.body()
            if (responseBody == null) {
                return emptyList()
            }
            
            val data = responseBody.data
            val rawEngineers = if (data is Map<*, *> && data.containsKey("rows")) {
                try {
                    @Suppress("UNCHECKED_CAST")
                    val rows = data["rows"] as? List<Map<String, Any>>
                    rows?.map { engineerMap ->
                        try {
                            val id = engineerMap["id"] as? String ?: ""
                            val name = engineerMap["name"] as? String ?: "未知工程师"
                            val phone = engineerMap["mobileNumber"] as? String
                            Engineer(id = id, name = name, phone = phone)
                        } catch (e: Exception) {
                            null
                        }
                    }?.filterNotNull() ?: emptyList()
                } catch (e: Exception) {
                    emptyList<Engineer>()
                }
            } else {
                emptyList<Engineer>()
            }
            
            if (rawEngineers.isEmpty()) {
                return emptyList()
            }
            
            val locationsResponse = apiService.getEngineerLocations().execute()
            
            if (!locationsResponse.isSuccessful || locationsResponse.body()?.code != 200) {
                return emptyList()
            }
            
            val locationsData = locationsResponse.body()?.data ?: emptyList()
            
            val locationMap = mutableMapOf<String, Map<String, Any?>>()
            
            locationsData.forEach { location ->
                try {
                    if (location is Map<*, *>) {
                        val id = location["id"]?.toString() ?: ""
                        if (id.isNotEmpty()) {
                            @Suppress("UNCHECKED_CAST")
                            locationMap[id] = location as Map<String, Any?>
                        }
                    }
                } catch (e: Exception) {
                }
            }
            
            return rawEngineers.mapNotNull { engineer ->
                try {
                    val engineerId = engineer.id
                    val locationInfo = locationMap[engineerId]
                    
                    if (locationInfo != null) {
                        val latitude = locationInfo["latitude"]?.toString()?.toDoubleOrNull() ?: 0.0
                        val longitude = locationInfo["longitude"]?.toString()?.toDoubleOrNull() ?: 0.0
                        val status = locationInfo["status"]?.toString() ?: "空闲"
                        val ordersCount = locationInfo["orderNumber"]?.toString()?.toIntOrNull() ?: 0
                        val completedToday = locationInfo["completedToday"]?.toString()?.toIntOrNull() ?: 0
                        val currentCustomer = locationInfo["currentCustomer"]?.toString() ?: ""
                        val locationUpdateTime = locationInfo["reportTime"]?.toString()
                        
                        if (latitude != 0.0 && longitude != 0.0) {
                            MapMarker(
                                id = "engineer_${engineer.id}",
                                name = engineer.name,
                                latitude = latitude,
                                longitude = longitude,
                                type = MarkerType.ENGINEER,
                                ordersCount = ordersCount,
                                completedToday = completedToday,
                                currentCustomer = currentCustomer,
                                status = status,
                                locationUpdateTime = locationUpdateTime
                            )
                        } else {
                            null
                        }
                    } else {
                        null
                    }
                } catch (e: Exception) {
                    null
                }
            }
        } catch (e: Exception) {
            return emptyList()
        }
    }
    
    private fun loadMarkersToMap(engineers: List<MapMarker>, customers: List<RepairOrder>) {
        engineerMarkers.clear()
        customerMarkers.clear()
        
        engineerMarkers.addAll(engineers)
        
        for (engineer in engineers) {
            try {
                markerManager.addOrUpdateMarker(engineer)
                updateEngineerWithWorkSummary(engineer)
            } catch (e: Exception) {
            }
        }
        
        for (order in customers) {
            try {
                markerManager.addOrUpdateRepairOrder(order)
                val mapMarker = order.toMapMarker()
                customerMarkers.add(mapMarker)
            } catch (e: Exception) {
            }
        }
        
        updateMarkers()
        zoomToFitAllMarkers()
    }
    
    private fun enableMyLocation() {
        try {
            if (PermissionUtils.hasLocationPermissions(requireContext())) {
                try {
                    tencentMap.isMyLocationEnabled = true
                    
                    val myLocationStyle = MyLocationStyle()
                    myLocationStyle.anchor(0.5f, 0.5f)
                    myLocationStyle.strokeColor(Color.TRANSPARENT)
                    myLocationStyle.strokeWidth(0)
                    myLocationStyle.fillColor(Color.parseColor("#4285F4"))
                    tencentMap.setMyLocationStyle(myLocationStyle)
                    
                    tencentMap.setOnMyLocationChangeListener { location ->
                        try {
                            if (location != null) {
                                val latLng = LatLng(location.latitude, location.longitude)
                                moveToLocation(latLng)
                                tencentMap.setOnMyLocationChangeListener(null)
                            }
                        } catch (e: Exception) {
                        }
                    }
                } catch (e: Exception) {
                }
            }
        } catch (e: Exception) {
        }
    }

    private fun moveToLocation(latLng: LatLng) {
        try {
            val cameraPosition = CameraPosition(latLng, 16f, 0f, 0f)
            tencentMap.animateCamera(CameraUpdateFactory.newCameraPosition(cameraPosition), 
                1000, 
                null)
        } catch (e: Exception) {
        }
    }
    
    private fun moveToInitialPosition() {
        val cameraPosition = CameraPosition(initialPosition, 12f, 0f, 0f)
        tencentMap.moveCamera(CameraUpdateFactory.newCameraPosition(cameraPosition))
    }
    
    private fun setupFilters() {
        checkboxEngineers.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                checkboxIdleEngineers.isChecked = false
                checkboxIdleEngineers.jumpDrawablesToCurrentState()
            }
            checkboxEngineers.jumpDrawablesToCurrentState()
            updateMarkers()
        }
        
        checkboxIdleEngineers.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                checkboxEngineers.isChecked = false
                checkboxEngineers.jumpDrawablesToCurrentState()
            }
            checkboxIdleEngineers.jumpDrawablesToCurrentState()
            updateMarkers()
        }
        
        checkboxCustomers.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                checkboxOverdueCustomers.isChecked = false
                checkboxOverdueCustomers.jumpDrawablesToCurrentState()
            }
            checkboxCustomers.jumpDrawablesToCurrentState()
            updateMarkers()
        }
        
        checkboxOverdueCustomers.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                checkboxCustomers.isChecked = false
                checkboxCustomers.jumpDrawablesToCurrentState()
            }
            checkboxOverdueCustomers.jumpDrawablesToCurrentState()
            updateMarkers()
        }
    }
    
    private fun setupFilterPanel() {
        panelFilterHeader.setOnClickListener {
            isFilterPanelExpanded = !isFilterPanelExpanded
            panelFilterContent.visibility = if (isFilterPanelExpanded) View.VISIBLE else View.GONE
            filterDivider.visibility = if (isFilterPanelExpanded) View.VISIBLE else View.GONE
            expandIcon.setImageResource(
                if (isFilterPanelExpanded) R.drawable.ic_expand_less
                else R.drawable.ic_expand_more
            )
            expandIcon.jumpDrawablesToCurrentState()
        }
    }
    
    private fun setupStatisticsPanel() {
        val scrollView = panelStatisticsContent.parent as ScrollView
        
        textStatisticsHeader.text = "报修客户：${customerMarkers.size}"
        statisticsExpandIcon.setImageResource(R.drawable.ic_expand_more)
        
        scrollView.visibility = View.GONE
        statisticsDivider.visibility = View.GONE
        
        val panelStatistics = view?.findViewById<LinearLayout>(R.id.panel_statistics)
        panelStatistics?.elevation = 6f
        
        panelStatisticsHeader.setOnClickListener {
            isStatisticsPanelExpanded = !isStatisticsPanelExpanded
            toggleStatisticsPanel(scrollView)
        }
    }
    
    private fun setupEngineersPanel() {
        val scrollView = panelEngineersContent.parent as ScrollView
        
        textEngineersHeader.text = "工程师：${engineerMarkers.size}个"
        engineersExpandIcon.setImageResource(R.drawable.ic_expand_more)
        
        scrollView.visibility = View.GONE
        engineersDivider.visibility = View.GONE
        
        val panelEngineers = view?.findViewById<LinearLayout>(R.id.panel_engineers)
        panelEngineers?.elevation = 4f
        
        panelEngineersHeader.setOnClickListener {
            isEngineersPanelExpanded = !isEngineersPanelExpanded
            toggleEngineersPanel(scrollView)
        }
    }
    
    private fun loadMarkerData() {
        engineerMarkers.clear()
        customerMarkers.clear()
        val engineers = listOf(
            MapMarker(
                id = "engineer_1",
                name = "张工程师",
                latitude = 30.6607,
                longitude = 104.0665,
                type = MarkerType.ENGINEER,
                ordersCount = 5,
                completedToday = 2,
                currentCustomer = "成都科技有限公司",
                status = "维修中"
            ),
            MapMarker(
                id = "engineer_2",
                name = "李工程师",
                latitude = 30.6550,
                longitude = 104.0700,
                type = MarkerType.ENGINEER,
                ordersCount = 3,
                completedToday = 1,
                currentCustomer = "",
                status = "空闲"
            ),
            MapMarker(
                id = "engineer_3",
                name = "王工程师",
                latitude = 30.6630,
                longitude = 104.0630,
                type = MarkerType.ENGINEER,
                ordersCount = 7,
                completedToday = 3,
                currentCustomer = "成都电子厂",
                status = "维修中"
            )
        )
        engineerMarkers.addAll(engineers)
        viewLifecycleOwner.lifecycleScope.launch {
            val unfinishedOrders = loadUnfinishedOrders()
            for (order in unfinishedOrders) {
                markerManager.addOrUpdateRepairOrder(order)
                val mapMarker = order.toMapMarker()
                customerMarkers.add(mapMarker)
            }
            updateStatistics()
            updateMarkers()
            zoomToFitAllMarkers()
        }
    }
    
    private fun zoomToFitAllMarkers() {
        if (engineerMarkers.isEmpty() && customerMarkers.isEmpty()) {
            // 如果没有标记，尝试使用LocationUpdateService的位置
            autoLocateToCurrentPosition()
            return
        }
        
        try {
            val boundsBuilder = LatLngBounds.builder()
            
            if (checkboxEngineers.isChecked) {
                for (marker in engineerMarkers) {
                    boundsBuilder.include(LatLng(marker.latitude, marker.longitude))
                }
            }
            
            if (checkboxCustomers.isChecked) {
                for (marker in customerMarkers) {
                    boundsBuilder.include(LatLng(marker.latitude, marker.longitude))
                }
            }
            
            val bounds = boundsBuilder.build()
            val padding = resources.getDimensionPixelSize(R.dimen.map_padding)
            tencentMap.moveCamera(CameraUpdateFactory.newLatLngBounds(bounds, padding))
        } catch (e: Exception) {
            autoLocateToCurrentPosition()
        }
    }
    
    /**
     * 自动定位到当前位置，优先使用LocationUpdateService的位置信息
     */
    private fun autoLocateToCurrentPosition() {
        // 首先尝试从LocationUpdateService获取最新位置
        val lastLocation = LocationUpdateService.getLastLocation()
        if (lastLocation != null) {
            val latLng = LatLng(lastLocation.latitude, lastLocation.longitude)
            moveToLocation(latLng)
            Log.d("MapFragment", "使用LocationUpdateService的位置: ${lastLocation.latitude}, ${lastLocation.longitude}")
            return
        }
        
        Log.d("MapFragment", "LocationUpdateService暂无位置信息，显示成都默认位置")
        // 如果LocationUpdateService没有位置信息，则显示成都默认位置
        moveToLocation(initialPosition)
    }
    
    private fun updateStatistics() {
        visibleEngineers = when {
            checkboxEngineers.isChecked -> engineerMarkers.size
            checkboxIdleEngineers.isChecked -> engineerMarkers.count { isIdle(it) }
            else -> 0
        }
        
        val visibleCustomerMarkers = when {
            checkboxCustomers.isChecked -> customerMarkers
            checkboxOverdueCustomers.isChecked -> customerMarkers.filter { isOverdue(it) }
            else -> emptyList()
        }
        
        visibleCustomers = visibleCustomerMarkers.size
        
        try {
            val allUncompletedOrders = viewModel.getAllUncompletedOrders()
            
            val overdueCount = allUncompletedOrders.count { isOverdueOrder(it) }
            
            val withLocationCount = allUncompletedOrders.count { it.hasLocation }
            
            val idleEngineerCount = engineerMarkers.count { isIdle(it) }
            
            checkboxCustomers.text = "客户 ${allUncompletedOrders.size}个(${withLocationCount}个有位置)"
            checkboxOverdueCustomers.text = "超时客户 (${overdueCount}个)"
            
            checkboxEngineers.text = "工程师 (${engineerMarkers.size}个)"
            checkboxIdleEngineers.text = "空闲工程师 (${idleEngineerCount}个)"
        
            textStatisticsHeader.text = "报修客户：${allUncompletedOrders.size}"
            textEngineersHeader.text = "工程师：${engineerMarkers.size}个"
        
            updateCustomerList(visibleCustomerMarkers)
            
            updateEngineersList(engineerMarkers)
        } catch (e: Exception) {
        }
    }
    
    private fun updateEngineersList(engineers: List<MapMarker>) {
        if (!isAdded || context == null || isFragmentDestroyed) {
            return
        }
        
        if (engineers.isEmpty()) {
            panelEngineersContent.removeAllViews()
            
            val textView = TextView(context).apply {
                text = "没有工程师信息"
                textSize = 12f
                setPadding(8, 8, 8, 8)
                setTextColor(Color.GRAY)
            }
            panelEngineersContent.addView(textView)
            return
        }
        
        val sortedEngineers = engineers.sortedWith(
            compareBy<MapMarker> { if (isIdle(it)) 0 else 1 }
        )

        val existingViewCount = panelEngineersContent.childCount / 2
        val needRebuild = existingViewCount == 0 || existingViewCount != sortedEngineers.size
        
        if (needRebuild) {
            try {
                panelEngineersContent.removeAllViews()
                
                for (engineer in sortedEngineers) {
                    addEngineerViewToPanel(engineer)
                    
                    if (engineer != sortedEngineers.last() && isAdded && !isFragmentDestroyed) {
                        val divider = View(requireContext()).apply {
                            layoutParams = LinearLayout.LayoutParams(
                                LinearLayout.LayoutParams.MATCH_PARENT, 
                                1
                            ).apply {
                                setMargins(8, 0, 8, 0)
                            }
                            setBackgroundColor(Color.parseColor("#E0E0E0"))
                        }
                        panelEngineersContent.addView(divider)
                    }
                }
            } catch (e: Exception) {
            }
        } else {
            try {
                var viewIndex = 0
                for (engineer in sortedEngineers) {
                    if (!isAdded || context == null || isFragmentDestroyed) {
                        return
                    }
                    
                    val engineerView = panelEngineersContent.getChildAt(viewIndex * 2) as? LinearLayout
                    engineerView?.let {
                        val nameText = it.getChildAt(0) as? TextView
                        nameText?.let { textView ->
                            val isEngineerIdle = isIdle(engineer)
                            if (isEngineerIdle) {
                                textView.text = "${engineer.name} (空闲)"
                                textView.setTextColor(Color.parseColor("#34C759"))
                            } else {
                                textView.text = "${engineer.name} (忙碌)"
                                textView.setTextColor(Color.parseColor("#FF9500"))
                            }
                        }
                        
                        it.setOnClickListener { view ->
                            if (!isAdded || context == null || isFragmentDestroyed) {
                                return@setOnClickListener
                            }
                            
                            val position = LatLng(engineer.latitude, engineer.longitude)
                            tencentMap.animateCamera(
                                CameraUpdateFactory.newLatLngZoom(position, 16f)
                            )
                            
                            expandedMarkerId = engineer.id
                            markerManager.toggleMarkerExpanded(engineer.id)
                            
                            isEngineersPanelExpanded = false
                            val scrollView = panelEngineersContent.parent as ScrollView
                            toggleEngineersPanel(scrollView)
                        }
                    }
                    viewIndex++
                }
            } catch (e: Exception) {
            }
        }
    }
    
    private fun addEngineerViewToPanel(engineer: MapMarker) {
        if (!isAdded || context == null || isFragmentDestroyed) {
            return
        }
        
        val isEngineerIdle = isIdle(engineer)
        
        val engineerView = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                resources.getDimensionPixelSize(R.dimen.min_height_customer_item)
            ).apply {
                setMargins(0, 4, 0, 4)
            }
            setPadding(8, 4, 8, 4)
            gravity = android.view.Gravity.CENTER_VERTICAL
            
            background = ContextCompat.getDrawable(requireContext(), R.drawable.clickable_background)
            
            setOnClickListener {
                val position = LatLng(engineer.latitude, engineer.longitude)
                tencentMap.animateCamera(
                    CameraUpdateFactory.newLatLngZoom(position, 16f)
                )
                
                expandedMarkerId = engineer.id
                markerManager.toggleMarkerExpanded(engineer.id)
                
                isEngineersPanelExpanded = false
                val scrollView = panelEngineersContent.parent as ScrollView
                toggleEngineersPanel(scrollView)
            }
            
            isClickable = true
            isFocusable = true
        }
        
        val nameText = TextView(requireContext()).apply {
            textSize = 14f
            setPadding(4, 0, 4, 0)
            setTypeface(null, android.graphics.Typeface.BOLD)
            
            if (isEngineerIdle) {
                text = "${engineer.name} (空闲)"
                setTextColor(Color.parseColor("#34C759"))
            } else {
                text = "${engineer.name} (忙碌)"
                setTextColor(Color.parseColor("#FF9500"))
            }
            
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            
            isClickable = false
        }
        engineerView.addView(nameText)
        
        panelEngineersContent.parent?.let {
            panelEngineersContent.addView(engineerView)
        }
    }
    
    private fun updateCustomerList(customers: List<MapMarker>) {
        panelStatisticsContent.removeAllViews()
        
        val allUncompletedOrders = viewModel.getAllUncompletedOrders()
        
        if (allUncompletedOrders.isEmpty()) {
            val textView = TextView(context).apply {
                text = "没有符合条件的未完成工单"
                textSize = 12f
                setPadding(8, 8, 8, 8)
                setTextColor(Color.GRAY)
            }
            panelStatisticsContent.addView(textView)
            return
        }
        
        val sortedCustomers = allUncompletedOrders.sortedWith(
            compareByDescending<RepairOrder> { isOverdueOrder(it) }
                .thenByDescending { order ->
                    when (order.statusValue) {
                        "pending_orders" -> 7
                        "engineer_receive" -> 6
                        "engineer_departure" -> 5
                        "engineer_arrive" -> 4
                        "wait_confirmed_report" -> 3
                        "to_be_settled" -> 2
                        "wait_audit" -> 1
                        else -> 0
                    }
                }
                .thenByDescending { it.hasLocation }
        )
        
        for (order in sortedCustomers) {
            val isOverdue = isOverdueOrder(order)
            
            val customerView = LinearLayout(context).apply {
                orientation = LinearLayout.HORIZONTAL
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    resources.getDimensionPixelSize(R.dimen.min_height_customer_item)
                ).apply {
                    setMargins(0, 4, 0, 4)
                }
                setPadding(8, 4, 8, 4)
                gravity = android.view.Gravity.CENTER_VERTICAL
                
                if (order.hasLocation) {
                    background = ContextCompat.getDrawable(context!!, R.drawable.clickable_background)
                    
                    setOnClickListener {
                        val position = LatLng(order.latitude, order.longitude)
                        tencentMap.animateCamera(
                            CameraUpdateFactory.newLatLngZoom(position, 16f)
                        )
                        
                        expandedMarkerId = order.id
                        markerManager.toggleMarkerExpanded(order.id)
                        
                        isStatisticsPanelExpanded = false
                        val scrollView = panelStatisticsContent.parent as ScrollView
                        toggleStatisticsPanel(scrollView)
                    }
                    
                    isClickable = true
                    isFocusable = true
                } else {
                    setBackgroundColor(Color.parseColor("#F5F5F5"))
                }
            }
            
            val nameText = TextView(context).apply {
                text = order.customerName
                textSize = 14f
                setPadding(4, 0, 4, 0)
                setTypeface(null, android.graphics.Typeface.BOLD)
                
                if (isOverdue) {
                    val overtimeType = when (order.statusValue) {

                        "pending_orders" -> "响应超时"
                        "engineer_receive" -> "响应超时"
                        "engineer_departure" -> "响应超时"
                        "engineer_arrive" -> "维修超时"
                        "wait_confirmed_report" -> "确认超时"
                        "to_be_settled" -> "确认超时"
                        "wait_audit" -> "确认超时"


                        else -> "超时"
                    }
                    text = "${order.customerName} (${overtimeType})"
                    setTextColor(Color.parseColor("#FF0000"))
                } else {
                    text = "${order.customerName}"
                    setTextColor(Color.parseColor("#34C759"))
                }
                
                if (!order.hasLocation) {
                    text = "${text} (无位置信息)"
                    setTextColor(Color.GRAY)
                }
                
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                )
                
                isClickable = false
            }
            customerView.addView(nameText)
            
            panelStatisticsContent.addView(customerView)
            
            if (order != sortedCustomers.last()) {
                val divider = View(context).apply {
                    layoutParams = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT, 
                        1
                    ).apply {
                        setMargins(8, 0, 8, 0)
                    }
                    setBackgroundColor(Color.parseColor("#E0E0E0"))
                }
                panelStatisticsContent.addView(divider)
            }
        }
    }
    
    private fun isOverdueOrder(order: RepairOrder): Boolean {
        if (order.createTime == null) {
            return false
        }
        
        val timeStatus = order.calculateTimeStatus()
        
        return when (timeStatus) {
            RepairOrder.TimeStatus.OVERTIME,
            RepairOrder.TimeStatus.REPAIR_OVERTIME,
            RepairOrder.TimeStatus.CONFIRM_OVERTIME
                -> true
            else -> false
        }
    }
    
    private fun measureContentWidth(): Int {
        var maxWidth = 0
        
        for (i in 0 until panelStatisticsContent.childCount) {
            val child = panelStatisticsContent.getChildAt(i)
            
            if (child is TextView || child is LinearLayout) {
                child.measure(
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                )
                val width = child.measuredWidth
                if (width > maxWidth) {
                    maxWidth = width
                }
            }
        }
        
        val padding = resources.getDimensionPixelSize(R.dimen.default_padding) * 2
        return maxWidth + padding
    }
    
    private fun isIdle(marker: MapMarker): Boolean {
        if (marker.type != MarkerType.ENGINEER) {
            return false
        }
        
        return marker.uncompletedOrders <= 0
    }
    
    private fun isOverdue(marker: MapMarker): Boolean {
        if (marker.type != MarkerType.CUSTOMER || marker.reportTime == null) {
            return false
        }
        
        val statusValue = marker.repairStatus
        
        val now = Calendar.getInstance().time
        
        when (statusValue) {
            "待确认维修报告", "wait_confirmed_report", "待结算", "to_be_settled", "待审核", "wait_audit" -> {
                val startDate = marker.waitConfirmTime ?: marker.reportTime
                val diff = now.time - startDate.time
                val diffHours = diff / (60 * 60 * 1000)
                return diffHours >= 24
            }
            "工程师到达", "engineer_arrive", "维修中" -> {
                val startDate = marker.arriveTime ?: marker.reportTime
                val diff = now.time - startDate.time
                val diffHours = diff / (60 * 60 * 1000)
                return diffHours >= 4
            }
            else -> {
                val timeoutHours = when {
                    marker.serviceType.contains("BUY_FULL") || marker.serviceType.contains("购机全保") -> 4
                    marker.serviceType.contains("BUY_HALF") || marker.serviceType.contains("购机半保") -> 4
                    marker.serviceType.contains("RENT_HALF") || marker.serviceType.contains("租赁半保") -> 4
                    marker.serviceType.contains("RENT_FULL") || marker.serviceType.contains("租赁全保") -> 4
                    marker.serviceType.contains("FINANCING_HALF") || marker.serviceType.contains("融资半保") -> 4
                    marker.serviceType.contains("FINANCING_FULL") || marker.serviceType.contains("融资全保") -> 4
                    marker.serviceType.contains("HALF") || marker.serviceType.contains("普通半保") -> 4
                    marker.serviceType.contains("ALL") || marker.serviceType.contains("普通全保") -> 4
                    marker.serviceType.contains("PACKAGE_ALL") || marker.serviceType.contains("包量全保") -> 4
                    marker.serviceType.contains("PACKAGE_HALF") || marker.serviceType.contains("包量半保") -> 4
                    
                    marker.serviceType.contains("WARRANTY") || marker.serviceType.contains("购机质保") -> 12
                    marker.serviceType.contains("QA") || marker.serviceType.contains("质保服务") -> 12
                    marker.serviceType.contains("QA_COMPONENT") || marker.serviceType.contains("质保含部件") -> 12
                    marker.serviceType.contains("MAINTENANCE") || marker.serviceType.contains("维保服务") -> 12
                    
                    marker.serviceType.contains("NO_WARRANTY") || marker.serviceType.contains("购机不保") -> 24
                    marker.serviceType.contains("SCATTERED") || marker.serviceType.contains("散修") -> 24
                    marker.serviceType.contains("OTHER") || marker.serviceType.contains("其他") -> 24
                    
                    else -> 24
                }
                
                val diff = now.time - marker.reportTime.time
                val diffHours = diff / (60 * 60 * 1000)
                return diffHours >= timeoutHours
            }
        }
    }
    
    private fun updateMarkers() {
        try {
            val expandedId = expandedMarkerId
            
            val markersToShow = mutableListOf<MapMarker>()
            
            if (checkboxEngineers.isChecked) {
                markersToShow.addAll(engineerMarkers)
            } else if (checkboxIdleEngineers.isChecked) {
                val idleEngineers = engineerMarkers.filter { isIdle(it) }
                markersToShow.addAll(idleEngineers)
            }
            
            val overdueCustomers = customerMarkers.filter { isOverdue(it) }
            
            if (checkboxCustomers.isChecked) {
                markersToShow.addAll(customerMarkers)
            } else if (checkboxOverdueCustomers.isChecked) {
                markersToShow.addAll(overdueCustomers)
            }
            
            try {
                val existingMarkers = markerManager.getAllMarkers().map { it.mapMarker.id }
                val markersToShowIds = markersToShow.map { it.id }
                
                val markersToAdd = markersToShow.filter { !existingMarkers.contains(it.id) }
                
                val markersToRemove = existingMarkers.filter { !markersToShowIds.contains(it) }
                
                for (markerId in markersToRemove) {
                    try {
                        markerManager.removeMarker(markerId)
                    } catch (e: Exception) {
                    }
                }
                
                for (marker in markersToAdd) {
                    try {
                        markerManager.addOrUpdateMarker(marker)
                    } catch (e: Exception) {
                    }
                }
                
                if (expandedId != null) {
                    try {
                        markerManager.findMapMarkerById(expandedId)?.let {
                            expandedMarkerId = expandedId
                            markerManager.toggleMarkerExpanded(expandedId)
                        } ?: run {
                            expandedMarkerId = null
                        }
                    } catch (e: Exception) {
                    }
                }
                
                if (markersToAdd.isNotEmpty() || markersToRemove.isNotEmpty()) {
                    try {
                        if ((checkboxEngineers.isChecked && engineerMarkers.isNotEmpty()) || 
                            (checkboxCustomers.isChecked && customerMarkers.isNotEmpty())) {
                            val bounds = LatLngBounds.builder()
                                .include(LatLng(30.66, 104.07))
                                .build()
                            
                            for (marker in markersToShow) {
                                try {
                                    bounds.include(LatLng(marker.latitude, marker.longitude))
                                } catch (e: Exception) {
                                }
                            }
                            
            tencentMap.animateCamera(
                                CameraUpdateFactory.newLatLngBounds(bounds, 100)
                            )
                        }
                    } catch (e: Exception) {
                    }
                }
            } catch (e: Exception) {
            }
            
            updateStatistics()
        } catch (e: Exception) {
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        when (requestCode) {
            PermissionUtils.LOCATION_PERMISSION_REQUEST_CODE -> {
                val allBasicPermissionsGranted = grantResults.isNotEmpty() && 
                                               grantResults.all { it == PackageManager.PERMISSION_GRANTED }
                
                if (allBasicPermissionsGranted) {
                Toast.makeText(context, "位置权限已获取，正在获取位置...", Toast.LENGTH_SHORT).show()
                enableMyLocation()
                    
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && 
                        !PermissionUtils.hasBackgroundLocationPermission(requireContext())) {
                        view?.postDelayed({
                            AlertDialog.Builder(requireContext())
                                .setTitle("需要后台位置权限")
                                .setMessage("为了能够在应用在后台运行时也能持续获取位置，需要授予后台位置权限。请在接下来的对话框中选择\"始终允许\"。")
                                .setPositiveButton("授予权限") { dialog: DialogInterface, which: Int ->
                                    PermissionUtils.requestBackgroundLocationPermission(this)
                                }
                                .setNegativeButton("稍后再说", null)
                                .show()
                        }, 500)
                    }
                
                view?.postDelayed({
                    requestLocationAndMoveTo()
                }, 1000)
            } else {
                    showLocationPermissionStatus()
                Toast.makeText(context, "需要位置权限才能定位到您的位置", Toast.LENGTH_SHORT).show()
                }
            }
            
            PermissionUtils.BACKGROUND_LOCATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(context, "后台位置权限已获取，可在后台更新位置", Toast.LENGTH_SHORT).show()
                    startLocationServiceIfEngineer()
                } else {
                    Toast.makeText(context, "未获取后台位置权限，应用在后台时将无法更新位置", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    override fun onStart() {
        super.onStart()
        try {
            if (::mapView.isInitialized) {
        mapView.onStart()
            }
        } catch (e: Exception) {
        }
    }

    override fun onResume() {
        super.onResume()

        isFragmentActive = true
        try {
            if (::mapView.isInitialized) {
                mapView.onResume()
            }
            startLocationServiceIfEngineer()
        } catch (e: Exception) {
        }

        if (isReturningFromDetailPage) {
            isReturningFromDetailPage = false
            // 从工单详情页返回时，需要恢复地图状态
            val cachedState = viewModel.getCachedState()
            if (cachedState != null && isMapInitialized && ::markerManager.isInitialized) {
                // 恢复地图位置
                tencentMap.moveCamera(CameraUpdateFactory.newCameraPosition(cachedState.cameraPosition))
                
                // 同步展开标记ID
                expandedMarkerId = cachedState.expandedMarkerId
                
                // 恢复筛选状态
                checkboxEngineers.isChecked = cachedState.filterState.isEngineersChecked
                checkboxIdleEngineers.isChecked = cachedState.filterState.isIdleEngineersChecked
                checkboxCustomers.isChecked = cachedState.filterState.isCustomersChecked
                checkboxOverdueCustomers.isChecked = cachedState.filterState.isOverdueCustomersChecked
                
                // 恢复标记数据
                customerMarkers.clear()
                customerMarkers.addAll(cachedState.customerMarkers)
                engineerMarkers.clear()
                engineerMarkers.addAll(cachedState.engineerMarkers)
                
                // 确保所有标记都存在于地图上
                val existingMarkerIds = markerManager.getAllMarkers().map { it.mapMarker.id }.toSet()
                
                // 恢复所有未完成工单数据
                val allOrders = viewModel.getAllUncompletedOrders()
                
                // 添加缺失的客户标记，并恢复工单数据
                for (marker in cachedState.customerMarkers) {
                    val repairOrder = allOrders.find { it.id == marker.id }
                    if (!existingMarkerIds.contains(marker.id)) {
                        // 标记不存在，需要添加
                        if (repairOrder != null) {
                            // 如果找到了RepairOrder，使用addOrUpdateRepairOrder
                            markerManager.addOrUpdateRepairOrder(repairOrder)
                        } else {
                            // 否则只添加MapMarker
                            markerManager.addOrUpdateMarker(marker)
                        }
                    } else {
                        // 标记已存在，但需要确保工单数据也存在
                        if (repairOrder != null) {
                            // 更新工单数据到markerManager
                            markerManager.addOrUpdateRepairOrder(repairOrder)
                        }
                    }
                }
                
                // 添加缺失的工程师标记
                for (marker in cachedState.engineerMarkers) {
                    if (!existingMarkerIds.contains(marker.id)) {
                        markerManager.addOrUpdateMarker(marker)
                    }
                }
                
                // 更新标记显示
                updateMarkers()
            }
            return
        }
    }

    override fun onPause() {
        super.onPause()
        
        // 保存当前状态（无论是否跳转到详情页）
        if (!isRestoringState && isMapInitialized) {
            saveCurrentStateToViewModel()
        }
        
        // 如果是跳转到详情页，只调用mapView.onPause()，不释放资源
        if (isReturningFromDetailPage) {
            try {
                if (::mapView.isInitialized) {
                    mapView.onPause()
                }
            } catch (e: Exception) {
            }
            return
        }
        
        // 非详情页跳转的情况下，准备释放资源
        if (!isStateSaved && isAdded) {
            overlayView?.apply {
                visibility = View.VISIBLE
                animate()
                    .alpha(1f)
                    .setDuration(100)
                    .start()
            }
            
            prepareMapResourceRelease()
        }
        
        try {
            if (::mapView.isInitialized) {
                mapView.onPause()
            }
        } catch (e: Exception) {
        }
    }
    
    override fun onStop() {
        try {
            if (::mapView.isInitialized) {
                mapView.onStop()
            }
        } catch (e: Exception) {
        }
        super.onStop()
    }

    override fun onDestroyView() {
        isFragmentActive = false
        
        releaseMapResources()
        
        try {
            overlayView?.let { overlay ->
                (overlay.parent as? ViewGroup)?.removeView(overlay)
                overlayView = null
            }
        } catch (e: Exception) {
        }
        
        super.onDestroyView()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
    }

    private fun showLocationHelp() {
        if (!PermissionUtils.hasLocationPermissions(requireContext())) {
            Toast.makeText(context, 
                "提示: 点击右下角定位按钮可使用位置功能，长按可查看权限状态", 
                Toast.LENGTH_LONG).show()
        }
    }

    private fun showLocationPermissionStatus() {
        val fineLocationGranted = ActivityCompat.checkSelfPermission(
            requireContext(), 
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        val coarseLocationGranted = ActivityCompat.checkSelfPermission(
            requireContext(), 
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        val backgroundLocationGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ActivityCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.ACCESS_BACKGROUND_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
        
        val message = when {
            fineLocationGranted && coarseLocationGranted && backgroundLocationGranted -> 
                "所有位置权限均已授予，可正常使用定位功能"
            fineLocationGranted && coarseLocationGranted -> 
                "基本位置权限已授予，但未授予后台位置权限"
            fineLocationGranted -> 
                "仅精确位置权限已授予"
            coarseLocationGranted -> 
                "仅粗略位置权限已授予"
            else -> 
                "未授予任何位置权限，请在系统设置中启用位置权限"
        }
        
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
        
        if (!fineLocationGranted || !coarseLocationGranted) {
            view?.postDelayed({
                Toast.makeText(context, 
                    "请前往：设置 > 应用 > 维修工单应用 > 权限 > 位置，并选择\"始终允许\"", 
                    Toast.LENGTH_LONG).show()
            }, 1500)
        }
        else if (fineLocationGranted && coarseLocationGranted && !backgroundLocationGranted && 
                 Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            view?.postDelayed({
                AlertDialog.Builder(requireContext())
                    .setTitle("需要后台位置权限")
                    .setMessage("为了在应用在后台运行时也能提供位置服务，请在权限设置中选择\"始终允许\"")
                    .setPositiveButton("去设置") { dialog: DialogInterface, which: Int ->
                        PermissionUtils.requestBackgroundLocationPermission(this)
                    }
                    .setNegativeButton("稍后再说", null)
                    .show()
            }, 1000)
        }
    }

    private fun isLocationMarker(marker: Marker): Boolean {
        for (markerData in markerManager.getAllMarkers()) {
            if (markerData.marker == marker) {
                return true
            }
        }
        return false
    }

    private fun isOrderCompleted(status: String): Boolean {
        return status.contains("完成") || status.contains("关闭") || status.contains("取消")
    }
    
    private suspend fun loadUnfinishedOrders(): List<RepairOrder> = withContext(Dispatchers.IO) {
        val orders = mutableListOf<RepairOrder>()
        val allUncompletedOrders = mutableListOf<RepairOrder>()
        var hasError = false
        var errorMessage = ""

        val filterStats = HashMap<String, Int>()
        val excludedOrderIds = mutableListOf<String>()
        try {
            val jsonObject = com.google.gson.JsonObject().apply {
                addProperty("pageNumber", "1")
                addProperty("pageSize", "200")
                val statusArray = com.google.gson.JsonArray()
                statusArray.add("pending_orders")
                statusArray.add("engineer_receive")
                statusArray.add("engineer_departure")
                statusArray.add("engineer_arrive")
                statusArray.add("wait_confirmed_report")
                statusArray.add("to_be_settled")
                add("status", statusArray)
            }
            val apiService = ApiClient.createService(WorkOrderApi::class.java)
            val response = apiService.getWorkOrderListWithArrayParams(jsonObject).execute()
            if (response.isSuccessful) {
                val responseData = response.body()
                val workOrderItems = responseData?.data?.rows
                val initialCount = workOrderItems?.size ?: 0
                filterStats["API返回原始数据"] = initialCount
                workOrderItems?.let { items ->
                    val uncompletedOrders = items.filter { order ->
                        val statusValue = order.status?.value ?: ""
                        val statusLabel = order.status?.label ?: ""
                        val isNotCompleted = statusValue != "completed" && 
                            statusValue != "close" && 
                            statusValue != "cancel" &&
                            !statusLabel.contains("完成") && 
                            !statusLabel.contains("关闭") && 
                            !statusLabel.contains("取消")
                        if (!isNotCompleted && order.id != null) {
                            excludedOrderIds.add("初筛-${order.id}-${statusValue}-${statusLabel}")
                        }
                        isNotCompleted
                    }
                    filterStats["初次筛选后"] = uncompletedOrders.size
                    val semaphore = kotlinx.coroutines.sync.Semaphore(50)
                    val detailResults = uncompletedOrders.map { order ->
                        async {
                            semaphore.withPermit {
                                try {
                                    val detailResponse = apiService.getWorkOrderDetail(order.id ?: "").execute()
                                    val workDetailResponse = apiService.getWorkDetail(order.id ?: "").execute()
                                    Pair(order, Pair(detailResponse, workDetailResponse))
                                } catch (e: Exception) {
                                    Pair(order, null)
                                }
                            }
                        }
                    }.awaitAll()
                    for (result in detailResults) {
                        val order = result.first
                        val pair = result.second
                        try {
                            var orderLat: Double? = order.latitude
                            var orderLng: Double? = order.longitude
                            var hasValidLocation = false
                            var expectArriveTime = ""
                            var prospectArriveTime = ""
                            var orderReceiveTime: java.util.Date? = null
                            var departureTime: java.util.Date? = null
                            var actualArriveTime: java.util.Date? = null
                            var sendReportTime: java.util.Date? = null
                            if (pair != null) {
                                val detailResponse = pair.first
                                val workDetailResponse = pair.second
                                if (detailResponse.isSuccessful) {
                                    val detailData = detailResponse.body()?.data
                                    if (detailData != null && detailData is Map<*, *>) {
                                        val lat = detailData["latitude"]
                                        val lng = detailData["longitude"]
                                        val customerData = detailData["customer"] as? Map<*, *>
                                        var customerLat: Any? = null
                                        var customerLng: Any? = null
                                        val customerLocation = customerData?.get("location") as? Map<*, *>
                                        if (customerLocation != null) {
                                            customerLat = customerLocation["latitude"]
                                            customerLng = customerLocation["longitude"]
                                        }
                                        if (customerLat == null || customerLng == null) {
                                            customerLat = customerData?.get("latitude")
                                            customerLng = customerData?.get("longitude")
                                        }
                                        if (lat != null) {
                                            val value = lat.toString().toDoubleOrNull()
                                            if (value != null && value != 0.0) {
                                                orderLat = value
                                            }
                                        }
                                        if (lng != null) {
                                            val value = lng.toString().toDoubleOrNull()
                                            if (value != null && value != 0.0) {
                                                orderLng = value
                                            }
                                        }
                                        if ((orderLat == null || orderLat == 0.0) && customerLat != null) {
                                            val value = customerLat.toString().toDoubleOrNull()
                                            if (value != null && value != 0.0) {
                                                orderLat = value
                                            }
                                        }
                                        if ((orderLng == null || orderLng == 0.0) && customerLng != null) {
                                            val value = customerLng.toString().toDoubleOrNull()
                                            if (value != null && value != 0.0) {
                                                orderLng = value
                                            }
                                        }
                                        hasValidLocation = orderLat != null && orderLng != null && orderLat != 0.0 && orderLng != 0.0
                                    }
                                }
                                if (workDetailResponse.isSuccessful) {
                                    val workDetailData = workDetailResponse.body()?.data
                                    if (workDetailData != null && workDetailData is Map<*, *>) {
                                        expectArriveTime = workDetailData["expectArriveTime"]?.toString() ?: ""
                                        prospectArriveTime = workDetailData["prospectArriveTime"]?.toString() ?: ""
                                        orderReceiveTime = workDetailData["orderReceiveTime"]?.toString()?.let { parseApiDate(it) }
                                        departureTime = workDetailData["departureTime"]?.toString()?.let { parseApiDate(it) }
                                        actualArriveTime = workDetailData["actualArriveTime"]?.toString()?.let { parseApiDate(it) }
                                        sendReportTime = workDetailData["sendReportTime"]?.toString()?.let { parseApiDate(it) }
                                    }
                                }
                            } else {
                                hasError = true
                                errorMessage = "部分工单详情获取失败"
                            }
                            val createDate = try {
                                if (!order.createdAt.isNullOrEmpty()) {
                                    val sdf = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                                    sdf.parse(order.createdAt)
                                } else {
                                    java.util.Date()
                                }
                            } catch (e: Exception) {
                                java.util.Date()
                            }
                            val repairOrder = RepairOrder(
                                id = order.id ?: "",
                                code = order.code ?: "",
                                customerId = order.customerId ?: "",
                                customerName = order.customerName ?: "未知客户",
                                address = order.customer?.address ?: "",
                                latitude = orderLat ?: 0.0,
                                longitude = orderLng ?: 0.0,
                                engineerId = order.engineerId?.id ?: "",
                                engineerName = order.engineerId?.name ?: "",
                                status = order.status?.label ?: "未知状态",
                                statusValue = order.status?.value ?: "",
                                createTime = createDate,
                                receiveTime = orderReceiveTime,
                                departureTime = departureTime,
                                actualArriveTime = actualArriveTime,
                                sendReportTime = sendReportTime,
                                machineModel = order.productInfo ?: "未知型号",
                                deviceGroup = order.deviceGroup?.label ?: "",
                                serviceType = order.serType?.label ?: "",
                                expectArriveTime = expectArriveTime,
                                prospectArriveTime = prospectArriveTime,
                                hasLocation = hasValidLocation
                            )
                            allUncompletedOrders.add(repairOrder)
                            if (hasValidLocation) {
                                orders.add(repairOrder)
                            }
                        } catch (e: Exception) {
                            hasError = true
                            errorMessage = "部分工单处理异常: ${e.message}"
                            excludedOrderIds.add("异常-${order.id}-${e.message}")
                        }
                    }
                }
            } else {
                hasError = true
                errorMessage = "获取工单列表失败: ${response.code()}"
            }
            viewModel.saveAllUncompletedOrders(allUncompletedOrders)
            filterStats["最终未完成工单总数"] = allUncompletedOrders.size
            filterStats["有位置工单数量"] = orders.size
            filterStats["无位置工单数量"] = allUncompletedOrders.size - orders.size
            if (allUncompletedOrders.isEmpty() && hasError) {
                activity?.runOnUiThread {
                    Toast.makeText(context, "获取工单数据失败，请检查网络连接", Toast.LENGTH_SHORT).show()
                }
            } else if (hasError && orders.isEmpty() && allUncompletedOrders.isNotEmpty()) {
                safeShowToast("获取到${allUncompletedOrders.size}个工单，但没有位置信息可在地图显示")
            }
        } catch (e: Exception) {
            viewModel.saveAllUncompletedOrders(allUncompletedOrders)
            safeShowToast("加载工单数据时发生异常: ${e.message}")
        }
        if (allUncompletedOrders.isEmpty() && orders.isEmpty()) {
            safeShowToast("没有未完成的工单")
        }
        return@withContext orders
    }
    
    private fun parseApiDate(dateString: String): Date {
        return try {
            val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            format.parse(dateString) ?: Date()
        } catch (e: Exception) {
            Date()
        }
    }

    override fun onLowMemory() {
        super.onLowMemory()
        try {
            mapView.javaClass.getMethod("onLowMemory").invoke(mapView)
        } catch (e: Exception) {
        }
    }

    private fun refreshMapData() {
        viewModel.markForRefresh()
        progressLoading.visibility = View.VISIBLE
        fabRefresh.isEnabled = false

        viewLifecycleOwner.lifecycleScope.launch {
            try {
                val customers = loadUnfinishedOrders()
                val engineers = withContext(Dispatchers.IO) { prepareEngineerData() }
                if (!isFragmentActive) return@launch
                try {
                    loadMarkersToMap(engineers, customers)
                    progressLoading.visibility = View.GONE
                    fabRefresh.isEnabled = true
                    startLocationServiceIfEngineer()
                } catch (e: Exception) {
                    progressLoading.visibility = View.GONE
                    fabRefresh.isEnabled = true
                    safeShowToast("刷新地图数据失败: ${e.message}")
                }
            } catch (e: Exception) {
                progressLoading.visibility = View.GONE
                fabRefresh.isEnabled = true
                safeShowToast("加载数据失败: ${e.message}")
            }
        }
    }
    
    private fun startLocationServiceIfEngineer() {
        try {
            val sharedPrefs = SharedPrefsManager(requireContext())
            val userRole = sharedPrefs.getUserRole()
            val isEngineer = userRole.contains("engineer", ignoreCase = true) || userRole.contains("工程师", ignoreCase = true)
            
            if (isEngineer) {
                if (!LocationUpdateService.isRunning()) {
                    val serviceIntent = Intent(requireContext(), LocationUpdateService::class.java).apply {
                        action = LocationUpdateService.ACTION_START_LOCATION_SERVICE
                    }
                    requireContext().startForegroundService(serviceIntent)
                }
            }
        } catch (e: Exception) {
        }
    }

    private fun transparentLoadData() {
        val cachedState = viewModel.getCachedState()
        if (cachedState != null) {
            progressLoading.visibility = View.GONE
            
            restoreMapState(cachedState)
            
            if (viewModel.checkNeedRefresh()) {
                safeShowToast("正在后台更新数据...")
                loadDataInBackground()
            }
        } else {
            progressLoading.visibility = View.VISIBLE
            loadMapData()
        }
    }
    
    private fun loadDataInBackground() {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                val customerData = loadUnfinishedOrders()
                val engineerData = withContext(Dispatchers.IO) { prepareEngineerData() }
                if (!isFragmentActive) return@launch
                val currentCamera = tencentMap.cameraPosition
                val expandedId = markerManager.expandedMarkerId
                val filterState = getCurrentFilterState()
                updateMarkersKeepingViewState(customerData, engineerData, currentCamera, expandedId, filterState)
                updateStatistics()
                saveCurrentStateToViewModel()
            } catch (e: Exception) {
                if (isFragmentActive) {
                    safeShowToast("后台刷新数据失败，使用缓存数据")
                }
            }
        }
    }
    
    private fun updateMarkersKeepingViewState(
        customerData: List<RepairOrder>,
        engineerData: List<MapMarker>,
        cameraPosition: CameraPosition,
        expandedMarkerId: String?,
        filterState: FilterState
    ) {
        try {
            checkboxEngineers.isChecked = filterState.isEngineersChecked
            checkboxIdleEngineers.isChecked = filterState.isIdleEngineersChecked
            checkboxCustomers.isChecked = filterState.isCustomersChecked
            checkboxOverdueCustomers.isChecked = filterState.isOverdueCustomersChecked
            
            markerManager.removeAllMarkersKeepingStyles()
            
            customerMarkers.clear()
            for (order in customerData) {
                markerManager.addOrUpdateRepairOrder(order)
                val mapMarker = order.toMapMarker()
                customerMarkers.add(mapMarker)
            }
            
            engineerMarkers.clear()
            engineerMarkers.addAll(engineerData)
            for (engineer in engineerData) {
                markerManager.addOrUpdateMarker(engineer)
            }
            
            tencentMap.moveCamera(CameraUpdateFactory.newCameraPosition(cameraPosition))
            
            expandedMarkerId?.let {
                markerManager.toggleMarkerExpanded(it)
            }
            
            updateMarkers()
        } catch (e: Exception) {
        }
    }
    
    private fun restoreMapStateFromCache() {
        if (isReturningFromDetailPage) {
            return
        }
        
        isRestoringState = true
        
        try {
            val state = viewModel.getCachedState()
            if (state != null) {
                restoreMapState(state)
                
                viewModel.resetRefreshFlag()
                
                safeShowToast("已恢复上次查看状态")
            } else {
                progressLoading.visibility = View.VISIBLE
                loadMapData()
            }
        } catch (e: Exception) {
            progressLoading.visibility = View.VISIBLE
            loadMapData()
        } finally {
            isRestoringState = false
        }
    }
    
    /**
     * 从工单详情页返回时恢复地图状态
     */
    private fun restoreMapStateAfterDetailReturn() {
        try {
            val state = viewModel.getCachedState()
            if (state != null && isMapInitialized) {
                // 直接恢复地图位置和标记状态，不需要检查isReturningFromDetailPage标志
                restoreMapStateForDetailReturn(state)
                safeShowToast("已恢复地图位置")
            } else if (isMapInitialized) {
                // 如果没有缓存状态，至少保持当前位置不变
                safeShowToast("保持当前地图位置")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "恢复地图状态失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun restoreMapState(state: MapState?) {
        if (isReturningFromDetailPage) {
            return
        }
        if (state == null) {
            return
        }
        if (!isMapInitialized || markerManager == null) {
            Handler(Looper.getMainLooper()).postDelayed({
                restoreMapState(state) 
            }, 500)
            return
        }
        try {
            progressLoading.visibility = View.GONE
            
            checkboxEngineers.isChecked = state.filterState.isEngineersChecked
            checkboxIdleEngineers.isChecked = state.filterState.isIdleEngineersChecked
            checkboxCustomers.isChecked = state.filterState.isCustomersChecked
            checkboxOverdueCustomers.isChecked = state.filterState.isOverdueCustomersChecked
            
            customerMarkers.clear()
            customerMarkers.addAll(state.customerMarkers)
            
            engineerMarkers.clear()
            engineerMarkers.addAll(state.engineerMarkers)
            
            markerManager.removeAllMarkers()
            
            for (marker in state.customerMarkers) {
                markerManager.addOrUpdateMarker(marker)
            }
            
            for (marker in state.engineerMarkers) {
                markerManager.addOrUpdateMarker(marker)
            }
            
            tencentMap.moveCamera(CameraUpdateFactory.newCameraPosition(state.cameraPosition))
            
            state.expandedMarkerId?.let {
                markerManager.toggleMarkerExpanded(it)
            }
            
            updateMarkers()
            
            visibleEngineers = state.visibleEngineers
            visibleCustomers = state.visibleCustomers
            updateStatisticsView()
        } catch (e: Exception) {
            
            progressLoading.visibility = View.VISIBLE
            loadMapData()
        }
    }
    
    /**
     * 专门用于从工单详情页返回时恢复地图状态的方法
     * 与restoreMapState不同，这个方法不检查isReturningFromDetailPage标志
     */
    private fun restoreMapStateForDetailReturn(state: MapState?) {
        if (state == null) {
            return
        }
        if (!isMapInitialized || !::markerManager.isInitialized) {
            Handler(Looper.getMainLooper()).postDelayed({
                restoreMapStateForDetailReturn(state) 
            }, 300)
            return
        }
        try {
            // 恢复筛选状态
            checkboxEngineers.isChecked = state.filterState.isEngineersChecked
            checkboxIdleEngineers.isChecked = state.filterState.isIdleEngineersChecked
            checkboxCustomers.isChecked = state.filterState.isCustomersChecked
            checkboxOverdueCustomers.isChecked = state.filterState.isOverdueCustomersChecked
            
            // 恢复标记数据到内存列表
            customerMarkers.clear()
            customerMarkers.addAll(state.customerMarkers)
            
            engineerMarkers.clear()
            engineerMarkers.addAll(state.engineerMarkers)
            
            // 恢复地图位置 - 这是最重要的
            tencentMap.moveCamera(CameraUpdateFactory.newCameraPosition(state.cameraPosition))
            
            // 只更新标记的展开状态，不重新切换
            // 确保 expandedMarkerId 与 markerManager 的状态一致
            expandedMarkerId = state.expandedMarkerId
            
            // 如果有保存的展开标记ID，但与当前管理器的不一致，需要同步
            if (state.expandedMarkerId != null && markerManager.expandedMarkerId != state.expandedMarkerId) {
                // 先检查该标记是否还存在
                val marker = markerManager.findMapMarkerById(state.expandedMarkerId)
                if (marker != null) {
                    // 如果管理器中有其他展开的标记，先折叠它
                    markerManager.expandedMarkerId?.let { currentExpandedId ->
                        if (currentExpandedId != state.expandedMarkerId) {
                            markerManager.toggleMarkerExpanded(currentExpandedId)
                        }
                    }
                    // 然后展开保存的标记
                    markerManager.toggleMarkerExpanded(state.expandedMarkerId)
                }
            }
            
            // 更新标记显示状态（只更新可见性，不重建）
            updateMarkers()
            
            // 恢复统计信息
            visibleEngineers = state.visibleEngineers
            visibleCustomers = state.visibleCustomers
            updateStatisticsView()
            
        } catch (e: Exception) {
            // 如果恢复失败，至少确保地图位置正确
            try {
                if (state.cameraPosition != null) {
                    tencentMap.moveCamera(CameraUpdateFactory.newCameraPosition(state.cameraPosition))
                }
            } catch (ex: Exception) {
                // 最后的备用方案，什么都不做，保持当前状态
            }
        }
    }
    
    private fun getCurrentFilterState(): FilterState {
        return FilterState(
            isEngineersChecked = checkboxEngineers.isChecked,
            isIdleEngineersChecked = checkboxIdleEngineers.isChecked,
            isCustomersChecked = checkboxCustomers.isChecked,
            isOverdueCustomersChecked = checkboxOverdueCustomers.isChecked
        )
    }
    
    private fun saveCurrentStateToViewModel() {
        try {
            val filterState = getCurrentFilterState()
            
            val state = MapState(
                customerMarkers = ArrayList(customerMarkers),
                engineerMarkers = ArrayList(engineerMarkers),
                expandedMarkerId = markerManager.expandedMarkerId,
                cameraPosition = tencentMap.cameraPosition,
                filterState = filterState,
                visibleEngineers = visibleEngineers,
                visibleCustomers = visibleCustomers
            )
            
            viewModel.saveMapState(state)
        } catch (e: Exception) {
        }
    }

    private fun loadMapData() {
        try {
            progressLoading.visibility = View.VISIBLE
            customerMarkers.clear()
            engineerMarkers.clear()
            markerManager.removeAllMarkers()
            viewLifecycleOwner.lifecycleScope.launch {
                try {
                    val customerData = loadUnfinishedOrders()
                    val engineerData = withContext(Dispatchers.IO) { prepareEngineerData() }
                    if (!isFragmentActive) return@launch
                    try {
                        for (order in customerData) {
                            markerManager.addOrUpdateRepairOrder(order)
                            val mapMarker = order.toMapMarker()
                            customerMarkers.add(mapMarker)
                        }
                        engineerMarkers.addAll(engineerData)
                        val totalEngineers = engineerData.size
                        var updatedEngineers = 0
                        if (totalEngineers == 0) {
                            progressLoading.visibility = View.GONE
                        }
                        for (engineer in engineerData) {
                            markerManager.addOrUpdateMarker(engineer)
                            val engineerId = engineer.id.replace("engineer_", "")
                            getEngineerWorkSummary(engineerId) { summaryData ->
                                updatedEngineers++
                                if (summaryData != null) {
                                    val updatedEngineer = engineer.copy(
                                        completedToday = summaryData.completedWorkNum,
                                        uncompletedOrders = summaryData.receiveNum,
                                        ordersCount = summaryData.todayWorkNum,
                                        status = if (summaryData.receiveNum > 0) "忙碌中" else "空闲"
                                    )
                                    markerManager.addOrUpdateMarker(updatedEngineer)
                                    updateMarkers()
                                } else {
                                    markerManager.addOrUpdateMarker(engineer)
                                    updateMarkers()
                                }
                                if (updatedEngineers >= totalEngineers) {
                                    progressLoading.visibility = View.GONE
                                }
                            }
                        }
                        val allOrders = viewModel.getAllUncompletedOrders()
                        updateStatistics()
                        updateMarkers()
                        zoomToFitAllMarkers()
                        saveCurrentStateToViewModel()
                        if (customerData.isEmpty() && allOrders.isEmpty()) {
                            safeShowToast("暂无未完成工单数据")
                        } else if (customerData.isEmpty() && allOrders.isNotEmpty()) {
                            safeShowToast("获取到${allOrders.size}个未完成工单，但没有位置信息可在地图显示")
                        } else {
                            safeShowToast("地图已加载，显示${customerData.size}个有位置信息的工单")
                        }
                        progressLoading.visibility = View.GONE
                    } catch (e: Exception) {
                        progressLoading.visibility = View.GONE
                        if (customerMarkers.isEmpty() && viewModel.getAllUncompletedOrders().isEmpty()) {
                            safeShowToast("加载地图数据失败: ${e.message}")
                        } else if (customerMarkers.isNotEmpty()) {
                            safeShowToast("已显示${customerMarkers.size}个标记点")
                        } else if (viewModel.getAllUncompletedOrders().isNotEmpty()) {
                            val count = viewModel.getAllUncompletedOrders().size
                            safeShowToast("获取到${count}个工单，但没有位置信息可显示")
                        }
                    }
                } catch (e: Exception) {
                    progressLoading.visibility = View.GONE
                    if (viewModel.getAllUncompletedOrders().isEmpty()) {
                        safeShowToast("加载数据失败: ${e.message}")
                    }
                }
            }
        } catch (e: Exception) {
            progressLoading.visibility = View.GONE
            safeShowToast("启动数据加载失败: ${e.message}")
        }
    }

    private fun updateStatisticsView() {
        try {
            textStatisticsHeader.text = "报修客户：${visibleCustomers}"
            
            val overdueCount = customerMarkers.count { isOverdue(it) }
            checkboxCustomers.text = "客户 (${customerMarkers.size}个未完成工单)"
            checkboxOverdueCustomers.text = "超时客户 (${overdueCount}个)"
            
            val visibleCustomerMarkers = when {
                checkboxCustomers.isChecked -> customerMarkers
                checkboxOverdueCustomers.isChecked -> customerMarkers.filter { isOverdue(it) }
                else -> emptyList()
            }
            
            updateCustomerList(visibleCustomerMarkers)
        } catch (e: Exception) {
        }
    }

    private fun toggleStatisticsPanel(scrollView: ScrollView) {
        if (isStatisticsPanelExpanded) {
            statisticsDivider.visibility = View.VISIBLE
            scrollView.visibility = View.VISIBLE
            
            statisticsExpandIcon.setImageResource(R.drawable.ic_expand_less)
            
            val itemHeight = resources.getDimensionPixelSize(R.dimen.min_height_customer_item)
            val dividerHeight = resources.getDimensionPixelSize(R.dimen.divider_height)
            val itemCount = customerMarkers.size
            
            val totalHeight = itemCount * (itemHeight + 8) + (itemCount - 1) * dividerHeight
            
            val maxItems = 8
            val maxHeight = maxItems * (itemHeight + 8) + (maxItems - 1) * dividerHeight
            
            val minHeight = resources.getDimensionPixelSize(R.dimen.min_panel_width) 
            scrollView.layoutParams.height = maxOf(minOf(totalHeight, maxHeight), minHeight)
            scrollView.requestLayout()
            
            scrollView.scrollTo(0, 0)
            
            scrollView.post {
                val contentWidth = measureContentWidth()
                
                val panelStatistics = view?.findViewById<LinearLayout>(R.id.panel_statistics)
                panelStatistics?.let {
                    val params = it.layoutParams as ConstraintLayout.LayoutParams
                    params.width = maxOf(contentWidth, resources.getDimensionPixelSize(R.dimen.min_panel_width))
                    params.horizontalBias = 0f
                    it.layoutParams = params
                }
            }
        } else {
            statisticsDivider.visibility = View.GONE
            scrollView.visibility = View.GONE
            
            statisticsExpandIcon.setImageResource(R.drawable.ic_expand_more)
            
            val panelStatistics = view?.findViewById<LinearLayout>(R.id.panel_statistics)
            panelStatistics?.let {
                val params = it.layoutParams as ConstraintLayout.LayoutParams
                params.width = resources.getDimensionPixelSize(R.dimen.min_panel_width)
                params.horizontalBias = 0f
                it.layoutParams = params
            }
        }
    }

    private fun getEngineerWorkSummary(engineerId: String, callback: (data: EngineerWorkData?) -> Unit) {
        try {
            val workOrderApi = ApiClient.createService(WorkOrderApi::class.java)
            val call = workOrderApi.getEngineerWorkSummary(engineerId)
            
            call.enqueue(object : retrofit2.Callback<ApiResponse<EngineerWorkData>> {
                override fun onResponse(call: retrofit2.Call<ApiResponse<EngineerWorkData>>, response: retrofit2.Response<ApiResponse<EngineerWorkData>>) {
                    
                    if (response.isSuccessful && response.body() != null) {
                        val apiResponse = response.body()
                        
                        if (apiResponse?.data != null) {
                            val data = apiResponse.data
                        }
                        
                        callback(apiResponse?.data)
                    } else {
                        try {
                            val errorBody = response.errorBody()?.string() ?: "无错误内容"
                        } catch (e: Exception) {
                        }
                        callback(null)
                    }
                }
                
                override fun onFailure(call: retrofit2.Call<ApiResponse<EngineerWorkData>>, t: Throwable) {
                    callback(null)
                }
            })
        } catch (e: Exception) {
            callback(null)
        }
    }

    private fun updateEngineerWithWorkSummary(engineer: MapMarker): MapMarker {
        return try {
            val originalId = engineer.id
            val engineerId = engineer.id.replace("engineer_", "")
            
            getEngineerWorkSummary(engineerId) { summaryData ->
                if (summaryData != null) {
                    val wasIdle = isIdle(engineer)
                    
                    val updatedEngineer = engineer.copy(
                        completedToday = summaryData.completedWorkNum,
                        uncompletedOrders = summaryData.receiveNum,
                        ordersCount = summaryData.todayWorkNum,
                        status = if (summaryData.receiveNum > 0) "忙碌中" else "空闲"
                    )
                    markerManager.updateMarkerDataOnly(updatedEngineer)
                    
                    val isNowIdle = isIdle(updatedEngineer)
                    
                    if (wasIdle != isNowIdle) {
                    }
                    
                    val index = engineerMarkers.indexOfFirst { it.id == originalId }
                    if (index != -1) {
                        engineerMarkers[index] = updatedEngineer
                    }
                    
                } else {
                    markerManager.updateMarkerDataOnly(engineer)
                }
            }
            
            engineer
        } catch (e: Exception) {
            
            markerManager.updateMarkerDataOnly(engineer)
            
            engineer
        }
    }

    private fun expandOrCollapseMarker(markerId: String, shouldMoveCamera: Boolean = true) {
        
        try {
            val mapMarker = markerManager.findMapMarkerById(markerId)
            if (mapMarker == null) {
                return
            }
            
            if (markerId == expandedMarkerId) {
                expandedMarkerId = null
                markerManager.toggleMarkerExpanded(markerId)
            } else {
                expandedMarkerId?.let { prevId ->
                    markerManager.toggleMarkerExpanded(prevId)
                }
                
                expandedMarkerId = markerId
                markerManager.toggleMarkerExpanded(markerId)
                
                if (shouldMoveCamera) {
                    val targetPosition = LatLng(mapMarker.latitude, mapMarker.longitude)
                    tencentMap.animateCamera(
                        CameraUpdateFactory.newLatLngZoom(targetPosition, 16f)
                    )
                }
            }
        } catch (e: Exception) {
        }
    }

    private fun refreshEngineerMarkerData() {
        try {
            if (!isAdded || context == null || isFragmentDestroyed) {
                return
            }
            
            val originalEngineerStates = engineerMarkers.associate { 
                it.id to (isIdle(it) to it.ordersCount)
            }
            
            engineerMarkers.forEach { engineer ->
                updateEngineerWithWorkSummary(engineer)
            }
            
            view?.postDelayed({
                if (!isAdded || context == null || isFragmentDestroyed) {
                    return@postDelayed
                }
                
                updateEngineersList(engineerMarkers)
                
                val idleEngineerCount = engineerMarkers.count { isIdle(it) }
                checkboxEngineers.text = "工程师 (${engineerMarkers.size}个)"
                checkboxIdleEngineers.text = "空闲工程师 (${idleEngineerCount}个)"
                
                textEngineersHeader.text = "工程师：${engineerMarkers.size}个"
                
                val stateChanged = engineerMarkers.any { engineer ->
                    val originalState = originalEngineerStates[engineer.id]
                    val currentIsIdle = isIdle(engineer)
                    val currentOrdersCount = engineer.ordersCount
                    
                    originalState?.first != currentIsIdle || originalState?.second != currentOrdersCount
                }
                
                if (stateChanged) {
                    updateMarkers()
                }
            }, 1000)
        } catch (e: Exception) {
        }
    }

    private fun toggleEngineersPanel(scrollView: ScrollView) {
        if (!isAdded || context == null || isFragmentDestroyed) {
            return
        }
        
        try {
            if (isEngineersPanelExpanded) {
                engineersDivider.visibility = View.VISIBLE
                scrollView.visibility = View.VISIBLE
                
                engineersExpandIcon.setImageResource(R.drawable.ic_expand_less)
                
                refreshEngineerMarkerData()
                
                val itemHeight = resources.getDimensionPixelSize(R.dimen.min_height_customer_item)
                val dividerHeight = resources.getDimensionPixelSize(R.dimen.divider_height)
                val itemCount = engineerMarkers.size
                
                val totalHeight = itemCount * (itemHeight + 8) + (itemCount - 1) * dividerHeight
                
                val maxItems = 6
                val maxHeight = maxItems * (itemHeight + 8) + (maxItems - 1) * dividerHeight
                
                scrollView.layoutParams.height = minOf(totalHeight, maxHeight)
                scrollView.requestLayout()
                
                scrollView.scrollTo(0, 0)
                
                scrollView.post {
                    if (!isAdded || context == null || isFragmentDestroyed) {
                        return@post
                    }
                    
                    val contentWidth = measureEngineerContentWidth()
                    
                    val panelEngineers = view?.findViewById<LinearLayout>(R.id.panel_engineers)
                    panelEngineers?.let {
                        val params = it.layoutParams as ConstraintLayout.LayoutParams
                        params.width = maxOf(contentWidth, resources.getDimensionPixelSize(R.dimen.min_panel_width))
                        params.horizontalBias = 0f
                        it.layoutParams = params
                    }
                }
            } else {
                engineersDivider.visibility = View.GONE
                scrollView.visibility = View.GONE
                
                engineersExpandIcon.setImageResource(R.drawable.ic_expand_more)
                
                val panelEngineers = view?.findViewById<LinearLayout>(R.id.panel_engineers)
                panelEngineers?.let {
                    val params = it.layoutParams as ConstraintLayout.LayoutParams
                    params.width = resources.getDimensionPixelSize(R.dimen.min_panel_width)
                    params.horizontalBias = 0f
                    it.layoutParams = params
                }
            }
        } catch (e: Exception) {
        }
    }
    
    private fun measureEngineerContentWidth(): Int {
        if (!isAdded || context == null || isFragmentDestroyed) {
            return resources.getDimensionPixelSize(R.dimen.min_panel_width)
        }
        
        try {
            var maxWidth = 0
            
            for (i in 0 until panelEngineersContent.childCount) {
                val child = panelEngineersContent.getChildAt(i)
                
                if (child is TextView || child is LinearLayout) {
                    child.measure(
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                    )
                    val width = child.measuredWidth
                    if (width > maxWidth) {
                        maxWidth = width
                    }
                }
            }
            
            val padding = resources.getDimensionPixelSize(R.dimen.default_padding) * 2
            return maxWidth + padding
        } catch (e: Exception) {
            return resources.getDimensionPixelSize(R.dimen.min_panel_width)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isFragmentDestroyed = true
        isFragmentActive = false
    }

    private fun setProgressLoadingVisibility(visibility: Int) {
        if (::progressLoading.isInitialized && !isFragmentDestroyed && context != null && isAdded) {
            progressLoading.visibility = visibility
        }
    }
    
    private fun prepareMapResourceRelease() {
        if (isMapResourceReleasing || !::mapView.isInitialized || !::tencentMap.isInitialized) {
            return
        }
        
        isMapResourceReleasing = true
        
        try {
            Thread {
                try {
                    Handler(Looper.getMainLooper()).post {
                        try {
                            if (::markerManager.isInitialized) {
                                markerManager.removeAllMarkers()
                            }
                        } catch (e: Exception) {
                        }
                    }
                    
                    Thread.sleep(50)
                    
                    Handler(Looper.getMainLooper()).post {
                        try {
                            if (::tencentMap.isInitialized) {
                                tencentMap.moveCamera(CameraUpdateFactory.zoomTo(3f))
                                tencentMap.clear()
                            }
                        } catch (e: Exception) {
                        }
                    }
                } catch (e: Exception) {
                }
            }.start()
        } catch (e: Exception) {
            isMapResourceReleasing = false
        }
    }
    
    private fun releaseMapResources() {
        if (!::mapView.isInitialized) {
            return
        }
        
        try {
            if (::tencentMap.isInitialized) {
                tencentMap.isMyLocationEnabled = false
            }
            
            if (::markerManager.isInitialized) {
                markerManager.removeAllMarkers()
            }
            
            if (::tencentMap.isInitialized) {
                tencentMap.setOnMapClickListener(null)
                tencentMap.setOnMarkerClickListener(null)
                tencentMap.setOnCameraChangeListener(null)
                tencentMap.setOnMyLocationChangeListener(null)
            }
            
            try {
                val parent = mapView.parent as? ViewGroup
                if (parent != null) {
                    parent.removeView(mapView)
                }
            } catch (e: Exception) {
            }
            
            try {
                mapView.onDestroy()
            } catch (e: Exception) {
            }
            
            if (::markerManager.isInitialized) {
                markerManager.orderDetailClickListener = null
            }
            
        } catch (e: Exception) {
        }
    }
    
    private fun fallbackToDefaultPosition() {
        try {
            
            progressLoading.visibility = View.GONE
            
            Toast.makeText(
                context,
                "定位失败，显示默认位置",
                Toast.LENGTH_SHORT
            ).show()
            
            moveToLocation(initialPosition)
        } catch (e: Exception) {
        }
    }

    private fun afterTencentMapReady(rootView: View) {
    }
}
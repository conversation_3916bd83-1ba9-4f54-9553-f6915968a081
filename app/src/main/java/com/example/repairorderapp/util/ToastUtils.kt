package com.example.repairorderapp.util

import android.content.Context
import android.widget.Toast

/**
 * Toast工具类
 */
object ToastUtils {
    
    private var lastToast: Toast? = null
    
    /**
     * 显示短时间Toast
     */
    fun showToast(context: Context, message: String) {
        showToast(context, message, Toast.LENGTH_SHORT)
    }
    
    /**
     * 显示长时间Toast
     */
    fun showLongToast(context: Context, message: String) {
        showToast(context, message, Toast.LENGTH_LONG)
    }
    
    /**
     * 显示Toast（避免重复显示）
     */
    private fun showToast(context: Context, message: String, duration: Int) {
        // 取消之前的Toast
        lastToast?.cancel()
        
        // 创建新的Toast
        lastToast = Toast.makeText(context.applicationContext, message, duration)
        lastToast?.show()
    }
} 
package com.example.repairorderapp.util

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.location.LocationManager
import android.os.Build
import android.os.PowerManager
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import com.example.repairorderapp.R

/**
 * 通知工具类
 * 
 * 提供统一的通知创建和管理功能，用于优化位置服务通知系统。
 * 支持智能状态缓存、条件更新机制，减少不必要的通知更新操作。
 * 
 * 主要功能：
 * - 统一的通知内容构建
 * - 版本兼容的通知创建
 * - 智能状态缓存和比较
 * - 通知渠道管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
object NotificationHelper {
    private const val TAG = "NotificationHelper"
    
    // 通知相关常量
    const val CHANNEL_ID = "location_service_channel"
    const val NOTIFICATION_ID = 12345
    const val KEEP_ALIVE_NOTIFICATION_ID = 12346
    
    // 默认通知配置
    private const val DEFAULT_CHANNEL_NAME = "位置更新服务"
    private const val DEFAULT_CHANNEL_DESCRIPTION = "用于后台更新工程师位置"
    private const val DEFAULT_NOTIFICATION_TITLE = "位置服务"
    
    // 缓存的通知状态
    private var cachedNotificationState: NotificationState? = null
    
    // 上次通知更新时间，用于防止过于频繁的更新
    private var lastUpdateTime: Long = 0L
    private const val MIN_UPDATE_INTERVAL = 2000L // 最小更新间隔：2秒
    
    // 统计信息
    private var totalUpdateRequests = 0
    private var actualUpdates = 0
    private var skipCount = 0
    
    /**
     * 通知状态数据类
     * 
     * 用于存储当前通知的状态信息，支持状态比较和缓存。
     * 
     * @property isLocationEnabled 位置服务是否开启
     * @property isPowerSaveMode 是否处于省电模式
     * @property isScreenOn 屏幕是否亮起
     */
    data class NotificationState(
        val isLocationEnabled: Boolean,
        val isPowerSaveMode: Boolean,
        val isScreenOn: Boolean
    ) {
        /**
         * 判断状态是否发生变化
         * 
         * @param other 要比较的状态
         * @return 如果状态有变化返回true，否则返回false
         */
        fun hasChanged(other: NotificationState?): Boolean {
            return other == null || this != other
        }
        
        /**
         * 获取状态描述字符串，便于日志记录
         */
        fun getDescription(): String {
            return "位置:${if (isLocationEnabled) "开启" else "关闭"}, " +
                   "省电:${if (isPowerSaveMode) "开启" else "关闭"}, " +
                   "屏幕:${if (isScreenOn) "亮起" else "熄灭"}"
        }
    }
    
    /**
     * 通知内容数据类
     * 
     * 用于存储通知显示的文本内容。
     * 
     * @property statusText 状态文本（如"正常运行中"、"省电模式运行中"等）
     * @property intervalText 间隔文本（如"每5分钟更新"、"每8分钟更新"等）
     */
    data class NotificationContent(
        val statusText: String,
        val intervalText: String
    ) {
        /**
         * 获取完整的通知内容文本
         * 
         * @return 格式化的通知内容："状态文本 • 间隔文本"
         */
        fun getFullText(): String {
            return "$statusText • $intervalText"
        }
    }
    
    /**
     * 通知配置数据类
     * 
     * 用于自定义通知的各种配置参数。
     * 
     * @property channelId 通知渠道ID
     * @property title 通知标题
     * @property iconRes 通知图标资源ID
     * @property priority 通知优先级
     * @property visibility 通知可见性
     * @property ongoing 是否为持续通知
     * @property showWhen 是否显示时间
     */
    data class NotificationConfig(
        val channelId: String = CHANNEL_ID,
        val title: String = DEFAULT_NOTIFICATION_TITLE,
        val iconRes: Int = R.drawable.ic_location,
        val priority: Int = NotificationCompat.PRIORITY_LOW,
        val visibility: Int = NotificationCompat.VISIBILITY_PUBLIC,
        val ongoing: Boolean = true,
        val showWhen: Boolean = true
    )
    
    /**
     * 智能更新结果
     * 
     * @property notification 创建的通知对象（如果有更新）
     * @property wasUpdated 是否实际进行了更新
     * @property reason 更新或跳过的原因
     */
    data class UpdateResult(
        val notification: Notification?,
        val wasUpdated: Boolean,
        val reason: String
    )
    
    /**
     * 创建通知渠道（Android O+ 需要）
     * 
     * @param context 上下文
     * @param channelId 渠道ID
     * @param channelName 渠道名称
     * @param channelDescription 渠道描述
     * @param importance 渠道重要性
     */
    fun createNotificationChannel(
        context: Context,
        channelId: String = CHANNEL_ID,
        channelName: String = DEFAULT_CHANNEL_NAME,
        channelDescription: String = DEFAULT_CHANNEL_DESCRIPTION,
        importance: Int = NotificationManager.IMPORTANCE_LOW
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val channel = NotificationChannel(channelId, channelName, importance).apply {
                    description = channelDescription
                    setShowBadge(false)
                }
                
                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.createNotificationChannel(channel)
                
                Log.d(TAG, "通知渠道创建成功: channelId=$channelId, name=$channelName")
            } catch (e: Exception) {
                Log.e(TAG, "创建通知渠道失败: ${e.message}", e)
            }
        } else {
            Log.d(TAG, "Android O 以下版本，无需创建通知渠道")
        }
    }
    
    /**
     * 创建前台服务通知
     * 
     * 使用 NotificationCompat 统一 API，自动处理版本兼容性问题。
     * 
     * @param context 上下文
     * @param config 通知配置（可选）
     * @return Notification 通知对象
     */
    fun createForegroundNotification(
        context: Context,
        config: NotificationConfig = NotificationConfig()
    ): Notification? {
        return try {
            // 确保通知渠道存在
            createNotificationChannel(context, config.channelId)
            
            // 获取通知内容
            val content = buildNotificationContent(context)
            
            // 创建通知
            NotificationCompat.Builder(context, config.channelId)
                .setContentTitle(config.title)
                .setContentText(content.getFullText())
                .setSmallIcon(config.iconRes)
                .setOngoing(config.ongoing)
                .setShowWhen(config.showWhen)
                .setWhen(System.currentTimeMillis())
                .setPriority(config.priority)
                .setVisibility(config.visibility)
                .apply {
                    // Android O+ 特有的设置
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        setCategory(Notification.CATEGORY_SERVICE)
                    }
                }
                .build().also {
                    Log.d(TAG, "前台服务通知创建成功: title=${config.title}, content=${content.getFullText()}")
                }
        } catch (e: Exception) {
            Log.e(TAG, "创建前台服务通知失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 创建前台服务通知（使用当前状态）
     * 
     * @param context 上下文
     * @param state 设备状态
     * @param config 通知配置（可选）
     * @return Notification 通知对象
     */
    fun createForegroundNotification(
        context: Context,
        state: NotificationState,
        config: NotificationConfig = NotificationConfig()
    ): Notification? {
        return try {
            // 确保通知渠道存在
            createNotificationChannel(context, config.channelId)
            
            // 获取通知内容
            val content = buildNotificationContent(state)
            
            // 创建通知
            NotificationCompat.Builder(context, config.channelId)
                .setContentTitle(config.title)
                .setContentText(content.getFullText())
                .setSmallIcon(config.iconRes)
                .setOngoing(config.ongoing)
                .setShowWhen(config.showWhen)
                .setWhen(System.currentTimeMillis())
                .setPriority(config.priority)
                .setVisibility(config.visibility)
                .apply {
                    // Android O+ 特有的设置
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        setCategory(Notification.CATEGORY_SERVICE)
                    }
                }
                .build().also {
                    Log.d(TAG, "前台服务通知创建成功（使用指定状态）: title=${config.title}, content=${content.getFullText()}")
                }
        } catch (e: Exception) {
            Log.e(TAG, "创建前台服务通知失败（使用指定状态）: ${e.message}", e)
            null
        }
    }
    
    /**
     * 智能更新前台服务通知
     * 
     * 这是核心的智能更新方法，只在真正需要时才更新通知。
     * 
     * @param context 上下文
     * @param config 通知配置（可选）
     * @param forceUpdate 是否强制更新，忽略状态比较（默认为false）
     * @return UpdateResult 更新结果
     */
    fun updateNotificationIfNeeded(
        context: Context,
        config: NotificationConfig = NotificationConfig(),
        forceUpdate: Boolean = false
    ): UpdateResult {
        totalUpdateRequests++
        
        val currentTime = System.currentTimeMillis()
        val currentState = checkDeviceState(context)
        
        // 检查是否需要更新
        val shouldUpdate = when {
            forceUpdate -> {
                Log.d(TAG, "强制更新通知: 忽略状态比较")
                true
            }
            currentState.hasChanged(cachedNotificationState) -> {
                Log.d(TAG, "状态变化，需要更新通知: ${cachedNotificationState?.getDescription()} -> ${currentState.getDescription()}")
                true
            }
            currentTime - lastUpdateTime < MIN_UPDATE_INTERVAL -> {
                Log.d(TAG, "更新请求过于频繁，跳过更新（距离上次更新仅 ${currentTime - lastUpdateTime}ms）")
                false
            }
            else -> {
                Log.d(TAG, "状态无变化，跳过更新: ${currentState.getDescription()}")
                false
            }
        }
        
        return if (shouldUpdate) {
            try {
                val notification = createForegroundNotification(context, currentState, config)
                if (notification != null) {
                    // 更新缓存状态
                    cachedNotificationState = currentState
                    lastUpdateTime = currentTime
                    actualUpdates++
                    
                    Log.i(TAG, "通知更新成功 - 总请求: $totalUpdateRequests, 实际更新: $actualUpdates, 跳过: $skipCount")
                    UpdateResult(notification, true, "状态变化或强制更新")
                } else {
                    skipCount++
                    UpdateResult(null, false, "通知创建失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "智能更新通知失败: ${e.message}", e)
                skipCount++
                UpdateResult(null, false, "更新异常: ${e.message}")
            }
        } else {
            skipCount++
            UpdateResult(null, false, "状态无变化或更新过于频繁")
        }
    }
    
    /**
     * 更新前台服务通知
     * 
     * 检查状态变化，仅在必要时更新通知。
     * 
     * @param context 上下文
     * @param config 通知配置（可选）
     * @return 如果更新了通知返回新的 Notification 对象，否则返回 null
     */
    fun updateForegroundNotificationIfNeeded(
        context: Context,
        config: NotificationConfig = NotificationConfig()
    ): Notification? {
        return updateNotificationIfNeeded(context, config).notification
    }
    
    /**
     * 强制更新通知
     * 
     * 忽略状态比较和时间间隔限制，强制创建并返回新的通知。
     * 适用于服务启动、关键状态变化等场景。
     * 
     * @param context 上下文
     * @param config 通知配置（可选）
     * @return Notification 通知对象
     */
    fun forceUpdateNotification(
        context: Context,
        config: NotificationConfig = NotificationConfig()
    ): Notification? {
        Log.d(TAG, "强制更新通知")
        return updateNotificationIfNeeded(context, config, forceUpdate = true).notification
    }
    
    /**
     * 检查设备状态
     * 
     * @param context 上下文
     * @return NotificationState 当前设备状态
     */
    fun checkDeviceState(context: Context): NotificationState {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        
        return NotificationState(
            isLocationEnabled = isLocationServiceEnabled(context),
            isPowerSaveMode = powerManager.isPowerSaveMode,
            isScreenOn = powerManager.isInteractive
        )
    }
    
    /**
     * 构建通知内容
     * 
     * 根据设备状态生成相应的通知文本内容，与原有逻辑保持完全一致。
     * 
     * @param state 设备状态
     * @return NotificationContent 通知内容
     */
    fun buildNotificationContent(state: NotificationState): NotificationContent {
        // 构建状态文本 - 与 LocationUpdateService 中的逻辑完全一致
        val statusText = when {
            !state.isLocationEnabled -> "位置服务未开启"
            state.isPowerSaveMode -> "省电模式运行中"
            !state.isScreenOn -> "后台运行中"
            else -> "正常运行中"
        }
        
        // 构建间隔文本 - 与 LocationUpdateService 中的逻辑完全一致
        val intervalText = when {
            !state.isLocationEnabled -> "等待开启位置服务"
            state.isPowerSaveMode -> "每8分钟更新"
            !state.isScreenOn -> "每10分钟更新"
            else -> "每5分钟更新"
        }
        
        return NotificationContent(statusText, intervalText)
    }
    
    /**
     * 构建通知内容（使用 Context 直接检查状态）
     * 
     * @param context 上下文
     * @return NotificationContent 通知内容
     */
    fun buildNotificationContent(context: Context): NotificationContent {
        val state = checkDeviceState(context)
        return buildNotificationContent(state)
    }
    
    /**
     * 检查位置服务是否启用
     * 
     * 与 LocationUpdateService 中的 isLocationServiceEnabled() 方法逻辑保持一致
     * 
     * @param context 上下文
     * @return true 表示位置服务已启用，false 表示未启用
     */
    private fun isLocationServiceEnabled(context: Context): Boolean {
        return try {
            val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
            locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || 
            locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        } catch (e: Exception) {
            Log.e(TAG, "检查位置服务状态异常: ${e.message}")
            false
        }
    }
    
    /**
     * 检查通知状态是否需要更新
     * 
     * 通过状态比较来判断是否需要更新通知，实现智能更新机制。
     * 
     * @param context 上下文
     * @return true 表示需要更新，false 表示不需要更新
     */
    fun shouldUpdateNotification(context: Context): Boolean {
        val currentState = checkDeviceState(context)
        val shouldUpdate = currentState.hasChanged(cachedNotificationState)
        
        if (shouldUpdate) {
            cachedNotificationState = currentState
            Log.d(TAG, "通知状态发生变化，需要更新: $currentState")
        } else {
            Log.d(TAG, "通知状态无变化，跳过更新")
        }
        
        return shouldUpdate
    }
    
    /**
     * 强制更新缓存状态
     * 
     * 在某些情况下（如服务启动时）需要强制更新缓存状态
     * 
     * @param context 上下文
     */
    fun forceUpdateCachedState(context: Context) {
        cachedNotificationState = checkDeviceState(context)
        Log.d(TAG, "强制更新缓存状态: $cachedNotificationState")
    }
    
    /**
     * 重置状态缓存
     * 
     * 清空所有缓存状态，下次更新时将强制刷新。
     * 适用于服务重启、重大配置变更等场景。
     */
    fun resetCache() {
        cachedNotificationState = null
        lastUpdateTime = 0L
        Log.d(TAG, "通知状态缓存已重置")
    }
    
    /**
     * 获取性能统计信息
     * 
     * @return 包含更新统计的字符串
     */
    fun getPerformanceStats(): String {
        val efficiency = if (totalUpdateRequests > 0) {
            ((totalUpdateRequests - actualUpdates) * 100 / totalUpdateRequests)
        } else {
            0
        }
        
        return "通知更新统计 - 总请求: $totalUpdateRequests, 实际更新: $actualUpdates, " +
               "跳过: $skipCount, 优化率: ${efficiency}%"
    }
    
    /**
     * 重置性能统计
     */
    fun resetStats() {
        totalUpdateRequests = 0
        actualUpdates = 0
        skipCount = 0
        Log.d(TAG, "性能统计已重置")
    }
} 
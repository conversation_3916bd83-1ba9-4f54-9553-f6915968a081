package com.example.repairorderapp.util

import android.content.Context
import android.util.Log

/**
 * 用户信息帮助类
 * 统一获取用户信息，复用LoginActivity中的存储方式
 */
object UserInfoHelper {
    private const val TAG = "UserInfoHelper"
    
    /**
     * 用户信息数据类
     */
    data class UserInfo(
        val userId: String,
        val userCode: String,
        val userName: String
    ) {
        /**
         * 检查用户信息是否有效（至少有用户ID）
         */
        fun isValid(): Boolean = userId.isNotEmpty()
        
        /**
         * 检查是否为空用户信息
         */
        fun isEmpty(): Boolean = userId.isEmpty() && userCode.isEmpty() && userName.isEmpty()
    }
    
    /**
     * 获取用户信息（复用LoginActivity中的存储方式）
     * 从token_pref SharedPreferences中统一获取用户信息
     * 
     * @param context 上下文
     * @return 用户信息，如果获取失败返回空的用户信息
     */
    fun getUserInfo(context: Context): UserInfo {
        return try {
            val tokenPrefs = context.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            val userId = tokenPrefs.getString("userId", "") ?: ""
            val userCode = tokenPrefs.getString("userCode", "") ?: ""
            val userName = tokenPrefs.getString("userName", "") ?: ""
            
            Log.d(TAG, "获取用户信息: userId=$userId, userCode=$userCode, userName=$userName")
            
            UserInfo(userId, userCode, userName)
        } catch (e: Exception) {
            Log.e(TAG, "获取用户信息失败", e)
            UserInfo("", "", "")
        }
    }
    
    /**
     * 获取用户ID
     */
    fun getUserId(context: Context): String {
        return getUserInfo(context).userId
    }
    
    /**
     * 获取用户编码
     */
    fun getUserCode(context: Context): String {
        return getUserInfo(context).userCode
    }
    
    /**
     * 获取用户名
     */
    fun getUserName(context: Context): String {
        return getUserInfo(context).userName
    }
    
    /**
     * 检查用户是否已登录
     */
    fun isUserLoggedIn(context: Context): Boolean {
        return getUserInfo(context).isValid()
    }
}

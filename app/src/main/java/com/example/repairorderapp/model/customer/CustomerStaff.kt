package com.example.repairorderapp.model.customer

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "customer_staffs")
data class CustomerStaff(
    @PrimaryKey val id: String,
    val customerId: String,              // 客户ID
    val name: String,                    // 员工姓名
    val tel: String,                     // 联系电话
    val role: CustomerOption? = null,    // 角色
    val status: Boolean = true,          // 状态
    val deleted: Int = 0,                // 是否删除(0-正常，1-删除)
    val createdAt: String? = null,       // 创建时间
    val updatedAt: String? = null,       // 更新时间
    val isCollect: Boolean = false,      // 是否收藏
    val vxGroupName: CustomerOption? = null, // 微信群名称
    
    // 以下是旧字段，为了兼容性保留
    val position: String? = null,        // 职位
    val phone: String? = null,           // 联系电话(旧)
    val email: String? = null,           // 电子邮箱
    val isMainContact: Boolean = false,  // 是否主要联系人
    val remark: String? = null           // 备注
) : Parcelable 
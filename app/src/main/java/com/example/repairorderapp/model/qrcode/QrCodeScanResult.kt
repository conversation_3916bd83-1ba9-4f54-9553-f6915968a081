package com.example.repairorderapp.model.qrcode

import com.google.gson.annotations.SerializedName

/**
 * 二维码扫描结果数据模型
 */
data class QrCodeScanResult(
    @SerializedName("first")
    val first: Boolean,     // 是否为首次绑定
    
    @SerializedName("second")
    val second: String?     // 错误信息或状态描述
)

/**
 * 二维码绑定结果数据模型
 */
data class QrCodeBindResult(
    @SerializedName("first")
    val first: <PERSON><PERSON><PERSON>,     // 绑定是否成功
    
    @SerializedName("second")
    val second: String?     // 错误信息或状态描述
)

/**
 * 二维码扫描内容数据模型
 */
data class QrCodeContent(
    @SerializedName("type")
    val type: String,       // 二维码类型：install、other等
    
    @SerializedName("data")
    val data: String        // 设备uid或其他数据
)

 
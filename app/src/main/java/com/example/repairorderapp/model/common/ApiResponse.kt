package com.example.repairorderapp.model.common

/**
 * API响应统一格式
 * 用于封装服务器返回的标准数据结构
 */
data class ApiResponse<T>(
    val code: Int,               // 状态码，200表示成功
    val message: String? = null, // 响应消息
    val data: T? = null          // 响应数据
) {
    /**
     * 检查响应是否成功
     */
    fun isSuccess(): Boolean {
        return code == 200
    }
    
    /**
     * 获取错误消息
     */
    fun getErrorMessage(): String {
        return message ?: "未知错误"
    }
} 
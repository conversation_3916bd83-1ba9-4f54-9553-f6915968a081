package com.example.repairorderapp.model.customer

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * 设备维修记录数据模型
 */
data class DeviceMaintenanceRecord(
    @SerializedName("repairReport") val repairReport: RepairReport?,
    @SerializedName("replaceOrder") val replaceOrder: ReplaceOrder?
) : Serializable

/**
 * 维修报告
 */
data class RepairReport(
    @SerializedName("id") val id: String,
    @SerializedName("workOrderId") val workOrderId: String,
    @SerializedName("workOrderCode") val workOrderCode: String,
    @SerializedName("deviceGroupId") val deviceGroupId: String,
    @SerializedName("customerId") val customerId: String,
    @SerializedName("engineerId") val engineerId: String,
    @SerializedName("excType") val excType: Map<String, Any>?,
    @SerializedName("reasonType") val reasonType: Map<String, Any>?,
    @SerializedName("resolveType") val resolveType: Map<String, Any>?,
    @SerializedName("excUnit") val excUnit: Map<String, Any>?,
    @SerializedName("blackWhiteCount") val blackWhiteCount: Int,
    @SerializedName("colorCount") val colorCount: Int,
    @SerializedName("fiveColourCount") val fiveColourCount: Int,
    @SerializedName("printCount") val printCount: Int,
    @SerializedName("colorExclude") val colorExclude: Int,
    @SerializedName("fiveColorExclude") val fiveColorExclude: Int,
    @SerializedName("blackWhiteExclude") val blackWhiteExclude: Int,
    @SerializedName("excDesc") val excDesc: String,
    @SerializedName("excDescPics") val excDescPics: List<ImageInfo>?,
    @SerializedName("resolveDesc") val resolveDesc: String,
    @SerializedName("announcements") val announcements: String?,
    @SerializedName("resolveDescPics") val resolveDescPics: List<ImageInfo>?,
    @SerializedName("finishTime") val finishTime: String,
    @SerializedName("createdAt") val createdAt: String,
    @SerializedName("updatedAt") val updatedAt: String,
    @SerializedName("deleted") val deleted: Int
) : Serializable

/**
 * 更换零件订单
 */
data class ReplaceOrder(
    @SerializedName("id") val id: String,
    @SerializedName("code") val code: String,
    @SerializedName("deviceGroupId") val deviceGroupId: String,
    @SerializedName("customerId") val customerId: String,
    @SerializedName("customerStaffId") val customerStaffId: String?,
    @SerializedName("replaceType") val replaceType: String,
    @SerializedName("workOrderId") val workOrderId: String,
    @SerializedName("workOrderCode") val workOrderCode: String,
    @SerializedName("engineerId") val engineerId: String,
    @SerializedName("replaceDetailList") val replaceDetailList: List<ReplaceDetail>?,
    @SerializedName("createdAt") val createdAt: String,
    @SerializedName("updatedAt") val updatedAt: String,
    @SerializedName("deleted") val deleted: Int
) : Serializable

/**
 * 更换零件详情
 */
data class ReplaceDetail(
    @SerializedName("id") val id: String,
    @SerializedName("replaceOrderId") val replaceOrderId: String,
    @SerializedName("replaceOrderCode") val replaceOrderCode: String,
    @SerializedName("itemStoreId") val itemStoreId: String,
    @SerializedName("itemId") val itemId: String,
    @SerializedName("itemName") val itemName: String,
    @SerializedName("saleSkuId") val saleSkuId: String,
    @SerializedName("saleUnitPrice") val saleUnitPrice: String,
    @SerializedName("skuInfo") val skuInfo: SkuInfo?,
    @SerializedName("num") val num: Int,
    @SerializedName("amount") val amount: String,
    @SerializedName("batchCode") val batchCode: String?,
    @SerializedName("isPm") val isPm: Boolean,
    @SerializedName("partId") val partId: String?,
    @SerializedName("createdAt") val createdAt: String,
    @SerializedName("updatedAt") val updatedAt: String,
    @SerializedName("deleted") val deleted: Int,
    @SerializedName("articleName") val articleName: String?,
    @SerializedName("articleCode") val articleCode: String?,
    @SerializedName("unit") val unit: String?,
    @SerializedName("oem") val oem: String?
) : Serializable

/**
 * SKU信息
 */
data class SkuInfo(
    @SerializedName("saleAttrVals") val saleAttrVals: List<SaleAttrVal>?,
    @SerializedName("picUrl") val picUrl: List<ImageInfo>?,
    @SerializedName("invSkuId") val invSkuId: String?
) : Serializable

/**
 * 销售属性值
 */
data class SaleAttrVal(
    @SerializedName("name") val name: String,
    @SerializedName("val") val value: String
) : Serializable

/**
 * 图片信息
 */
data class ImageInfo(
    @SerializedName("key") val key: String,
    @SerializedName("url") val url: String,
    @SerializedName("name") val name: String? = null
) : Serializable 
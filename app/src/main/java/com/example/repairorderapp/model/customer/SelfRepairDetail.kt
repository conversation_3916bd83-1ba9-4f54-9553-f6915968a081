package com.example.repairorderapp.model.customer

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * 自修详情数据模型
 */
data class SelfRepairDetail(
    @SerializedName("id") val id: String = "",
    @SerializedName("code") val code: String = "",
    @SerializedName("deviceGroupId") val deviceGroupId: String = "",
    @SerializedName("customerId") val customerId: String = "",
    @SerializedName("customerStaffId") val customerStaffId: String = "",
    @SerializedName("blackWhiteCount") val blackWhiteCount: Int = 0,
    @SerializedName("colorCount") val colorCount: Int = 0,
    @SerializedName("fiveColourCount") val fiveColourCount: Int = 0,
    @SerializedName("excDesc") val excDesc: String = "",
    @SerializedName("createdAt") val createdAt: String = "",
    @SerializedName("updatedAt") val updatedAt: String = "",
    @SerializedName("deleted") val deleted: Int = 0,
    @SerializedName("customerSeqId") val customerSeqId: String = "",
    @SerializedName("customerName") val customerName: String = "",
    @SerializedName("subbranch") val subbranch: String = "",
    @SerializedName("brand") val brand: String = "",
    @SerializedName("machine") val machine: String = "",
    @SerializedName("customerDeviceGroup") val customerDeviceGroup: CustomerDeviceGroup? = null,
    @SerializedName("replaceDetailList") val replaceDetailList: List<SelfReplaceDetail>? = null
) : Serializable

/**
 * 客户设备组信息
 */
data class CustomerDeviceGroup(
    @SerializedName("id") val id: String = "",
    @SerializedName("customerId") val customerId: String = "",
    @SerializedName("deviceSeqId") val deviceSeqId: String = "",
    @SerializedName("deviceGroup") val deviceGroup: DictItem? = null,
    @SerializedName("productId") val productId: String = "",
    @SerializedName("deviceStatus") val deviceStatus: DictItem? = null,
    @SerializedName("status") val status: Boolean = false,
    @SerializedName("deviceOn") val deviceOn: DictItem? = null,
    @SerializedName("treatyType") val treatyType: DictItem? = null,
    @SerializedName("fixStatus") val fixStatus: DictItem? = null,
    @SerializedName("machineNum") val machineNum: String = "",
    @SerializedName("deviceGroupImg") val deviceGroupImg: Any? = null,
    @SerializedName("paperType") val paperType: String = "",
    @SerializedName("macNumber") val macNumber: String = "",
    @SerializedName("enableStatistics") val enableStatistics: Boolean = false,
    @SerializedName("blackWhiteCounter") val blackWhiteCounter: Int = 0,
    @SerializedName("colorCounter") val colorCounter: Int = 0,
    @SerializedName("fiveColourCounter") val fiveColourCounter: Int = 0,
    @SerializedName("adjustBlackWhite") val adjustBlackWhite: Int = 0,
    @SerializedName("adjustColor") val adjustColor: Int = 0,
    @SerializedName("deleted") val deleted: Int = 0,
    @SerializedName("productInfo") val productInfo: String = "",
    @SerializedName("regCliState") val regCliState: String = "",
    @SerializedName("installAt") val installAt: String = "",
    @SerializedName("dataShowState") val dataShowState: Int = 0,
    @SerializedName("serType") val serType: DictItem? = null,
    @SerializedName("color") val color: DictItem? = null,
    @SerializedName("deviceAccessories") val deviceAccessories: List<Any>? = null,
    @SerializedName("isContracted") val isContracted: Boolean = false,
    @SerializedName("isAllServe") val isAllServe: Boolean = false
) : Serializable

/**
 * 自修零件详情
 */
data class SelfReplaceDetail(
    @SerializedName("id") val id: String = "",
    @SerializedName("selfRepairReportId") val selfRepairReportId: String = "",
    @SerializedName("itemStoreId") val itemStoreId: String = "",
    @SerializedName("itemId") val itemId: String = "",
    @SerializedName("itemName") val itemName: String = "",
    @SerializedName("saleSkuId") val saleSkuId: String = "",
    @SerializedName("saleUnitPrice") val saleUnitPrice: String = "",
    @SerializedName("amount") val amount: String = "",
    @SerializedName("skuInfo") val skuInfo: SkuInfo? = null,
    @SerializedName("batchCode") val batchCode: String = "",
    @SerializedName("num") val num: Int = 0,
    @SerializedName("isPm") val isPm: Boolean = false,
    @SerializedName("partId") val partId: String = "",
    @SerializedName("createdAt") val createdAt: String = "",
    @SerializedName("updatedAt") val updatedAt: String = "",
        @SerializedName("deleted") val deleted: Int = 0
) : Serializable

 
package com.example.repairorderapp

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.example.repairorderapp.base.BaseActivity
import com.example.repairorderapp.model.MenuItem
import com.example.repairorderapp.service.LocationUpdateService
import com.example.repairorderapp.service.LocationServiceStarter
import com.example.repairorderapp.ui.profile.ProfileFragment
import com.example.repairorderapp.util.SharedPrefsManager
import com.example.repairorderapp.utils.PermissionManager
import com.example.repairorderapp.utils.PermissionUtils
import com.google.android.material.bottomnavigation.BottomNavigationView
import org.json.JSONArray
import org.json.JSONObject

class MainActivity : BaseActivity() {
    
    companion object {
        private const val TAG = "MainActivity"
        
        /**
         * 从MainActivity启动位置服务
         */
        private fun startLocationServiceAfterLogin(context: Context) {
            // 检查是否已登录
            val sharedPrefs = SharedPrefsManager(context)
            if (sharedPrefs.getAuthToken().isNotEmpty()) {
                Log.d(TAG, "用户已登录，启动位置服务")
                startLocationService(context)
            } else {
                Log.d(TAG, "用户未登录，不启动位置服务")
            }
        }
        
        /**
         * 启动位置更新服务
         * 增强权限检查和状态监控
         */
        private fun startLocationService(context: Context) {
            try {
                // 使用LocationServiceStarter统一启动逻辑
                val serviceStarter = LocationServiceStarter(context)

                // 检查服务状态（仅在调试时显示详细信息）
                if (BuildConfig.DEBUG) {
                    val serviceStatus = serviceStarter.getDetailedServiceStatus()
                    Log.d(TAG, "位置服务状态:\n$serviceStatus")
                }

                // 直接启动服务（LocationServiceStarter会处理所有检查和修复）
                serviceStarter.startLocationService()

            } catch (e: Exception) {
                Log.e(TAG, "启动位置服务失败: ${e.message}", e)
            }
        }
    }
    
    private lateinit var navController: NavController
    private lateinit var permissionManager: PermissionManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 使用新的布局文件
        setContentView(R.layout.activity_main_navigation)
        
        // 初始化权限管理器
        permissionManager = PermissionManager.getInstance(this)
        
        // 输出原始权限数据到日志（调试用）
        permissionManager.logRawPermissionData()
        
        // 如果发现权限数据有问题，立即重置权限数据（调试用）
        if (isPermissionDataEmpty()) {
            resetPermissionData()
        }
        
        // 设置导航控制器
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        navController = navHostFragment.navController
        
        // 设置默认导航选项，改善Fragment切换体验
        val navGraph = navController.navInflater.inflate(R.navigation.nav_graph)
        navGraph.setStartDestination(R.id.profileFragment)
        navController.graph = navGraph
        
        // 设置底部导航
        setupBottomNavigation()
        
        // 启动位置服务（如果用户已登录）
        startLocationServiceAfterLogin(this)
        
        // 检查并提示电池优化设置（延迟执行，避免影响启动）
        window.decorView.postDelayed({
            checkBatteryOptimization()
        }, 2000)

        // 设置返回键处理（适用于Android 13及以上版本）
        setupBackPressedCallback()
    }
    
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        // 先处理全局触摸事件（隐藏键盘）
        ev?.let { event ->
            RepairOrderApp.handleActivityTouch(this, event)
        }

        // 继续正常的事件分发
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 设置返回键处理回调
     * 适用于Android 13及以上版本
     */
    private fun setupBackPressedCallback() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                try {
                    // 获取当前Fragment
                    val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
                    val currentFragment = navHostFragment.childFragmentManager.fragments.firstOrNull()
                    
                    // 如果当前是首页，则将应用最小化到后台
                    if (currentFragment is ProfileFragment) {
                        Log.d(TAG, "在首页按返回键，将应用最小化到后台")
                        // 将应用移到后台而不是退出
                        moveTaskToBack(true)
                    } else {
                        // 其他页面正常返回
                        isEnabled = false
                        onBackPressedDispatcher.onBackPressed()
                        isEnabled = true
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理返回键时出错: ${e.message}", e)
                    // 出错时也尝试将应用最小化，避免退出
                    moveTaskToBack(true)
                }
            }
        })
    }
    
    /**
     * 设置底部导航
     */
    private fun setupBottomNavigation() {
        try {
            val bottomNav = findViewById<BottomNavigationView>(R.id.bottom_navigation)
            bottomNav?.setupWithNavController(navController)
        } catch (e: Exception) {
            Log.e(TAG, "设置底部导航失败: ${e.message}", e)
        }
    }
    
    /**
     * 检查权限数据是否为空
     */
    private fun isPermissionDataEmpty(): Boolean {
        val permissions = permissionManager.getPermissions()
        return permissions.isEmpty()
    }
    
    /**
     * 重置权限数据（仅用于调试）
     */
    private fun resetPermissionData() {
        try {
            Log.d(TAG, "重置权限数据")
            
            // 创建测试权限数据，格式与服务器返回一致
            val testPermissions = JSONArray()
            
            // 工单管理权限
            val workPoolPerm = JSONObject()
            workPoolPerm.put("label", "工单管理")
            workPoolPerm.put("value", "workPool")
            workPoolPerm.put("type", JSONObject().put("value", "menu").put("label", "菜单"))
            testPermissions.put(workPoolPerm)
            
            // 地图权限
            val mapPerm = JSONObject()
            mapPerm.put("label", "地图")
            mapPerm.put("value", "map")
            mapPerm.put("type", JSONObject().put("value", "menu").put("label", "菜单"))
            testPermissions.put(mapPerm)
            
            // 待接工单权限
            val pendingOrderPerm = JSONObject()
            pendingOrderPerm.put("label", "待接工单")
            pendingOrderPerm.put("value", "pendingOrder")
            pendingOrderPerm.put("type", JSONObject().put("value", "menu").put("label", "菜单"))
            testPermissions.put(pendingOrderPerm)
            
            // 我的工单权限
            val myWorkOrderPerm = JSONObject()
            myWorkOrderPerm.put("label", "我的工单")
            myWorkOrderPerm.put("value", "myWorkOrder")
            myWorkOrderPerm.put("type", JSONObject().put("value", "menu").put("label", "菜单"))
            testPermissions.put(myWorkOrderPerm)
            
            // 保存测试权限数据
            val permPrefs = getSharedPreferences("perm_pref", Context.MODE_PRIVATE)
            permPrefs.edit().putString("powerMenu", testPermissions.toString()).apply()
            
            Log.d(TAG, "已重置权限数据: $testPermissions")
            Toast.makeText(this, "已重置权限数据", Toast.LENGTH_SHORT).show()
            
        } catch (e: Exception) {
            Log.e(TAG, "重置权限数据失败", e)
        }
    }
    
    /**
     * 重写返回按钮处理
     * 在首页时将应用最小化到后台，而不是退出应用或返回登录页
     * 注意：此方法在Android 13及以上版本已弃用，使用OnBackPressedDispatcher代替
     */
    @Suppress("DEPRECATION")
    override fun onBackPressed() {
        try {
            // 获取当前Fragment
            val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
            val currentFragment = navHostFragment.childFragmentManager.fragments.firstOrNull()
            
            // 如果当前是首页，则将应用最小化到后台
            if (currentFragment is ProfileFragment) {
                Log.d(TAG, "在首页按返回键，将应用最小化到后台")
                // 将应用移到后台而不是退出
                moveTaskToBack(true)
            } else {
                // 其他页面正常返回
                super.onBackPressed()
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理返回键时出错: ${e.message}", e)
            // 出错时也尝试将应用最小化，避免退出
            moveTaskToBack(true)
        }
    }
    
    /**
     * 检查电池优化设置
     */
    private fun checkBatteryOptimization() {
        val sharedPrefs = getSharedPreferences("battery_optimization_pref", Context.MODE_PRIVATE)
        val hasShownBatteryTip = sharedPrefs.getBoolean("has_shown_battery_tip", false)
        
        // 只在第一次或未优化时显示
        if (!hasShownBatteryTip || !com.example.repairorderapp.util.BatteryOptimizationHelper.isIgnoringBatteryOptimizations(this)) {
            com.example.repairorderapp.util.BatteryOptimizationHelper.showBatteryOptimizationDialog(this)
            
            // 记录已显示过
            sharedPrefs.edit()
                .putBoolean("has_shown_battery_tip", true)
                .apply()
            
//            // 延迟显示厂商特定提示
//            window.decorView.postDelayed({
//                com.example.repairorderapp.util.BatteryOptimizationHelper.showVendorSpecificTips(this)
//            }, 3000)
            // 移除厂商特定提示，避免两个弹窗同时出现
            // 可以在用户完成电池优化设置后，或在设置页面中单独显示厂商提示
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 应用关闭时不要停止位置服务，让它继续在后台运行
        // 位置服务会自己判断是否需要停止
    }
}
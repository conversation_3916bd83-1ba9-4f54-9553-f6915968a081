package com.example.repairorderapp.service

import android.content.Context
import android.util.Log
import androidx.work.*
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.network.ApiClient
import com.example.repairorderapp.data.local.AppDatabase
import com.example.repairorderapp.util.SharedPrefsManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * 后台工作器，负责从本地数据库读取位置数据并上传到服务器。
 * 具有网络约束和失败重试策略。
 */
class UploadWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    private val locationDao = AppDatabase.getDatabase(context).locationDao()
    private val sharedPrefsManager = SharedPrefsManager(context)

    companion object {
        private const val TAG = "UploadWorker"
        const val WORK_NAME = "location_upload_work"
        private const val BATCH_SIZE = 1 // 每次只上传1条数据

        /**
         * 调度周期性的上传任务。
         * 任务将在网络连接时运行，并且每小时最多运行一次。
         */
        fun schedulePeriodicWork(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()

            val uploadWorkRequest = PeriodicWorkRequestBuilder<UploadWorker>(
                1, TimeUnit.HOURS) // 每小时运行一次
                .setConstraints(constraints)
                .setBackoffCriteria( // 设置指数退避策略
                    BackoffPolicy.LINEAR,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .addTag(WORK_NAME)
                .build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                uploadWorkRequest
            )
            Log.d(TAG, "已调度周期性位置上传任务。")
        }
    }

    override suspend fun doWork(): Result {
        Log.d(TAG, "开始执行位置数据上传任务...")

        try {
            // 检查是否有有效的认证令牌
            val authToken = sharedPrefsManager.getAuthToken()
            if (authToken.isEmpty()) {
                Log.w(TAG, "没有有效的认证令牌，跳过上传")
                return Result.success()
            }

            val unuploadedLocations = locationDao.getUnuploadedLocations(BATCH_SIZE)

            if (unuploadedLocations.isEmpty()) {
                Log.d(TAG, "没有需要上传的位置数据。")
                return Result.success()
            }

            Log.d(TAG, "发现 ${unuploadedLocations.size} 条未上传的位置数据，准备上传。")

            // 获取工程师ID和用户名
            val engineerId = sharedPrefsManager.getEngineerId()
            val userName = sharedPrefsManager.getUserName()
            
            if (engineerId.isEmpty()) {
                Log.w(TAG, "没有工程师ID，跳过上传")
                return Result.success()
            }
            
            // 将工程师ID转换为Long类型
            val engineerIdLong = try {
                engineerId.toLong()
            } catch (e: NumberFormatException) {
                Log.e(TAG, "工程师ID不是有效的数字: $engineerId")
                return Result.success()
            }

            // 使用认证令牌创建API客户端
            val workOrderApi = ApiClient.getInstanceWithAuth(authToken).create(WorkOrderApi::class.java)

            // 批量上传位置数据
            var uploadedCount = 0
            val uploadedIds = mutableListOf<Long>()
            
            // 创建时间格式化器
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

            // 在IO线程中执行网络请求
            withContext(Dispatchers.IO) {
                for (location in unuploadedLocations) {
                    try {
                        // 格式化时间戳
                        val timestamp = Date(location.timestamp)
                        val formattedTime = dateFormat.format(timestamp)
                        
                        // 构建位置数据参数（使用原始接口格式）
                        val locationParams = mapOf<String, Any>(
                            "id" to engineerIdLong,
                            "name" to userName,
                            "latitude" to location.latitude,
                            "longitude" to location.longitude,
                            "localDateTime" to formattedTime
                        )

                        val call = workOrderApi.reportLocation(locationParams)
                        val response = call.execute()

                        if (response.isSuccessful && response.body()?.code == 200) {
                            uploadedIds.add(location.id)
                            uploadedCount++
                            Log.d(TAG, "成功上传位置数据: ${location.latitude}, ${location.longitude} at $formattedTime")
                        } else {
                            Log.e(TAG, "上传位置数据失败: ${response.code()} - ${response.body()?.msg ?: "未知错误"}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "上传单个位置数据时发生异常: ${e.message}", e)
                    }
                }
            }

            // 标记已上传的数据
            if (uploadedIds.isNotEmpty()) {
                locationDao.markAsUploaded(uploadedIds)
                Log.d(TAG, "已在本地将 ${uploadedIds.size} 条数据标记为已上传。")
            }

            return if (uploadedCount > 0) {
                Log.d(TAG, "成功上传 $uploadedCount 条位置数据。")
                Result.success()
            } else {
                Log.w(TAG, "没有成功上传任何位置数据，稍后重试。")
                Result.retry()
            }

        } catch (e: Exception) {
            Log.e(TAG, "执行上传任务时发生异常", e)
            return Result.retry()
        }
    }
} 
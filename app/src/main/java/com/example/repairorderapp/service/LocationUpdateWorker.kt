package com.example.repairorderapp.service

import android.content.Context
import android.util.Log
import androidx.work.*
import com.example.repairorderapp.util.SharedPrefsManager
import com.tencent.map.geolocation.*
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume

/**
 * 位置更新Worker
 * 使用WorkManager作为备份机制，确保在各种限制下仍能定期更新位置
 */
class LocationUpdateWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {
    
    companion object {
        private const val TAG = "LocationUpdateWorker"
        const val WORK_NAME = "location_update_work"
        
        /**
         * 调度定期位置更新任务
         */
        fun schedulePeriodicWork(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()
            
            val workRequest = PeriodicWorkRequestBuilder<LocationUpdateWorker>(
                15, TimeUnit.MINUTES  // 最小间隔15分钟
            )
                .setConstraints(constraints)
                .addTag(WORK_NAME)
                .build()
            
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                workRequest
            )
            
            Log.d(TAG, "已调度WorkManager定期位置更新任务")
        }
        
        /**
         * 取消定期位置更新任务
         */
        fun cancelWork(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
            Log.d(TAG, "已取消WorkManager位置更新任务")
        }
    }
    
    override suspend fun doWork(): Result {
        Log.d(TAG, "开始执行WorkManager位置更新任务")
        
        return try {
            // 检查用户登录状态
            val sharedPrefs = SharedPrefsManager(applicationContext)
            if (sharedPrefs.getAuthToken().isEmpty()) {
                Log.d(TAG, "用户未登录，跳过位置更新")
                return Result.success()
            }
            
            // 检查服务是否在运行
            if (LocationUpdateService.isRunning()) {
                Log.d(TAG, "位置服务正在运行，跳过Worker更新")
                return Result.success()
            }
            
            // 获取一次位置
            val location = getLocationOnce()
            if (location != null) {
                Log.d(TAG, "WorkManager获取到位置: ${location.latitude}, ${location.longitude}")
                // 这里可以直接上报位置，或者启动服务来处理
                startLocationServiceIfNeeded()
            } else {
                Log.w(TAG, "WorkManager未能获取位置")
            }
            
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "WorkManager位置更新失败: ${e.message}")
            Result.retry()
        }
    }
    
    /**
     * 获取一次位置
     */
    private suspend fun getLocationOnce(): TencentLocation? = suspendCancellableCoroutine { cont ->
        try {
            val locationManager = TencentLocationManager.getInstance(applicationContext)
            val request = TencentLocationRequest.create().apply {
                requestLevel = TencentLocationRequest.REQUEST_LEVEL_ADMIN_AREA
                isAllowGPS = true
                isAllowCache = true
            }
            
            val listener = object : TencentLocationListener {
                override fun onLocationChanged(location: TencentLocation?, error: Int, reason: String?) {
                    if (error == TencentLocation.ERROR_OK && location != null) {
                        cont.resume(location)
                    } else {
                        cont.resume(null)
                    }
                    locationManager.removeUpdates(this)
                }
                
                override fun onStatusUpdate(name: String?, status: Int, desc: String?) {
                    // 忽略状态更新
                }
            }
            
            val result = locationManager.requestSingleFreshLocation(request, listener, null)
            if (result != 0) {
                cont.resume(null)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "获取位置异常: ${e.message}")
            cont.resume(null)
        }
    }
    
    /**
     * 如果需要，启动位置服务
     */
    private fun startLocationServiceIfNeeded() {
        try {
            val starter = LocationServiceStarter(applicationContext)
            if (starter.isServiceEnabled() && !LocationUpdateService.isRunning()) {
                Log.d(TAG, "从WorkManager启动位置服务")
                starter.startLocationService()
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动位置服务失败: ${e.message}")
        }
    }
} 
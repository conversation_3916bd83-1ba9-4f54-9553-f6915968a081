package com.example.repairorderapp.service

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.content.ContextCompat
import com.example.repairorderapp.util.SharedPrefsManager
import com.example.repairorderapp.utils.PermissionUtils

/**
 * 位置服务启动器
 * 用于管理位置服务的启动和停止
 */
class LocationServiceStarter(private val context: Context) {
    companion object {
        private const val TAG = "LocationServiceStarter"
        private const val PREF_NAME = "location_service_prefs"
        private const val KEY_SERVICE_ENABLED = "location_service_enabled"
        private const val DEFAULT_ENABLED = true // 默认启用位置服务
        
        /**
         * 从LoginActivity或MainActivity调用，启动位置服务
         */
        fun startLocationServiceAfterLogin(context: Context) {
            val starter = LocationServiceStarter(context)
            // 检查是否已登录
            val sharedPrefs = SharedPrefsManager(context)
            if (sharedPrefs.getAuthToken().isNotEmpty()) {
                Log.d(TAG, "用户已登录，启动位置服务")
                starter.startLocationService()
            } else {
                Log.d(TAG, "用户未登录，不启动位置服务")
            }
        }
    }
    
    private val servicePrefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    /**
     * 启动位置更新服务（如果已启用）
     * 增强权限检查逻辑和异常处理
     */
    fun startLocationService() {
        // 首先检查并修复配置文件问题
        try {
            val allPrefs = servicePrefs.all
            if (allPrefs.isEmpty()) {
                Log.w(TAG, "配置文件为空，重置为启用状态")
                servicePrefs.edit()
                    .putBoolean(KEY_SERVICE_ENABLED, true)
                    .apply()
            }
        } catch (e: Exception) {
            Log.e(TAG, "修复配置文件失败: ${e.message}")
        }

        // 检查服务启用状态
        if (!isServiceEnabled()) {
            Log.w(TAG, "位置服务被禁用")
            return
        }

        // 检查基础权限
        if (!PermissionUtils.hasLocationPermissions(context)) {
            Log.w(TAG, "缺少位置权限")
            return
        }

        // 检查后台权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
            !PermissionUtils.hasBackgroundLocationPermission(context)) {
            Log.w(TAG, "缺少后台位置权限")
        }

        // 检查系统定位功能
        if (!PermissionUtils.isLocationServiceEnabled(context)) {
            Log.w(TAG, "系统定位功能未开启")
            return
        }

        // 启动服务
        val serviceIntent = Intent(context, LocationUpdateService::class.java).apply {
            action = LocationUpdateService.ACTION_START_LOCATION_SERVICE
        }

        try {
            Log.i(TAG, "正在启动位置服务...")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context, serviceIntent)
                Log.i(TAG, "已发送前台服务启动命令")
            } else {
                context.startService(serviceIntent)
                Log.i(TAG, "已发送服务启动命令")
            }

            // 延迟检查服务是否真正启动
            Handler(Looper.getMainLooper()).postDelayed({
                val isRunning = LocationUpdateService.isRunning()
                Log.i(TAG, "服务启动检查: ${if (isRunning) "成功" else "失败"}")
                if (!isRunning) {
                    Log.e(TAG, "服务启动失败，可能在onCreate或onStartCommand中出现异常")
                }
            }, 3000) // 3秒后检查

        } catch (e: Exception) {
            Log.e(TAG, "启动位置服务失败: ${e.message}")
        }

        // 调度WorkManager备份
        try {
            LocationUpdateWorker.schedulePeriodicWork(context)
        } catch (e: Exception) {
            Log.e(TAG, "调度WorkManager失败: ${e.message}")
        }
    }
    
    /**
     * 停止位置更新服务
     */
    fun stopLocationService() {
        try {
            val serviceIntent = Intent(context, LocationUpdateService::class.java).apply {
                action = LocationUpdateService.ACTION_STOP_LOCATION_SERVICE
            }
            context.startService(serviceIntent)
            Log.d(TAG, "已发送停止位置服务的请求")
        } catch (e: Exception) {
            Log.e(TAG, "停止位置服务失败: ${e.message}", e)
        }
        
        // 同时取消WorkManager任务
        try {
            LocationUpdateWorker.cancelWork(context)
            Log.d(TAG, "已取消WorkManager任务")
        } catch (e: Exception) {
            Log.e(TAG, "取消WorkManager失败: ${e.message}")
        }
    }
    
    /**
     * 设置服务启用状态
     */
    fun setServiceEnabled(enabled: Boolean) {
        servicePrefs.edit().putBoolean(KEY_SERVICE_ENABLED, enabled).apply()
        
        if (enabled) {
            // 如果启用，立即启动服务
            startLocationService()
        } else {
            // 如果禁用，停止服务
            stopLocationService()
        }
        
        Log.d(TAG, "位置服务状态已设置为: ${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 检查服务是否已启用
     */
    fun isServiceEnabled(): Boolean {
        return servicePrefs.getBoolean(KEY_SERVICE_ENABLED, DEFAULT_ENABLED)
    }
    
    /**
     * 检查服务是否正在运行
     */
    fun isServiceRunning(): Boolean {
        return LocationUpdateService.isRunning()
    }

    /**
     * 获取详细的服务状态信息（用于调试）
     */
    fun getDetailedServiceStatus(): String {
        val status = StringBuilder()

        // 检查服务启用状态
        val isEnabled = isServiceEnabled()
        status.append("位置服务开关: ${if (isEnabled) "✓ 启用" else "✗ 禁用"}\n")

        // 检查服务运行状态
        val isRunning = isServiceRunning()
        status.append("服务运行状态: ${if (isRunning) "✓ 运行中" else "✗ 未运行"}\n")

        // 检查SharedPreferences文件状态
        try {
            val prefFile = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            val allPrefs = prefFile.all
            val isEmpty = allPrefs.isEmpty()

            status.append("配置文件状态: ${if (isEmpty) "✗ 空文件" else "✓ 正常"}\n")
            status.append("配置项数量: ${allPrefs.size}\n")

            // 如果配置文件为空，尝试修复
            if (isEmpty) {
                try {
                    prefFile.edit()
                        .putBoolean(KEY_SERVICE_ENABLED, true)
                        .apply()

                    val newPrefs = prefFile.all
                    status.append("配置文件修复: ${if (newPrefs.isNotEmpty()) "✓ 已修复" else "✗ 修复失败"}\n")

                    newPrefs.forEach { (key, value) ->
                        status.append("  $key = $value\n")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "修复配置文件失败: ${e.message}")
                    status.append("配置文件修复: ✗ 修复失败\n")
                }
            } else {
                allPrefs.forEach { (key, value) ->
                    status.append("  $key = $value\n")
                }
            }
        } catch (e: Exception) {
            status.append("配置文件状态: ✗ 读取失败 - ${e.message}\n")
        }

        // 检查权限状态
        val hasLocationPermission = PermissionUtils.hasLocationPermissions(context)
        status.append("基础位置权限: ${if (hasLocationPermission) "✓" else "✗"}\n")

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val hasBackgroundPermission = PermissionUtils.hasBackgroundLocationPermission(context)
            status.append("后台位置权限: ${if (hasBackgroundPermission) "✓" else "✗"}\n")
        }

        // 检查系统定位服务
        val isLocationServiceEnabled = PermissionUtils.isLocationServiceEnabled(context)
        status.append("系统定位服务: ${if (isLocationServiceEnabled) "✓" else "✗"}\n")

        return status.toString()
    }

    /**
     * 强制重置服务状态（用于修复问题）
     */
    fun forceResetServiceState() {
        servicePrefs.edit()
            .putBoolean(KEY_SERVICE_ENABLED, true)
            .apply()

        startLocationService()
    }

    /**
     * 检查厂商ROM限制
     */
    private fun checkManufacturerRestrictions() {
        // 移除详细日志，仅在需要时记录
    }
}
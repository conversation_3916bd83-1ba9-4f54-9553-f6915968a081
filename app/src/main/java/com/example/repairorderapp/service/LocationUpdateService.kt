package com.example.repairorderapp.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ServiceInfo
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.os.IBinder
import android.Manifest
import android.os.PowerManager
import android.util.Log
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.ServiceCompat
import com.example.repairorderapp.MainActivity
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.RetrofitClient
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.util.SharedPrefsManager
import com.example.repairorderapp.util.NotificationHelper
import com.example.repairorderapp.manager.RemoteConfigManager
import com.example.repairorderapp.manager.EnhancedLogCollector
import com.tencent.map.geolocation.TencentLocation
import com.tencent.map.geolocation.TencentLocationListener
import com.tencent.map.geolocation.TencentLocationManager
import com.tencent.map.geolocation.TencentLocationRequest
import kotlinx.coroutines.*
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import android.os.Looper
import java.util.concurrent.TimeUnit
import android.content.BroadcastReceiver
import android.content.IntentFilter
import android.net.wifi.WifiManager
import android.net.wifi.WifiManager.WifiLock
import android.app.UiModeManager
import androidx.annotation.RequiresApi
import android.location.LocationManager
import android.provider.Settings
import android.app.PendingIntent

import android.app.ActivityManager
import com.example.repairorderapp.data.local.AppDatabase
import com.example.repairorderapp.data.model.LocationPoint
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * 位置更新后台服务
 * 用于定期获取工程师位置并上报到服务器
 * 支持位置缓存、网络监听和后台定位
 */
class LocationUpdateService : Service(), TencentLocationListener {
    companion object {
        private const val TAG = "LocationUpdateService"
        private const val NOTIFICATION_ID = 12345
        private const val CHANNEL_ID = "location_service_channel"

        // 位置更新间隔（毫秒）- 根据屏幕状态调整
        private const val UPDATE_INTERVAL_SCREEN_ON = 5 * 60 * 1000L   // 亮屏：5分钟
        private const val UPDATE_INTERVAL_SCREEN_OFF = 10 * 60 * 1000L  // 息屏：10分钟
        private const val UPDATE_INTERVAL_STATIONARY = 30 * 60 * 1000L // 静止：30分钟
        
        // 位置回调超时 - 根据屏幕状态调整
        private const val LOCATION_CALLBACK_TIMEOUT_SCREEN_ON = 90_000L   // 亮屏：90秒
        private const val LOCATION_CALLBACK_TIMEOUT_SCREEN_OFF = 120_000L  // 息屏：120秒

        // Intent actions
        const val ACTION_START_LOCATION_SERVICE = "com.example.repairorderapp.START_LOCATION_SERVICE"
        const val ACTION_STOP_LOCATION_SERVICE = "com.example.repairorderapp.STOP_LOCATION_SERVICE"

        // 服务是否正在运行
        private var isServiceRunning = false

        // 最大缓存位置信息数量
        private const val MAX_CACHED_LOCATIONS = 1  // 缓存最多1条位置信息

        // 省电模式下的特殊配置
        private const val POWER_SAVE_UPDATE_INTERVAL = 8 * 60 * 1000L      // 省电模式：8分钟
        private const val POWER_SAVE_CALLBACK_TIMEOUT = 45_000L             // 省电模式超时：45秒
        private const val POWER_SAVE_WAKELOCK_DURATION = 30 * 60 * 1000L    // 省电模式唤醒锁：30分钟

        // 静态位置信息，用于跨实例访问
        private var staticLastLocation: TencentLocation? = null
        private var staticLastLocationTime: Long = 0L
        private const val LOCATION_CACHE_TIMEOUT = 60 * 60 * 1000L // 60分钟缓存超时

        // Android 12+ 后台定位相关常量
        private const val UPDATE_INTERVAL_BACKGROUND_RESTRICTED = 10 * 60 * 1000L  // 受限时：10分钟
        private const val ALARM_REQUEST_CODE = 2001
        private const val WORK_TAG_LOCATION = "location_update_work"
        
        // 保活相关常量
        private const val KEEP_ALIVE_NOTIFICATION_ID = 12346
        private const val DUAL_PROCESS_ENABLED = true  // 是否启用双进程保活
        
        // 服务实例引用
        @Volatile
        private var serviceInstance: LocationUpdateService? = null
        
        /**
         * 获取服务实例
         */
        fun getInstance(): LocationUpdateService? {
            return serviceInstance
        }

        /**
         * 获取服务运行状态
         */
        fun isRunning(): Boolean {
            return isServiceRunning
        }
        
        /**
         * 获取最新的位置信息
         * @return 最新的位置信息，如果没有或已过期则返回null
         */
        fun getLastLocation(): TencentLocation? {
            val currentTime = System.currentTimeMillis()
            return if (staticLastLocation != null &&
                (currentTime - staticLastLocationTime) <= LOCATION_CACHE_TIMEOUT) {
                staticLastLocation
            } else {
                null
            }
        }

        /**
         * 更新静态位置信息
         */
        internal fun updateLastLocation(location: TencentLocation?) {
            staticLastLocation = location
            staticLastLocationTime = System.currentTimeMillis()
        }
    }

    // 腾讯定位管理器
    private lateinit var locationManager: TencentLocationManager
    private lateinit var locationRequest: TencentLocationRequest

    // API服务
    private lateinit var apiService: WorkOrderApi

    // 协程作用域
    private lateinit var serviceScope: CoroutineScope

    // 网络状态监听
    private lateinit var connectivityManager: ConnectivityManager
    private var networkCallback: ConnectivityManager.NetworkCallback? = null

    // 通知管理器
    private lateinit var notificationManager: NotificationManager

    // 网络状态
    private var isNetworkAvailable = false

    // 缓存的位置信息
    private val cachedLocations = Collections.synchronizedList(ArrayList<LocationData>())

    // 唤醒锁
    private var wakeLock: PowerManager.WakeLock? = null

    // 上次位置信息
    private var lastLocation: TencentLocation? = null
    
    // 数据库DAO
    private val locationDao by lazy { AppDatabase.getDatabase(this).locationDao() }

    // 缓存处理器
    private var cacheUploadJob: Job? = null

    // 定时位置更新任务
    private var locationUpdateJob: Job? = null

    // 远程配置管理器和日志收集器
    private lateinit var remoteConfigManager: RemoteConfigManager
    private lateinit var enhancedLogCollector: EnhancedLogCollector

    // 最后一次位置更新时间
    private var lastLocationUpdateTime = 0L
    
    // 静止状态检测相关
    private var lastStationaryCheckLocation: TencentLocation? = null
    private var stationaryStartTime: Long = 0L
    private var isDeviceStationary = false
    private val STATIONARY_DISTANCE_THRESHOLD_METERS = 50f // 50米
    private val STATIONARY_TIME_THRESHOLD_MS = 10 * 60 * 1000L // 10分钟
    
    // 屏幕状态相关
    private var isScreenOn = true
    private lateinit var powerManager: PowerManager
    
    // 监听器注册状态标志
    private var isScreenReceiverRegistered = false
    private var isNetworkCallbackRegistered = false
    private var isPowerSaveReceiverRegistered = false
    
    // 屏幕状态变化处理
    private var screenStateReceiver: BroadcastReceiver? = null
    
    // 回调看门狗相关
    private var callbackWatchdogJob: Job? = null
    private var lastRequestTime = 0L
    
    // 网络保持相关
    private var wifiLock: WifiLock? = null
    private lateinit var wifiManager: WifiManager
    private var isPowerSaveMode = false
    
    // 通知刷新任务
    private lateinit var notificationRefreshJob: Job
    
    // 位置设置提醒状态
    private var lastLocationSettingsNotificationTime = 0L
    private val LOCATION_NOTIFICATION_INTERVAL = 5 * 60 * 1000L // 5分钟间隔
    
    // 省电模式监听器相关
    private var powerSaveReceiver: BroadcastReceiver? = null
    
    // Android 12+ 权限状态
    private var hasBackgroundLocationPermission = false
    private var isBackgroundRestricted = false
    
    // 保活相关
    private var keepAliveJob: Job? = null
    
    // 防重复请求标志
    private var isLocationRequestInProgress = false
    
    // 唤醒锁相关
    private var wakeLockRefreshJob: Job? = null
    private val wakeLockMutex = Any()  // 唤醒锁同步锁

    // 位置数据类
    data class LocationData(
        val latitude: Double,
        val longitude: Double,
        val accuracy: Float,
        val timestamp: Long
    )

    override fun onCreate() {
        super.onCreate()
        
        // 设置服务实例引用
        serviceInstance = this

        // 初始化日志记录
        Log.i(TAG, "位置服务已创建")
        
        // 获取电源管理器
        powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        isScreenOn = powerManager.isInteractive
        
        // 移除AlarmManager（已不使用，改为WorkManager）
        
        // 检查Android 12+权限状态
        checkAndroid12Permissions()
        
        // 注册屏幕状态监听
        registerScreenStateReceiver()

        // 检查并加载用户信息
        checkAndInitUserInfo()

        // 获取系统服务
        connectivityManager = getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
        notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager

        // 创建通知渠道
        createNotificationChannel()

        // 设置隐私合规接口(腾讯定位SDK 7.6+要求)
        try {
            val privacyClass = Class.forName("com.tencent.map.geolocation.TencentLocationManager")
            val privacyMethod = privacyClass.getDeclaredMethod("setUserAgreePrivacy", Boolean::class.java)
            privacyMethod.invoke(null, true)
            Log.i("定位调试", "位置服务 - 成功设置隐私合规接口：已同意")
        } catch (e: Exception) {
            Log.e("定位调试", "位置服务 - 设置隐私合规接口失败: ${e.message}")
        }

        // 初始化腾讯位置服务
        locationManager = TencentLocationManager.getInstance(this)

        // 配置位置请求
        locationRequest = TencentLocationRequest.create().apply {
            interval = UPDATE_INTERVAL_SCREEN_ON  // 设置定位周期
            requestLevel = if (isScreenOn) {
                TencentLocationRequest.REQUEST_LEVEL_ADMIN_AREA
            } else {
                TencentLocationRequest.REQUEST_LEVEL_POI  // 息屏时降低精度以省电
            }
            isAllowGPS = isScreenOn  // 息屏时禁用GPS以省电
            isAllowCache = true  // 允许使用缓存

            // 简化坐标系设置，使用默认值
            try {
                this.setRequestLevel(TencentLocationRequest.REQUEST_LEVEL_ADMIN_AREA)
                Log.d(TAG, "位置请求配置成功")
            } catch (e: Exception) {
                Log.d(TAG, "位置请求配置异常: ${e.message}")
            }
        }

        // 初始化定位服务配置
        initLocationManagerConfig()

        // 加载已缓存的位置数据
        loadCachedLocations()

        // 创建协程范围
        serviceScope = CoroutineScope(Dispatchers.IO)

        // 注册网络回调
        registerNetworkCallback()

        // 初始化Retrofit API服务，使用带认证的客户端
        val sharedPrefs = SharedPrefsManager(this)
        val token = sharedPrefs.getAuthToken()
        val retrofit = if (token.isNotEmpty()) {
            RetrofitClient.createWithAuth(token)
        } else {
            RetrofitClient.getInstance()
        }
        apiService = retrofit.create(WorkOrderApi::class.java)
        
        // 初始化WiFi管理器
        wifiManager = applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
        
        // 检查省电模式状态
        checkPowerSaveMode()

        // 初始化远程配置管理器和日志收集器
        initializeLogSystem()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 立即调用startForeground以避免ANR，使用ServiceCompat以支持Android 14+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14 (API 34)
            // Android 14+ 需要指定前台服务类型
            ServiceCompat.startForeground(
                this,
                NOTIFICATION_ID,
                createNotification(),
                ServiceInfo.FOREGROUND_SERVICE_TYPE_LOCATION
            )
        } else {
            // Android 13 及以下版本使用原有方式
            startForeground(NOTIFICATION_ID, createNotification())
        }
        Log.i(TAG, "位置服务前台服务已启动")

        intent?.let {
            when (it.action) {
                ACTION_START_LOCATION_SERVICE -> {
                    if (!isServiceRunning) {
                        startLocationUpdates()
                    }
                }
                ACTION_STOP_LOCATION_SERVICE -> {
                    stopLocationUpdates()
                    stopSelf()
                }
            }
        } ?: run {
            // 如果没有intent，也启动位置更新
            if (!isServiceRunning) {
                startLocationUpdates()
            }
        }

        // 如果服务被系统终止，则重新启动，并传递最后的intent
        return START_REDELIVER_INTENT
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "位置更新服务销毁")
        
        // 清除服务实例引用
        serviceInstance = null
        
        // 取消保活任务
        keepAliveJob?.cancel()
        
        // 注销所有广播接收器
        unregisterReceivers()
        
        // 取消回调看门狗
        callbackWatchdogJob?.cancel()

        // 保存剩余的缓存位置数据
        saveCachedLocations()

        // 取消定时位置更新任务
        locationUpdateJob?.cancel()

        // 释放资源
        stopLocationUpdates()
        releaseWakeLock()
        unregisterNetworkCallback()
        serviceScope.cancel()
        isServiceRunning = false
        
        // 释放WiFi锁
        releaseWifiLock()

        // 清理日志系统资源
        try {
            if (::enhancedLogCollector.isInitialized) {
                enhancedLogCollector.i(TAG, "位置服务正在停止")
                enhancedLogCollector.flushLogs() // 强制保存剩余日志
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理日志系统资源失败", e)
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "位置更新服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用于后台更新工程师位置"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建并显示前台服务通知
     */
    private fun createNotification(): Notification {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            createOreoNotification()
        } else {
            createLegacyNotification()
        }
    }
    
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createOreoNotification(): Notification {
        createNotificationChannel()
        
        // 检查位置服务状态
        val isLocationEnabled = isLocationServiceEnabled()
        
        // 根据设备状态构建通知内容
        val statusText = when {
            !isLocationEnabled -> "位置服务未开启"
            isPowerSaveMode -> "省电模式运行中"
            !isScreenOn -> "后台运行中"
            else -> "正常运行中"
        }
        
        val intervalText = when {
            !isLocationEnabled -> "等待开启位置服务"
            isPowerSaveMode -> "每8分钟更新"
            !isScreenOn -> "每10分钟更新"
            else -> "每5分钟更新"
        }
        
        return Notification.Builder(this, CHANNEL_ID)
            .setContentTitle("位置服务")
            .setContentText("$statusText • $intervalText")
            .setSmallIcon(R.drawable.ic_location)
            .setOngoing(true)
            .setShowWhen(true)
            .setWhen(System.currentTimeMillis())
            .setCategory(Notification.CATEGORY_SERVICE)
            .setPriority(Notification.PRIORITY_LOW)
            .setVisibility(Notification.VISIBILITY_PUBLIC)
            .build()
    }
    
    private fun createLegacyNotification(): Notification {
        // 检查位置服务状态
        val isLocationEnabled = isLocationServiceEnabled()
        
        val statusText = when {
            !isLocationEnabled -> "位置服务未开启"
            isPowerSaveMode -> "省电模式运行中"
            !isScreenOn -> "后台运行中"
            else -> "正常运行中"
        }
        
        val intervalText = when {
            !isLocationEnabled -> "等待开启位置服务"
            isPowerSaveMode -> "每8分钟更新"
            !isScreenOn -> "每10分钟更新"
            else -> "每5分钟更新"
        }

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("位置服务")
            .setContentText("$statusText • $intervalText")
            .setSmallIcon(R.drawable.ic_location)
            .setOngoing(true)
            .setShowWhen(true)
            .setWhen(System.currentTimeMillis())
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .build()
    }

    /**
     * 检查并初始化用户信息
     */
    private fun checkAndInitUserInfo() {
        val sharedPrefsManager = SharedPrefsManager(applicationContext)
        val userId = sharedPrefsManager.getUserId()
        val userName = sharedPrefsManager.getUserName()

        Log.d(TAG, "检查用户信息: userId=${userId}, userName=${userName}")

        if (userId.isEmpty()) {
            // 尝试从token_pref获取用户信息
            val tokenPrefs = applicationContext.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            val engineerId = tokenPrefs.getString("engineerId", "") ?: ""
            val tokenUserId = tokenPrefs.getString("userId", "") ?: ""
            val tokenUserName = tokenPrefs.getString("userName", "") ?: ""

            Log.d(TAG, "从token_pref获取: engineerId=${engineerId}, userId=${tokenUserId}, userName=${tokenUserName}")

            // 使用工程师ID或用户ID
            val effectiveId = if (engineerId.isNotEmpty()) engineerId else tokenUserId

            if (effectiveId.isNotEmpty()) {
                Log.d(TAG, "将有效ID保存到SharedPrefsManager: $effectiveId")
                sharedPrefsManager.saveUserId(effectiveId)

                if (tokenUserName.isNotEmpty()) {
                    Log.d(TAG, "将用户名保存到SharedPrefsManager: $tokenUserName")
                    sharedPrefsManager.saveUserName(tokenUserName)
                }

                // 获取token并保存
                val token = tokenPrefs.getString("accessToken", "") ?: ""
                if (token.isNotEmpty()) {
                    sharedPrefsManager.saveLoginStatus(true)
                }
            } else {
                Log.w(TAG, "无法从任何数据源获取用户ID")
            }
        }
    }
    
    /**
     * 检查Android 12+权限状态
     */
    private fun checkAndroid12Permissions() {
        // 检查后台位置权限
        val previousBackgroundPermission = hasBackgroundLocationPermission
        hasBackgroundLocationPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_BACKGROUND_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        // 如果后台权限状态发生变化，记录日志
        if (previousBackgroundPermission != hasBackgroundLocationPermission) {
            Log.w(TAG, "后台位置权限状态变化: $previousBackgroundPermission -> $hasBackgroundLocationPermission")
            if (!hasBackgroundLocationPermission) {
                Log.e(TAG, "后台位置权限丢失！这将导致后台定位失效")
            }
        }

        // 检查是否被后台限制
        isBackgroundRestricted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            try {
                val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                activityManager.isBackgroundRestricted
            } catch (e: Exception) {
                false
            }
        } else {
            false
        }

        // 如果被后台限制，记录警告
        if (isBackgroundRestricted) {
            Log.w(TAG, "应用被系统后台限制，可能影响定位服务运行")
        }
        
        // 检查精确位置权限（Android 12+）
        val hasPreciseLocation = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
        
        Log.i(TAG, "权限状态检查 - 后台位置: $hasBackgroundLocationPermission, " +
                   "后台限制: $isBackgroundRestricted, 精确位置: $hasPreciseLocation")
        
        // 如果没有后台位置权限或被限制，记录警告信息
        if (!hasBackgroundLocationPermission || isBackgroundRestricted) {
            Log.w(TAG, "检测到后台定位限制，定位服务可能受到影响")
        }
    }

    /**
     * 初始化位置服务配置
     */
    private fun initLocationManagerConfig() {
        // 初始化位置服务配置
        Log.d(TAG, "初始化位置服务配置")

        // 检查位置权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "位置权限未授予，无法启动位置更新")
                stopSelf()
                return
            }
        }

        // 确保定位服务正常
        try {
            val available = locationManager.coordinateType > 0 // 简单检查腾讯定位服务是否可用
            Log.d(TAG, "定位服务状态: ${if (available) "可用" else "不可用"}")
        } catch (e: Exception) {
            Log.e(TAG, "检查定位服务支持状态失败: ${e.message}")
        }
    }

    /**
     * 注册网络回调
     */
    private fun registerNetworkCallback() {
        if (isNetworkCallbackRegistered) {
            Log.d(TAG, "网络回调已注册，跳过重复注册")
            return
        }
        
        Log.d(TAG, "注册网络状态监听")

        try {
            // 检查当前网络状态
            val activeNetwork = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
            isNetworkAvailable = capabilities != null &&
                    (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR))

            Log.d(TAG, "当前网络状态: ${if (isNetworkAvailable) "可用" else "不可用"}")

            // 注册网络状态变化监听
            networkCallback = object : ConnectivityManager.NetworkCallback() {
                override fun onAvailable(network: Network) {
                    Log.d(TAG, "网络连接恢复")
                    isNetworkAvailable = true

                    // 网络恢复时，上传缓存的位置数据
                    serviceScope.launch {
                    uploadCachedLocations()
                    }
                }

                override fun onLost(network: Network) {
                    Log.d(TAG, "网络连接丢失: $network")
                    // 再次检查网络状态，避免误判
                    val actuallyConnected = isNetworkConnected()
                    Log.d(TAG, "网络丢失后重新检查: $actuallyConnected")
                    isNetworkAvailable = actuallyConnected
                }
            }

            val request = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()

            connectivityManager.registerNetworkCallback(request, networkCallback!!)
            isNetworkCallbackRegistered = true
            Log.d(TAG, "网络状态监听注册成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "注册网络状态监听异常: ${e.message}")
        }
    }

    /**
     * 获取部分唤醒锁以保持CPU运行
     */
    private fun acquireWakeLock() {
        synchronized(wakeLockMutex) {
        if (wakeLock == null || !wakeLock!!.isHeld) {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "RepairOrderApp:LocationServiceWakeLock"
            )
                
                // 根据设备状态调整唤醒锁时间
                val wakeLockTime = when {
                    isPowerSaveMode -> POWER_SAVE_WAKELOCK_DURATION  // 省电模式：30分钟
                    !isScreenOn -> 15 * 60 * 1000L                   // 息屏：15分钟
                    else -> 5 * 60 * 1000L                           // 亮屏：5分钟
                }
                
                wakeLock?.acquire(wakeLockTime)
                
                val mode = when {
                    isPowerSaveMode -> "省电模式"
                    !isScreenOn -> "息屏模式"
                    else -> "正常模式"
                }
                Log.d(TAG, "已获取唤醒锁($mode)，${wakeLockTime/60000}分钟后自动释放")
                
                // 启动唤醒锁刷新任务
                if (!isPowerSaveMode) {
                    startWakeLockRefreshTask()
                }
            }
        }
    }

    /**
     * 启动唤醒锁刷新任务
     */
    private fun startWakeLockRefreshTask() {
        // 取消之前的唤醒锁刷新任务
        wakeLockRefreshJob?.cancel()
        
        wakeLockRefreshJob = serviceScope.launch {
                while (isServiceRunning) {
                try {
                    // 根据屏幕状态确定刷新间隔
                    val refreshInterval = if (isScreenOn) {
                        4 * 60 * 1000L  // 亮屏时4分钟刷新
                    } else {
                        12 * 60 * 1000L  // 息屏时12分钟刷新
                    }
                    
                    delay(refreshInterval)
                    
                    if (isServiceRunning && wakeLock?.isHeld == true) {
                        Log.d(TAG, "刷新唤醒锁")
                        synchronized(wakeLockMutex) {
                            try {
                                releaseWakeLock()
                                acquireWakeLock()
                            } catch (e: Exception) {
                                Log.e(TAG, "唤醒锁刷新异常: ${e.message}")
                            }
                        }
                    }
                } catch (e: CancellationException) {
                    Log.d(TAG, "唤醒锁刷新任务已取消")
                    break
            } catch (e: Exception) {
                    Log.e(TAG, "唤醒锁刷新任务异常: ${e.message}")
                    break
                }
            }
        }
    }

    /**
     * 释放唤醒锁
     */
    private fun releaseWakeLock() {
        synchronized(wakeLockMutex) {
            try {
                if (wakeLock != null && wakeLock!!.isHeld) {
                    wakeLock!!.release()
                    wakeLock = null
                    Log.d(TAG, "已释放唤醒锁")
            }
        } catch (e: Exception) {
                Log.e(TAG, "释放唤醒锁异常: ${e.message}")
                wakeLock = null  // 确保锁对象被清空
            }
        }
    }

    /**
     * 开始位置更新
     */
    private fun startLocationUpdates() {
        // 防止重复启动
        if (isServiceRunning) {
            Log.d(TAG, "位置服务已在运行中，跳过重复启动")
            return
        }
        
        try {
            
            // 首先检查位置服务是否开启
            if (!isLocationServiceEnabled()) {
                Log.w(TAG, "位置服务未开启，显示提醒并启动检查器")
                handleLocationServiceDisabled()
                // 继续启动服务，但会定期检查位置服务状态
            }
            
            // 检查并申请电池优化白名单
            checkBatteryOptimization()
            
            // 获取部分唤醒锁
            acquireWakeLock()
            
            // 注册各种监听器（带重复检查）
            registerScreenStateReceiver()
            registerNetworkCallback()
            registerPowerSaveModeReceiver()
            
            // 开始周期性的通知刷新任务
            if (!::notificationRefreshJob.isInitialized || notificationRefreshJob.isCancelled) {
                startNotificationRefreshTask()
            }
            
            // 创建位置请求配置
            updateLocationRequestConfig()
            
            // 启动周期性位置更新任务（使用单次请求模式，不使用持续监听）
            startLocationUpdateTask()
            
            // 启动保活任务
            startKeepAliveTask()
            
            // 调度数据上传任务
            UploadWorker.schedulePeriodicWork(this)

            // 启动位置上传状态监控
            startLocationUploadMonitoring()

            Log.i(TAG, "位置服务已启动")
            
        } catch (e: Exception) {
            Log.e(TAG, "启动位置更新失败: ${e.message}", e)
        }
    }

    /**
     * 停止位置更新
     */
    private fun stopLocationUpdates() {
        try {
            Log.d(TAG, "停止位置更新")

            // 设置服务停止状态
        isServiceRunning = false
            
            // 停止定位服务（我们使用单次请求模式，无需removeUpdates）
            Log.d(TAG, "位置更新服务已停止")
            
            // 取消各种任务
            locationUpdateJob?.cancel()
            callbackWatchdogJob?.cancel()
            
            // 注销监听器
            unregisterReceivers()
            
            // 保存缓存数据
            saveCachedLocations()
            
        } catch (e: Exception) {
            Log.e(TAG, "停止位置更新异常: ${e.message}", e)
        }
    }

    /**
     * 注销所有广播接收器
     */
    private fun unregisterReceivers() {
        // 注销屏幕状态监听器
        if (isScreenReceiverRegistered) {
            screenStateReceiver?.let {
                try {
                    unregisterReceiver(it)
                    isScreenReceiverRegistered = false
                    Log.d(TAG, "屏幕状态监听器已注销")
                } catch (e: Exception) {
                    Log.e(TAG, "注销屏幕状态监听器异常: ${e.message}")
                }
            }
        }
        // 注销省电模式监听器
        if (isPowerSaveReceiverRegistered) {
            powerSaveReceiver?.let {
                try {
                    unregisterReceiver(it)
                    isPowerSaveReceiverRegistered = false
                    powerSaveReceiver = null
                    Log.d(TAG, "省电模式监听器已注销")
                } catch (e: Exception) {
                    Log.e(TAG, "注销省电模式监听器异常: ${e.message}")
                }
            }
        }
        // 注销网络回调
        if (isNetworkCallbackRegistered) {
            unregisterNetworkCallback()
        }
    }
    
    /**
     * 初始化日志系统
     */
    private fun initializeLogSystem() {
        try {
            remoteConfigManager = RemoteConfigManager.getInstance(this)
            enhancedLogCollector = EnhancedLogCollector.getInstance(this)

            enhancedLogCollector.i(TAG, "位置服务日志系统已初始化")
            Log.i(TAG, "日志系统初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "日志系统初始化失败", e)
        }
    }

    /**
     * 上报位置到服务器（增强省电模式下的重试机制）
     */
    private fun reportLocation(location: TencentLocation) {
        // 首先缓存位置数据
        cacheLocation(location)
        
        // 在协程中将位置信息存入数据库
        serviceScope.launch {
            try {
                val locationPoint = LocationPoint(
                    latitude = location.latitude,
                    longitude = location.longitude,
                    accuracy = location.accuracy,
                    timestamp = location.time,
                    speed = location.speed,
                    bearing = location.bearing,
                    provider = location.provider
                )
                locationDao.insertLocation(locationPoint)
                
                // 保持数据库中只有最新的2条记录
                locationDao.keepLatestRecords(3)
                
                // 位置数据保存成功，不记录额外日志（已在位置获取时记录）

                // 如果网络可用，立即尝试上传缓存的位置数据
                if (isNetworkAvailable) {
                    uploadCachedLocations()
                }
            } catch (e: Exception) {
                Log.e(TAG, "位置数据存入数据库失败", e)
            }
        }
    }

    /**
     * 缓存位置数据
     */
    private fun cacheLocation(location: TencentLocation) {
        synchronized(cachedLocations) {
            // 如果缓存已达到最大容量，移除最老的数据
            if (cachedLocations.size >= MAX_CACHED_LOCATIONS) {
                cachedLocations.removeAt(0)
            }

            // 添加新位置到缓存
            cachedLocations.add(
                LocationData(
                    latitude = location.latitude,
                    longitude = location.longitude,
                    accuracy = location.accuracy,
                    timestamp = System.currentTimeMillis()
                )
            )

            Log.d(TAG, "缓存位置数据，当前缓存数量: ${cachedLocations.size}")

            // 异步保存缓存
            serviceScope.launch {
                saveCachedLocations()
            }
        }
    }

    /**
     * 上传缓存的位置数据
     * @param forceUpload 是否强制上传，即使网络不可用也尝试
     */
    private fun uploadCachedLocations(forceUpload: Boolean = false) {
        // 如果网络不可用且非强制上传，则跳过
        if (!isNetworkAvailable && !forceUpload) {
            Log.d(TAG, "网络不可用，跳过上传缓存")
            return
        }

        // 取消之前的缓存上传任务
        cacheUploadJob?.cancel()

        // 创建新的上传任务
        cacheUploadJob = serviceScope.launch {
            try {
                var uploadSuccess = false
                var retryCount = 0
                val maxRetries = 3

                while (retryCount < maxRetries) {
                    try {
                        // 处理前先检查缓存是否为空
                        var latestLocationData: LocationData? = null
                        var engineerIdLong: Long = 0
                        var userName = ""

                        synchronized(cachedLocations) {
                            if (cachedLocations.isEmpty()) {
                                Log.d(TAG, "没有缓存的位置数据需要上传")
                                return@launch
                            }

                            // 只获取最新的一条位置数据（列表中的最后一条）
                            latestLocationData = cachedLocations.lastOrNull()
                            if (latestLocationData == null) {
                                Log.d(TAG, "无法获取最新位置数据")
                                return@launch
                            }

                            Log.d(TAG, "准备上传最新的位置数据")

                            // 获取工程师ID和名称
                            val sharedPrefs = SharedPrefsManager(applicationContext)
                            var engineerId = sharedPrefs.getEngineerId()
                            userName = sharedPrefs.getUserName()

                            // 如果从SharedPrefsManager拿不到engineerId，尝试从其他数据源获取
                            if (engineerId.isEmpty()) {
                                Log.d(TAG, "从SharedPrefsManager获取工程师ID为空，尝试从其他数据源获取")

                                // 尝试从token_pref获取工程师ID
                                val tokenPrefs = applicationContext.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                                engineerId = tokenPrefs.getString("engineerId", "") ?: ""
                                if (engineerId.isNotEmpty()) {
                                    Log.d(TAG, "从token_pref获取到工程师ID: $engineerId")
                                    // 保存到SharedPrefsManager中以便后续使用
                                    sharedPrefs.saveUserId(engineerId)
                                }

                                // 尝试获取用户名
                                if (userName.isEmpty()) {
                                    userName = tokenPrefs.getString("userName", "") ?: ""
                                    if (userName.isNotEmpty()) {
                                        Log.d(TAG, "从token_pref获取到用户名: $userName")
                                        sharedPrefs.saveUserName(userName)
                                    }
                                }
                            }

                            if (engineerId.isEmpty()) {
                                Log.d(TAG, "工程师ID为空，无法上报缓存位置")
                                return@launch
                            }

                            // 将工程师ID转换为Long类型，处理可能的转换异常
                            try {
                                engineerIdLong = engineerId.toLong()
                            } catch (e: NumberFormatException) {
                                Log.d(TAG, "工程师ID不是有效的数字: $engineerId")
                                return@launch
                            }
                        }

                        // 创建时间格式化器 - synchronized块外处理
                        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

                        // 处理最新位置数据 - synchronized块外处理
                        latestLocationData?.let { locationData ->
                            // 跳过过期的位置数据（超过24小时）
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - locationData.timestamp > 24 * 60 * 60 * 1000) {
                                Log.d(TAG, "跳过过期的位置数据: ${dateFormat.format(Date(locationData.timestamp))}")
                                // 清除过期数据
                                synchronized(cachedLocations) {
                                    cachedLocations.clear()
                                    saveCachedLocations()
                                }
                                return@launch
                            }

                            // 准备请求参数
                            val timestamp = Date(locationData.timestamp)
                            val formattedTime = dateFormat.format(timestamp)

                            val params = mapOf<String, Any>(
                                "id" to engineerIdLong,
                                "name" to userName,
                                "latitude" to locationData.latitude,
                                "longitude" to locationData.longitude,
                                "localDateTime" to formattedTime
                            )

                            try {
                                // 同步调用API上报位置
                                val response = apiService.reportLocation(params).execute()

                                if (response.isSuccessful && response.body()?.code == 200) {
                                    Log.d(TAG, "最新位置上报成功: ${formattedTime}")
                                    uploadSuccess = true

                                    // 上传成功后清除所有缓存数据
                                    synchronized(cachedLocations) {
                                        cachedLocations.clear()
                                        saveCachedLocations()
                                        Log.d(TAG, "已清除所有缓存位置数据")
                                    }
                                } else {
                                    Log.d(TAG, "最新位置上报失败: ${response.code()} - ${response.body()?.msg ?: "未知错误"}")
                                }
                            } catch (e: Exception) {
                                Log.d(TAG, "最新位置上报异常: ${e.message}")
                            }
                        }

                        // 如果上传成功，跳出循环
                        if (uploadSuccess) {
                            Log.d(TAG, "最新位置上传成功，结束任务")
                            break
                        } else {
                            retryCount++
                            if (retryCount < maxRetries) {
                                Log.d(TAG, "最新位置上传失败，尝试重试 (${retryCount}/${maxRetries})")
                                delay(3000L) // 固定5秒重试间隔
                            }
                        }
                    } catch (e: Exception) {
                        Log.d(TAG, "上传缓存位置异常: ${e.message}")
                        retryCount++
                        if (retryCount < maxRetries) {
                            Log.d(TAG, "最新位置上传失败，尝试重试 (${retryCount}/${maxRetries})")
                            delay(3000L) // 固定5秒重试间隔
                        }
                    }
                }

                if (retryCount >= maxRetries) {
                    Log.d(TAG, "最新位置上传达到最大重试次数，放弃上传")
                }
            } catch (e: CancellationException) {
                Log.d(TAG, "缓存位置上传任务已取消")
            } catch (e: Exception) {
                Log.d(TAG, "上传缓存位置总体异常: ${e.message}")
            }
        }
    }

    /**
     * 保存缓存的位置数据到SharedPreferences
     */
    private fun saveCachedLocations() {
        try {
            synchronized(cachedLocations) {
                val sharedPrefs = getSharedPreferences("location_cache", Context.MODE_PRIVATE)
                val editor = sharedPrefs.edit()

                // 将缓存数据转换为JSON字符串
                val gson = com.google.gson.Gson()
                val json = gson.toJson(cachedLocations)

                // 保存到SharedPreferences
                editor.putString("cached_locations", json)
                editor.apply()

                Log.d(TAG, "缓存位置数据已保存: ${cachedLocations.size}条")
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存缓存位置数据异常: ${e.message}")
        }
    }

    /**
     * 从SharedPreferences加载缓存的位置数据
     */
    private fun loadCachedLocations() {
        try {
            val sharedPrefs = getSharedPreferences("location_cache", Context.MODE_PRIVATE)
            val json = sharedPrefs.getString("cached_locations", null)

            if (json != null) {
                val gson = com.google.gson.Gson()
                val type = com.google.gson.reflect.TypeToken.getParameterized(
                    ArrayList::class.java,
                    LocationData::class.java
                ).type

                val loadedLocations = gson.fromJson<ArrayList<LocationData>>(json, type)

                synchronized(cachedLocations) {
                    cachedLocations.clear()
                    cachedLocations.addAll(loadedLocations)
                    Log.d(TAG, "已加载${cachedLocations.size}条缓存位置数据")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载缓存位置数据异常: ${e.message}")
        }
    }

    /**
     * 检查网络是否连接
     */
    private fun isNetworkConnected(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val activeNetwork = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
                capabilities != null && 
                (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) || 
                 capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) &&
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            } else {
                @Suppress("DEPRECATION")
        val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo != null && networkInfo.isConnected
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查网络状态异常: ${e.message}")
            true // 异常时假设网络可用，避免误判
        }
    }

    /**
     * 注销网络监听回调
     */
    private fun unregisterNetworkCallback() {
        if (isNetworkCallbackRegistered) {
            try {
                networkCallback?.let {
                    connectivityManager.unregisterNetworkCallback(it)
                    isNetworkCallbackRegistered = false
                    Log.d(TAG, "网络回调已注销")
                }
            } catch (e: Exception) {
                Log.e(TAG, "注销网络回调异常: ${e.message}")
            }
        }
    }
    
    /**
     * 注册屏幕状态监听器
     */
    private fun registerScreenStateReceiver() {
        if (isScreenReceiverRegistered) {
            Log.d(TAG, "屏幕状态监听器已注册，跳过重复注册")
            return
        }
        
        try {
            screenStateReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    when (intent?.action) {
                        Intent.ACTION_SCREEN_ON -> {
                            isScreenOn = true
                            Log.d(TAG, "屏幕点亮")
                            updateLocationRequestConfig()
                        }
                        Intent.ACTION_SCREEN_OFF -> {
                            isScreenOn = false
                            Log.d(TAG, "屏幕熄灭")
                            updateLocationRequestConfig()
                        }
                    }
                }
            }
            
            val filter = IntentFilter().apply {
                addAction(Intent.ACTION_SCREEN_ON)
                addAction(Intent.ACTION_SCREEN_OFF)
            }
            
            registerReceiver(screenStateReceiver, filter)
            isScreenReceiverRegistered = true
            Log.d(TAG, "屏幕状态监听器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册屏幕状态监听器异常: ${e.message}")
        }
    }
    
    /**
     * 屏幕状态变化处理
     */
    private fun onScreenStateChanged() {
        // 重新配置位置请求参数
        updateLocationRequestConfig()
        
        // 重启定时位置更新任务
        startPeriodicLocationUpdates()
        
        // 立即进行一次位置更新
        requestLocationUpdate()
        
        Log.d(TAG, "屏幕状态变化，更新间隔调整为: ${getCurrentUpdateInterval()/1000}秒")
    }
    
    /**
     * 更新位置请求配置
     */
    private fun updateLocationRequestConfig() {
        locationRequest = TencentLocationRequest.create().apply {
            interval = getCurrentUpdateInterval()
            
            // 根据设备状态优化定位精度配置
            requestLevel = when {
                isPowerSaveMode -> {
                    // 省电模式下保持较高精度，但优化其他参数
                    TencentLocationRequest.REQUEST_LEVEL_ADMIN_AREA
                }
                !isScreenOn -> {
                    // 息屏时适度降低精度
                    TencentLocationRequest.REQUEST_LEVEL_POI
                }
                else -> {
                    // 亮屏时使用最高精度
                    TencentLocationRequest.REQUEST_LEVEL_ADMIN_AREA
                }
            }
            
            // GPS使用策略优化
            isAllowGPS = when {
                isPowerSaveMode -> true  // 省电模式下仍允许GPS，确保精度
                !isScreenOn -> false     // 息屏时禁用GPS
                else -> true            // 亮屏时允许GPS
            }
            
            isAllowCache = true
        }
        
        Log.d(TAG, "位置请求配置已更新: GPS=${locationRequest.isAllowGPS}, " +
                "精度=${locationRequest.requestLevel}, 间隔=${locationRequest.interval}ms")
    }
    
    /**
     * 获取当前更新间隔（考虑省电模式）
     */
    private fun getCurrentUpdateInterval(): Long {
        return when {
            isDeviceStationary -> UPDATE_INTERVAL_STATIONARY // 优先判断静止状态
            isPowerSaveMode -> POWER_SAVE_UPDATE_INTERVAL
            !isScreenOn -> UPDATE_INTERVAL_SCREEN_OFF
            else -> UPDATE_INTERVAL_SCREEN_ON
        }
    }
    
    /**
     * 获取当前回调超时时间（考虑省电模式）
     */
    private fun getCurrentCallbackTimeout(): Long {
        return when {
            isPowerSaveMode -> POWER_SAVE_CALLBACK_TIMEOUT
            !isScreenOn -> LOCATION_CALLBACK_TIMEOUT_SCREEN_OFF
            else -> LOCATION_CALLBACK_TIMEOUT_SCREEN_ON
        }
    }
    
    /**
     * 启动回调看门狗
     */
    private fun startCallbackWatchdog() {
        callbackWatchdogJob?.cancel()
        callbackWatchdogJob = serviceScope.launch {
            val timeout = getCurrentCallbackTimeout()
            delay(timeout)
            
            // 检查是否超时
            if (System.currentTimeMillis() - lastRequestTime >= timeout) {
                Log.w(TAG, "位置回调超时，重置标志并重新请求定位")
                isLocationRequestInProgress = false // 重置标志
                requestLocationUpdate()
            }
        }
    }
    
    /**
     * 检查设备是否处于省电模式
     */
    private fun checkPowerSaveMode() {
        try {
            isPowerSaveMode = powerManager.isPowerSaveMode
            
            Log.d(TAG, "设备省电模式状态: $isPowerSaveMode")
            
            if (isPowerSaveMode) {
                Log.w(TAG, "设备处于省电模式，启用增强后台保持策略")
                enablePowerSaveOptimizations()
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查省电模式状态异常: ${e.message}")
        }
    }
    
    /**
     * 启用省电模式优化策略
     */
    private fun enablePowerSaveOptimizations() {
        // 获取WiFi锁以保持网络连接
        acquireWifiLock()
        
        // 启用更频繁的心跳检查
        startPowerSaveHeartbeat()
        
        Log.d(TAG, "省电模式优化策略已启用")
    }
    
    /**
     * 获取WiFi锁
     */
    private fun acquireWifiLock() {
        try {
            if (wifiLock == null || !wifiLock!!.isHeld) {
                wifiLock = wifiManager.createWifiLock(
                    WifiManager.WIFI_MODE_FULL_HIGH_PERF,
                    "RepairOrderApp:LocationWifiLock"
                )
                wifiLock?.acquire()
                Log.d(TAG, "已获取WiFi锁，保持网络连接")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取WiFi锁异常: ${e.message}")
        }
    }
    
    /**
     * 释放WiFi锁
     */
    private fun releaseWifiLock() {
        try {
            if (wifiLock != null && wifiLock!!.isHeld) {
                wifiLock!!.release()
                wifiLock = null
                Log.d(TAG, "已释放WiFi锁")
            }
        } catch (e: Exception) {
            Log.e(TAG, "释放WiFi锁异常: ${e.message}")
        }
    }
    
    /**
     * 启动省电模式心跳检查
     */
    private fun startPowerSaveHeartbeat() {
        serviceScope.launch {
            while (isServiceRunning && isPowerSaveMode) {
                try {
                    // 每30秒检查一次服务状态
                    delay(30_000L)
                    
                    Log.d(TAG, "省电模式心跳检查")
                    
                    // 检查网络状态
                    if (!isNetworkAvailable) {
                        Log.w(TAG, "省电模式下网络不可用，尝试重新连接")
                        // 重新获取WiFi锁
                        releaseWifiLock()
                        acquireWifiLock()
                    }
                    
                    // 检查定位服务状态
                    val lastUpdateTime = System.currentTimeMillis() - lastLocationUpdateTime
                    val maxAllowedInterval = getCurrentUpdateInterval() * 2 // 允许2倍的更新间隔
                    if (lastUpdateTime > maxAllowedInterval) {
                        Log.w(TAG, "省电模式下定位服务可能被暂停，强制请求定位 (上次更新: ${lastUpdateTime/1000}秒前)")
                        requestLocationUpdate()
                    }
                    
                    // 刷新唤醒锁
                    if (wakeLock?.isHeld != true) {
                        Log.w(TAG, "省电模式下唤醒锁丢失，重新获取")
                        acquireWakeLock()
                    }
                    
                } catch (e: CancellationException) {
                    Log.d(TAG, "省电模式心跳检查已取消")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "省电模式心跳检查异常: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 检查并申请电池优化白名单
     */
    private fun checkBatteryOptimization() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val packageName = applicationContext.packageName
                val isIgnoring = powerManager.isIgnoringBatteryOptimizations(packageName)
                
                Log.d(TAG, "电池优化状态检查: 应用包名=$packageName, 已忽略电池优化=$isIgnoring")
                
                if (!isIgnoring) {
                    Log.w(TAG, "应用未在电池优化白名单中，可能影响后台定位服务的稳定性")
                    Log.w(TAG, "建议用户在设置中将应用加入电池优化白名单")
                    
                    // 这里可以通过通知或其他方式提醒用户设置白名单
                    // 但不在服务中直接启动Activity
                    showBatteryOptimizationNotification()
                } else {
                    Log.d(TAG, "应用已在电池优化白名单中，后台服务将更加稳定")
                }
            } else {
                Log.d(TAG, "Android M以下版本，无需检查电池优化")
                }
            } catch (e: Exception) {
            Log.e(TAG, "检查电池优化状态异常: ${e.message}")
        }
    }
    
    /**
     * 显示电池优化提醒通知
     */
    private fun showBatteryOptimizationNotification() {
        try {
            val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("位置服务优化提醒")
                .setContentText("为确保位置服务稳定运行，建议将应用加入电池优化白名单")
                .setSmallIcon(R.drawable.ic_engineer_marker)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setAutoCancel(true)
                .build()
            
            notificationManager.notify(NOTIFICATION_ID + 1, notification)
            Log.d(TAG, "已显示电池优化提醒通知")
        } catch (e: Exception) {
            Log.e(TAG, "显示电池优化提醒通知异常: ${e.message}")
        }
    }
    
    /**
     * 定期更新通知状态
     */
    private fun startNotificationRefreshTask() {
        serviceScope.launch {
            while (isServiceRunning) {
                try {
                    // 每5分钟更新一次通知
                    delay(5 * 60 * 1000L)
                    
                    if (isServiceRunning) {
                        val updatedNotification = createNotification()
                        notificationManager.notify(NOTIFICATION_ID, updatedNotification)
                        Log.d(TAG, "前台服务通知已刷新")
                    }
                } catch (e: CancellationException) {
                    Log.d(TAG, "通知刷新任务已取消")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "刷新通知异常: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 监听省电模式变化
     */
    private fun registerPowerSaveModeReceiver() {
        if (isPowerSaveReceiverRegistered) {
            Log.d(TAG, "省电模式监听器已注册，跳过重复注册")
            return
        }
        
        try {
            powerSaveReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    if (intent?.action == PowerManager.ACTION_POWER_SAVE_MODE_CHANGED) {
                        val wasPowerSaveMode = isPowerSaveMode
                        isPowerSaveMode = powerManager.isPowerSaveMode
                        Log.d(TAG, "省电模式状态变化: $wasPowerSaveMode -> $isPowerSaveMode")
                        if (isPowerSaveMode && !wasPowerSaveMode) {
                            Log.w(TAG, "进入省电模式，启用增强策略")
                            enablePowerSaveOptimizations()
                        } else if (!isPowerSaveMode && wasPowerSaveMode) {
                            Log.d(TAG, "退出省电模式，恢复正常策略")
                            releaseWifiLock()
                        }
                        updateLocationRequestConfig()
                        try {
                            val updatedNotification = createNotification()
                            notificationManager.notify(NOTIFICATION_ID, updatedNotification)
                        } catch (e: Exception) {
                            Log.e(TAG, "更新通知失败: ${e.message}")
                        }
                    }
                }
            }
            val filter = IntentFilter(PowerManager.ACTION_POWER_SAVE_MODE_CHANGED)
            registerReceiver(powerSaveReceiver, filter)
            isPowerSaveReceiverRegistered = true
            Log.d(TAG, "省电模式监听器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册省电模式监听器异常: ${e.message}")
        }
    }
    
    /**
     * 启动周期性位置更新任务
     */
    private fun startLocationUpdateTask() {
        serviceScope.launch {
            // 设置服务运行状态
            isServiceRunning = true
            
            // 检查网络连接状态
            isNetworkAvailable = isNetworkConnected()
            if (!isNetworkAvailable) {
                Log.w(TAG, "网络连接不可用，将在网络恢复后上传位置数据")
            }
            
            // 检查位置权限
            val hasFineLocation = ActivityCompat.checkSelfPermission(
                this@LocationUpdateService, Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
            
            val hasCoarseLocation = ActivityCompat.checkSelfPermission(
                this@LocationUpdateService, Manifest.permission.ACCESS_COARSE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
            
            Log.i(TAG, "权限状态 - 精确位置: $hasFineLocation, 粗略位置: $hasCoarseLocation")
            
            if (!hasFineLocation && !hasCoarseLocation) {
                Log.e(TAG, "缺少位置权限，无法启动位置更新")
                stopSelf()
                return@launch
            }
            
            // 检查定位管理器是否正确初始化
            if (!::locationManager.isInitialized) {
                Log.e(TAG, "定位管理器未初始化")
                stopSelf()
                return@launch
            }
            
            Log.i(TAG, "位置请求配置: interval=${locationRequest.interval}, level=${locationRequest.requestLevel}")
            
            // 启动定时位置更新任务
            startPeriodicLocationUpdates()
            
            // 进行首次位置请求
            requestLocationUpdate()
            
            // 将最后位置更新时间设为当前时间
            lastLocationUpdateTime = System.currentTimeMillis()
            
            // 上传之前缓存的位置数据
            uploadCachedLocations()
            
            // 启动定时检查服务状态的任务
            startServiceStatusCheck()
            
            // 启动保活任务
            startKeepAliveTask()
            
            // 调度数据上传任务
            UploadWorker.schedulePeriodicWork(this@LocationUpdateService)
            
            Log.i("定位调试", "位置更新服务已启动")
        }
    }
    
    /**
     * 启动定时位置更新任务
     */
    private fun startPeriodicLocationUpdates() {
        // 取消之前的定时任务
        locationUpdateJob?.cancel()
        
        // 创建新的定时任务
        locationUpdateJob = serviceScope.launch {
            try {
                // 无限循环，定期请求位置更新
                while (isServiceRunning) {
                    // 计算距离上次位置更新的时间
                    val currentTime = System.currentTimeMillis()
                    val timeSinceLastUpdate = currentTime - lastLocationUpdateTime
                    val updateInterval = getCurrentUpdateInterval()
                    
                    // 如果已经超过更新间隔，请求位置更新
                    if (timeSinceLastUpdate >= updateInterval) {
                        val mode = when {
                            isPowerSaveMode -> "省电模式"
                            !isScreenOn -> "息屏模式"
                            else -> "正常模式"
                        }
                        Log.d(TAG, "当前处于$mode，使用${updateInterval/60000}分钟更新间隔")
                        Log.d(TAG, "定时位置更新: 距离上次更新已经过去 ${timeSinceLastUpdate/1000} 秒")
                        requestLocationUpdate()
                        lastLocationUpdateTime = currentTime
                    } else {
                        // 计算需要等待的时间
                        val waitTime = updateInterval - timeSinceLastUpdate
                        val mode = when {
                            isPowerSaveMode -> "省电模式"
                            !isScreenOn -> "息屏模式"
                            else -> "正常模式"
                        }
                        Log.d(TAG, "当前处于$mode，使用${updateInterval/60000}分钟更新间隔")
                        Log.d(TAG, "定时位置更新: 等待 ${waitTime/1000} 秒后进行下次更新")
                        delay(waitTime)
                    }
                }
            } catch (e: CancellationException) {
                Log.d(TAG, "定时位置更新任务已取消")
            } catch (e: Exception) {
                Log.e(TAG, "定时位置更新任务异常: ${e.message}")
                // 发生异常时，等待后重新启动
                if (isServiceRunning) {
                    delay(30_000) // 等待30秒后重试
                    startPeriodicLocationUpdates()
                }
            }
        }
    }
    
    /**
     * 请求位置更新
     */
    private fun requestLocationUpdate() {
        try {
            // 防止重复请求
            if (isLocationRequestInProgress) {
                Log.d(TAG, "位置请求正在进行中，跳过重复请求")
                return
            }
            
            val mode = when {
                isPowerSaveMode -> "省电模式-8分钟间隔"
                !isScreenOn -> "息屏模式-10分钟间隔"
                else -> "正常模式-5分钟间隔"
            }
            Log.d(TAG, "请求位置更新 ($mode)")
            
            // 设置请求进行中标志
            isLocationRequestInProgress = true
            
            // 启动回调看门狗
            lastRequestTime = System.currentTimeMillis()
            startCallbackWatchdog()
            
            // 先尝试常规位置更新
            val result = locationManager.requestSingleFreshLocation(
                locationRequest,
                this,
                Looper.getMainLooper()
            )
            
            if (result == 0) {
                Log.d(TAG, "位置更新请求成功，等待回调")
            } else {
                Log.e(TAG, "位置更新请求失败: $result")
                isLocationRequestInProgress = false // 重置标志
                
                // 如果常规更新失败，使用上次位置或等待下次更新
                lastLocation?.let {
                    val timeDiff = System.currentTimeMillis() - lastLocationUpdateTime
                    if (timeDiff < 30 * 60 * 1000L) { // 30分钟内的位置还算有效
                        Log.d(TAG, "使用缓存位置: ${it.latitude}, ${it.longitude}")
                        reportLocation(it)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "请求位置更新异常: ${e.message}")
            isLocationRequestInProgress = false // 重置标志
        }
    }
    
    /**
     * 启动服务状态检查
     */
    private fun startServiceStatusCheck() {
        serviceScope.launch {
            while (isServiceRunning) {
                try {
                    // 每5分钟检查一次服务状态
                    delay(5 * 60 * 1000L)
                    
                    if (!isServiceRunning) {
                        Log.d(TAG, "服务已停止，停止状态检查")
                        break
                    }
                    
                    // 检查是否长时间没有位置更新
                    val timeSinceLastUpdate = System.currentTimeMillis() - lastLocationUpdateTime
                    val maxInterval = getCurrentUpdateInterval() * 3 // 3倍间隔时间
                    
                    if (timeSinceLastUpdate > maxInterval) {
                        Log.w(TAG, "长时间无位置更新(${timeSinceLastUpdate/1000}秒)，检查服务状态")
                        
                        // 尝试重新请求位置
                        requestLocationUpdate()
                    }
                    
                    // 检查网络状态
                    if (!isNetworkAvailable) {
                        Log.w(TAG, "网络不可用，等待网络恢复")
                    }
                    
                } catch (e: CancellationException) {
                    Log.d(TAG, "服务状态检查任务已取消")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "服务状态检查异常: ${e.message}")
                    delay(60 * 1000L) // 发生异常时，1分钟后重试
                }
            }
        }
    }
    
    /**
     * 状态更新回调
     */
    override fun onStatusUpdate(name: String?, status: Int, desc: String?) {
        Log.d(TAG, "位置状态更新: name=$name, status=$status, desc=$desc")
        
        // 处理位置服务状态异常
        when (status) {
            TencentLocation.ERROR_OK -> {
                Log.d(TAG, "位置服务状态正常")
            }
            2 -> { // ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF
                Log.w(TAG, "位置服务异常: 位置开关被关闭或网络不可用")
                handleLocationServiceDisabled()
            }
            5 -> { // location service switch is off
                Log.w(TAG, "位置服务开关被关闭")
                handleLocationServiceDisabled()
            }
            else -> {
                Log.w(TAG, "位置服务状态异常: 状态码=$status, 描述=$desc")
                if (desc?.contains("location") == true || desc?.contains("disabled") == true) {
                    handleLocationServiceDisabled()
                }
            }
        }
    }
    
    /**
     * 处理位置服务被禁用的情况
     */
    private fun handleLocationServiceDisabled() {
        // 检查是否需要显示通知（避免频繁提醒）
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastLocationSettingsNotificationTime < LOCATION_NOTIFICATION_INTERVAL) {
            Log.d(TAG, "位置设置提醒通知冷却中，跳过此次提醒")
            return
        }
        
        // 显示位置设置提醒
        showLocationSettingsNotification()
        lastLocationSettingsNotificationTime = currentTime
        
        // 暂停频繁的位置请求，改为定期检查
        startLocationServiceChecker()
    }
    
    /**
     * 启动位置服务检查器
     */
    private fun startLocationServiceChecker() {
        serviceScope.launch {
            try {
                var checkCount = 0
                val maxChecks = 12 // 最多检查12次（1小时）
                
                while (checkCount < maxChecks && isServiceRunning) {
                    delay(5 * 60 * 1000L) // 每5分钟检查一次
                    checkCount++
                    
                    if (isLocationServiceEnabled()) {
                        Log.d(TAG, "位置服务已开启，恢复正常位置更新")
                        // 取消位置设置提醒通知
                        try {
                            notificationManager.cancel(NOTIFICATION_ID + 1)
                        } catch (e: Exception) {
                            Log.e(TAG, "取消通知异常: ${e.message}")
                        }
                        
                        // 重新启动位置更新
                        requestLocationUpdate()
                        break
                    } else {
                        Log.d(TAG, "位置服务仍未开启，继续等待 (${checkCount}/$maxChecks)")
                    }
                }
                
                if (checkCount >= maxChecks) {
                    Log.w(TAG, "位置服务长时间未开启，停止检查")
                }
            } catch (e: CancellationException) {
                Log.d(TAG, "位置服务检查器已取消")
            } catch (e: Exception) {
                Log.e(TAG, "位置服务检查器异常: ${e.message}")
            }
        }
    }
    
    /**
     * 检查位置服务是否可用
     */
    private fun isLocationServiceEnabled(): Boolean {
        return try {
            val locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
            locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || 
            locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        } catch (e: Exception) {
            Log.e(TAG, "检查位置服务状态异常: ${e.message}")
            false
        }
    }
    
    /**
     * 显示位置设置提醒通知
     */
    private fun showLocationSettingsNotification() {
        try {
            // 创建打开位置设置的Intent
            val settingsIntent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            
            val pendingIntent = PendingIntent.getActivity(
                this,
                0,
                settingsIntent,
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )
            
            val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("位置服务未开启")
                .setContentText("点击前往设置开启位置服务以确保功能正常使用")
                .setSmallIcon(R.drawable.ic_location)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_SOUND)
                .addAction(
                    R.drawable.ic_location,
                    "前往设置",
                    pendingIntent
                )
                .build()
            
            notificationManager.notify(NOTIFICATION_ID + 1, notification)
            Log.d(TAG, "已显示位置设置提醒通知")
        } catch (e: Exception) {
            Log.e(TAG, "显示位置设置提醒通知异常: ${e.message}")
        }
    }
    
    /**
     * 位置更新回调
     */
    override fun onLocationChanged(location: TencentLocation, error: Int, reason: String?) {
        // 取消看门狗
        callbackWatchdogJob?.cancel()
        
        // 重置位置请求进行中标志
        isLocationRequestInProgress = false
        
        if (error == TencentLocation.ERROR_OK) {
            // 位置更新成功
            Log.d(TAG, "获取到新位置: ${location.latitude}, ${location.longitude}, 精度=${location.accuracy}米")
            
            // 保存最后一次位置
            lastLocation = location
            lastLocationUpdateTime = System.currentTimeMillis()
            
            // 更新静态位置信息供外部访问
            updateLastLocation(location)
            
            // 清除位置设置提醒时间（位置获取成功说明服务正常）
            lastLocationSettingsNotificationTime = 0L
            
            // 取消位置设置提醒通知
            try {
                notificationManager.cancel(NOTIFICATION_ID + 1)
            } catch (e: Exception) {
                Log.d(TAG, "取消通知异常（可能不存在）: ${e.message}")
            }
            
            // 判断是否需要过滤低精度位置
            if (location.accuracy > 100) {
                Log.w(TAG, "位置精度较低(${location.accuracy}米)，但仍会上报")
            }
            
            // 记录位置获取成功的日志
            if (::enhancedLogCollector.isInitialized) {
                enhancedLogCollector.logLocation(
                    tag = TAG,
                    message = "位置获取成功: 精度=${location.accuracy}米",
                    latitude = location.latitude,
                    longitude = location.longitude,
                    accuracy = location.accuracy
                )
            }

            // 无论网络是否可用，都尝试上报位置
            reportLocation(location)
        } else {
            // 位置更新失败
            Log.e(TAG, "位置更新失败: 错误码=$error, 原因=$reason")

            // 记录位置获取失败的日志
            if (::enhancedLogCollector.isInitialized) {
                enhancedLogCollector.e(
                    tag = TAG,
                    message = "位置获取失败: 错误码=$error, 原因=$reason"
                )
            }

            // 处理特定错误
            when (error) {
                2 -> { // ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF
                    Log.w(TAG, "位置服务被关闭，提醒用户开启")
                    handleLocationServiceDisabled()
                }
                else -> {
                    Log.w(TAG, "其他位置错误: $error - $reason")
                    
                    // 对于非位置服务关闭的错误，仍然尝试使用缓存位置
                    lastLocation?.let {
                        val timeDiff = System.currentTimeMillis() - lastLocationUpdateTime
                        if (timeDiff < 10 * 60 * 1000L) { // 10分钟内的位置还算有效
                            Log.d(TAG, "使用上次获取的位置: ${it.latitude}, ${it.longitude}")
                            reportLocation(it)
                        }
                    }
                }
            }
        }
        
        // 更新静止状态
        location?.let { updateStationaryStatus(it) }
    }
    
    /**
     * 启动保活任务
     */
    private fun startKeepAliveTask() {
        keepAliveJob?.cancel()
        keepAliveJob = serviceScope.launch {
            while (isServiceRunning) {
                try {
                    // 每分钟检查一次服务状态
                    delay(60_000L)
                    
                    // 检查前台服务状态
                    if (!isServiceRunning) {
                        Log.w(TAG, "检测到服务停止，尝试重启")
                        break
                    }
                    
                    // 刷新前台通知（防止被系统回收）
                    val notification = createNotification()
                    notificationManager.notify(NOTIFICATION_ID, notification)
                    
                    // 检查并刷新唤醒锁
                    if (wakeLock?.isHeld != true) {
                        Log.w(TAG, "唤醒锁丢失，重新获取")
                        acquireWakeLock()
                    }
                    
                    // 检查位置更新状态
                    val timeSinceLastUpdate = System.currentTimeMillis() - lastLocationUpdateTime
                    if (timeSinceLastUpdate > 15 * 60 * 1000L) { // 15分钟无更新
                        Log.w(TAG, "长时间无位置更新，强制请求")
                        requestLocationUpdate()
                    }
                    
                    // 对于厂商系统的特殊处理
                    handleVendorSpecificOptimizations()
                    
                } catch (e: CancellationException) {
                    Log.d(TAG, "保活任务已取消")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "保活任务异常: ${e.message}")
                    break
                }
            }
        }
    }
    
    /**
     * 处理厂商特定的优化
     */
    private fun handleVendorSpecificOptimizations() {
        val manufacturer = Build.MANUFACTURER.lowercase()
        
        when {
            manufacturer.contains("xiaomi") || manufacturer.contains("redmi") -> {
                // MIUI系统特殊处理
                Log.d(TAG, "检测到MIUI系统，应用特殊优化")
                // 可以添加MIUI特定的保活策略
            }
            manufacturer.contains("huawei") || manufacturer.contains("honor") -> {
                // EMUI系统特殊处理
                Log.d(TAG, "检测到EMUI系统，应用特殊优化")
            }
            manufacturer.contains("oppo") || manufacturer.contains("realme") -> {
                // ColorOS系统特殊处理
                Log.d(TAG, "检测到ColorOS系统，应用特殊优化")
            }
            manufacturer.contains("vivo") -> {
                // FuntouchOS系统特殊处理
                Log.d(TAG, "检测到FuntouchOS系统，应用特殊优化")
            }
        }
    }

    /**
     * 更新设备的静止状态
     * 如果设备静止超过阈值时间，则降低定位频率
     */
    private fun updateStationaryStatus(newLocation: TencentLocation) {
        val lastLoc = lastStationaryCheckLocation
        if (lastLoc == null) {
            // 如果是第一次获取位置，则记录并返回
            lastStationaryCheckLocation = newLocation
            return
        }

        val distance = calculateDistance(lastLoc.latitude, lastLoc.longitude, newLocation.latitude, newLocation.longitude)
        Log.d(TAG, "与上次位置距离: ${distance}米")

        if (distance > STATIONARY_DISTANCE_THRESHOLD_METERS) {
            // 设备正在移动
            if (isDeviceStationary) {
                Log.i(TAG, "设备开始移动，恢复正常定位频率。")
                isDeviceStationary = false
                // 立即更新定位请求配置以恢复频率
                onScreenStateChanged() 
            }
            stationaryStartTime = 0L // 重置静止计时器
        } else {
            // 设备可能处于静止状态
            if (stationaryStartTime == 0L) {
                // 开始静止计时
                stationaryStartTime = System.currentTimeMillis()
                Log.d(TAG, "设备可能已静止，开始计时。")
            } else {
                val stationaryDuration = System.currentTimeMillis() - stationaryStartTime
                if (stationaryDuration > STATIONARY_TIME_THRESHOLD_MS && !isDeviceStationary) {
                    // 静止时间超过阈值，且当前不处于静止模式
                    Log.i(TAG, "设备已静止超过${STATIONARY_TIME_THRESHOLD_MS / 60000}分钟，切换到低功耗定位模式。")
                    isDeviceStationary = true
                    // 更新定位请求配置以降低频率
                    onScreenStateChanged()
                }
            }
        }

        // 更新最后一次检查的位置
        lastStationaryCheckLocation = newLocation
    }

    /**
     * 计算两个经纬度点之间的距离（单位：米）
     * 使用 Haversine 公式
     */
    private fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Float {
        val r = 6371e3 // 地球半径（米）
        val phi1 = Math.toRadians(lat1)
        val phi2 = Math.toRadians(lat2)
        val deltaPhi = Math.toRadians(lat2 - lat1)
        val deltaLambda = Math.toRadians(lon2 - lon1)

        val a = sin(deltaPhi / 2) * sin(deltaPhi / 2) +
                cos(phi1) * cos(phi2) *
                sin(deltaLambda / 2) * sin(deltaLambda / 2)
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))

        return (r * c).toFloat()
    }

    /**
     * 启动位置上传状态监控
     */
    private fun startLocationUploadMonitoring() {
        serviceScope.launch {
            while (isActive && isServiceRunning) {
                try {
                    delay(5 * 60 * 1000L) // 每5分钟检查一次

                    // 检查未上传的位置数据数量
                    val unuploadedCount = locationDao.getUnuploadedLocations(100).size

                    if (unuploadedCount > 20) {
                        Log.w(TAG, "检测到大量未上传位置数据: $unuploadedCount 条")

                        // 检查权限状态
                        checkAndroid12Permissions()

                        if (!hasBackgroundLocationPermission && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            Log.e(TAG, "后台位置权限丢失，这是导致位置无法上传的主要原因")
                            // 可以在这里发送通知提醒用户
                        }

                        // 检查网络状态
                        if (!isNetworkAvailable) {
                            Log.w(TAG, "网络不可用，等待网络恢复后上传")
                        } else {
                            Log.i(TAG, "网络可用，尝试强制上传缓存数据")
                            uploadCachedLocations(forceUpload = true)
                        }

                        // 检查系统定位服务状态
                        if (!isLocationServiceEnabled()) {
                            Log.e(TAG, "系统定位服务已关闭，无法获取位置信息")
                        }
                    } else if (unuploadedCount > 0) {
                        Log.d(TAG, "当前有 $unuploadedCount 条位置数据待上传")
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "位置上传状态监控异常", e)
                }
            }
        }
    }

}
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# 保留 AndroidX 核心组件
-keep class androidx.core.app.** { *; }
-keep class androidx.core.content.** { *; }
-keep class androidx.fragment.app.** { *; }
-keep class androidx.appcompat.app.** { *; }
-keep class androidx.multidex.** { *; }

# 为CoreComponentFactory添加特定规则
-keep class androidx.core.app.CoreComponentFactory { *; }

# 如果使用Kotlin反射，添加以下规则
-keepclassmembers class kotlin.Metadata {
    public <methods>;
}

# 如果使用Kotlin协程，添加以下规则
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}
-keepclassmembers class kotlin.coroutines.** {
    volatile <fields>;
}
-keepclassmembers class * {
    kotlin.coroutines.Continuation .* ;
}
-keep class kotlinx.coroutines.android.** { *; }
-keepnames class kotlinx.** { *; }

# 移除日志调用
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int d(...);
    public static int i(...);
    public static int w(...);
    public static int e(...);
}

# 移除我们自定义的日志工具类中的日志调用
-assumenosideeffects class com.example.repairorderapp.utils.LogUtils {
    public static void v(...);
    public static void d(...);
    public static void i(...);
    public static void w(...);
    public static void e(...);
}

# Uncomment this to preserve the line number information for
# debugging stack traces.
-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 以下是自动生成的规则，用于抑制R8优化时的警告
# 添加来自missing_rules.txt的规则
-dontwarn com.tencent.smtt.sdk.ValueCallback
-dontwarn org.bouncycastle.jsse.BCSSLParameters
-dontwarn org.bouncycastle.jsse.BCSSLSocket
-dontwarn org.bouncycastle.jsse.provider.BouncyCastleJsseProvider
-dontwarn org.conscrypt.Conscrypt$Version
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.conscrypt.ConscryptHostnameVerifier
-dontwarn org.openjsse.javax.net.ssl.SSLParameters
-dontwarn org.openjsse.javax.net.ssl.SSLSocket
-dontwarn org.openjsse.net.ssl.OpenJSSE

# 腾讯地图SDK混淆规则
-keep class com.tencent.tencentmap.**{*;}
-keep class com.tencent.map.**{*;}
-keep class com.tencent.beacontmap.**{*;}
-keep class navsns.**{*;}
-dontwarn com.tencent.tencentmap.**
-dontwarn com.tencent.map.**
-dontwarn com.tencent.beacontmap.**
-dontwarn navsns.**

# 腾讯定位SDK混淆规则
-keep class com.tencent.map.geolocation.**{*;}
-keepclassmembers class ** {
    public void on*Event(...);
}
-keep public class com.tencent.location.**{*;}
-dontwarn com.tencent.map.geolocation.**

# 腾讯云COS SDK混淆规则
-keep class com.tencent.cos.** {*;}
-dontwarn com.tencent.cos.**

# Glide混淆规则
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-dontwarn com.bumptech.glide.**

# Matisse图片选择库混淆规则
-keep class com.zhihu.matisse.** {*;}
-dontwarn com.zhihu.matisse.**

# Retrofit2混淆规则
-keepattributes Signature, InnerClasses, EnclosingMethod
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.annotation.**
-dontwarn kotlin.Unit
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# OkHttp混淆规则
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Gson混淆规则
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.examples.android.model.** { <fields>; }
-keep class * implements com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Moshi混淆规则
-keepclasseswithmembers class * {
    @com.squareup.moshi.* <methods>;
}
-keep @com.squareup.moshi.JsonQualifier interface *
-keepclassmembers @com.squareup.moshi.JsonClass class * extends java.lang.Enum {
    <fields>;
    **[] values();
}

# PhotoView混淆规则
-keep class com.github.chrisbanes.photoview.** {*;}
-dontwarn com.github.chrisbanes.photoview.**

# SignaturePad混淆规则
-keep class com.github.gcacace.signaturepad.** {*;}
-dontwarn com.github.gcacace.signaturepad.**

# BouncyCastle混淆规则
-keep class org.bouncycastle.** {*;}
-keepnames class org.bouncycastle.** {*;}
-dontwarn org.bouncycastle.**

# 保留自定义实体类
-keep class com.example.repairorderapp.data.model.** {*;}
-keep class com.example.repairorderapp.data.entity.** {*;}
-keep class com.example.repairorderapp.data.remote.dto.** {*;}

# 额外的Retrofit混淆规则
# 保护所有API接口
-keep,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# 保护API相关类
-keep class com.example.repairorderapp.data.remote.api.** { *; }
-keep class com.example.repairorderapp.data.remote.service.** { *; }

# 保护Response相关类
-keep class retrofit2.Response { *; }
-keep class retrofit2.Call { *; }
-keep class retrofit2.** { *; }
-keep class com.squareup.okhttp.** { *; }

# 保护response converters
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# 保护response model类
-keepclassmembers class com.example.repairorderapp.data.remote.model.** { *; }
-keepclassmembers class com.example.repairorderapp.data.remote.response.** { *; }
-keepclassmembers class com.example.repairorderapp.data.remote.request.** { *; }

# Kotlin反射混淆规则完善
-keep class kotlin.reflect.** { *; }
-keepclassmembers class * {
    @kotlin.Metadata <methods>;
}

# 保护LoginActivity相关代码
-keep class com.example.repairorderapp.ui.login.** { *; }
-keepclassmembers class com.example.repairorderapp.ui.login.** { *; }

# 特别保护LoginService及其相关实现，修复问题
-keep class com.example.repairorderapp.network.service.LoginService { *; }
-keepclassmembers class com.example.repairorderapp.network.service.LoginService { *; }
-keep class com.example.repairorderapp.network.ApiClient { *; }
-keep class com.example.repairorderapp.network.TokenManager { *; }
-keep class com.example.repairorderapp.data.api.LoginService { *; }
-keep class com.example.repairorderapp.data.api.RetrofitClient { *; }
-keep class com.example.repairorderapp.data.api.ApiClient { *; }

# 保护所有模型类和响应类，有多个包结构
-keep class com.example.repairorderapp.data.model.** { *; }
-keep class com.example.repairorderapp.data.remote.model.** { *; }
-keep class com.example.repairorderapp.network.model.** { *; }
-keep class com.example.repairorderapp.data.api.** { *; }
-keep class com.example.repairorderapp.ui.login.** { *; }

# Retrofit完整调用链保护
-keep class retrofit2.** { *; }
-keepattributes Signature, InnerClasses, EnclosingMethod
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.annotation.**
-dontwarn kotlin.Unit
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*
-dontwarn retrofit2.Platform$Java8
-keepnames class retrofit2.Response
-keepnames class retrofit2.Call
-keepnames class retrofit2.Callback
-keepnames class retrofit2.Converter

# OkHttp额外保护
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# 保护ResponseBody
-keepnames class okhttp3.ResponseBody

# 保护Navigation组件
-keepnames class androidx.navigation.fragment.NavHostFragment
-keep class * extends androidx.navigation.Navigator
-keepnames class * extends androidx.navigation.Navigator
-keep class androidx.navigation.** { *; }

# 保护仓库相关的Fragment和其内部类
-keep class com.example.repairorderapp.ui.warehouse.** { *; }
-keepclassmembers class com.example.repairorderapp.ui.warehouse.** { *; }
-keep class com.example.repairorderapp.ui.warehouse.WarehouseApplyFragment { *; }
-keep class com.example.repairorderapp.ui.warehouse.WarehouseApplyFragment$* { *; }
-keep class com.example.repairorderapp.ui.warehouse.WarehouseApplyFragment$SelectedItem { *; }

# 保护所有安全参数 (Safe Args)
-keepclassmembers class ** {
    @androidx.navigation.NavDirections <methods>;
}
-keepclassmembers class ** {
    @androidx.navigation.NavAction <fields>;
}

# 保护所有的Parcelable实现
-keepclassmembers class * implements android.os.Parcelable {
    static ** CREATOR;
}

# 保护所有Serializable类
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 特别保护kotlinx.parcelize注解的类
-keep class * implements android.os.Parcelable {
    public static final ** CREATOR;
}
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator CREATOR;
    <fields>;
    <methods>;
}

# 特别处理kotlinx.parcelize包下的内容
-keepattributes *Annotation*
-keep @kotlinx.parcelize.Parcelize public class *
-keep class **.*$Creator {
    public static ** CREATOR;
}
-keepclassmembers class * {
    @kotlinx.parcelize.* <methods>;
}

# 额外保护所有安全参数 Safe Args 类
-keep class com.example.repairorderapp.**.** { *; }
-keep class * extends androidx.fragment.app.Fragment

# 保护Android核心组件和NavHost
-keep class androidx.navigation.fragment.NavHostFragment { *; }
-keep class androidx.fragment.app.FragmentContainerView { *; }
-keepclassmembers class androidx.navigation.fragment.NavHostFragment { *; }
-keepclassmembers class androidx.fragment.app.FragmentContainerView { *; }

# 特别处理Navigation相关类
-keep class * extends androidx.navigation.Navigator
-keep class androidx.navigation.** { *; }

# 保护NavArgs和Direction类
-keep class **.FragmentArgs { *; }
-keep class **.FragmentDirections { *; }
-keep class **.FragmentDirections$* { *; }
-keep class **.FragmentArgs$* { *; }
-keep class **.NavGraphDirections$* { *; }
-keep class **.NavGraphDirections { *; }

# 保护自定义的Parcelable类
-keepnames class * implements android.os.Parcelable
-keepnames class * implements java.io.Serializable

# ====== TypeToken专用保护规则 ======
# 解决客户设备维修记录和换机记录页面release版本TypeToken报错问题
# 错误信息：TypeToken must be created with a type argument: new TypeToken<...>(){}

# 1. TypeToken核心类保护
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# 2. 泛型信息全面保护
-keepattributes Signature,*Annotation*,EnclosingMethod,InnerClasses

# 3. 保护TypeToken的匿名内部类
-keep class **$*TypeToken* { *; }
-keepclassmembers class * {
    ** *TypeToken*;
}

# ====== customer包模型类全面保护 ======
# 保护customer包下所有模型类，解决DeviceMaintenanceRecord和DeviceReplaceRecord序列化问题
-keep class com.example.repairorderapp.model.customer.** { *; }
-keepclassmembers class com.example.repairorderapp.model.customer.** { 
    <fields>;
    <methods>;
}

# ====== Gson反射机制增强保护 ======
# 增强Gson反射保护，确保TypeToken在混淆环境下正常工作
-keep class com.google.gson.internal.** { *; }
-keep class com.google.gson.reflect.** { *; }

# ====== 其他TypeToken使用位置保护 ======
# 保护项目中其他使用TypeToken的类
-keep class com.example.repairorderapp.ui.orders.PartItem { *; }

# ====== 确保所有@SerializedName字段不被混淆 ======
# 增强现有规则，确保customer包下的字段保护
-keepclassmembers,allowobfuscation class com.example.repairorderapp.model.customer.** {
    @com.google.gson.annotations.SerializedName <fields>;
}
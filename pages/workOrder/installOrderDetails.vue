<template>
	<view class="main">
		<view class="address">
			<view class="address-info">
				<view class="address-title">
					{{ details.customerName || '' }}
				</view>
				<view class="address-details" @click="openMap">
					{{ details.consigneeFullAddress || '' }}
				</view>
			</view>
		</view>
		<view v-if="details.status && details.status.value === 'distributed'" class="transfer-btn" @click="handleOrderTransfer">
			<text>工单转让</text>
		</view>
		<view class="des">
			<view class="name des-boder">
				<view class="name-top">
					<view class="name-left">
						安装客户：
						<view class="staust">
							<text>{{ details.customerName || '' }}</text>
						</view>
					</view>
					<view class="name-right">
						<view class="phone" @click="callToCustomer">
							联系客户
							<u-icon name="phone-fill" size="22" color="#FF541E"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<view class="des-number" v-if="details.expectInstallTime">
				预计安装时间：
				<view class="count">{{ details.expectInstallTime }}</view>
			</view>
			<view class="des-number des-boder">
				工单号：
				<view class="count">{{ details.code }}</view>
			</view>
			<view class="des-number des-boder">
				安装费：
				<view class="count">{{ details.installAmount || '0.00' }}元</view>
			</view>
			<view v-if="details.freight" class="des-number des-boder">
				运费：
				<view class="count">{{ details.freight || '0.00' }}元</view>
			</view>
			<view v-if="details.additionalPay" class="des-number des-boder">
				工程师追加费用：
				<view class="count">{{ details.additionalPay || '0.00' }}元</view>
			</view>
			<view v-if="details.derateAmount" class="des-number des-boder">
				工程师减免费用：
				<view class="count">{{ details.derateAmount || '0.00' }}元</view>
			</view>
			<view class="des-number des-boder">
				应付费用：
				<view class="count">{{ details.totalPay || '0.00' }}元</view>
			</view>
		</view>

		<view class="price">
			<view class="top-box">
				<view class="tui-card">
					<view class="list-header">
						<view class="cell" style="flex: 1">主机类型</view>
						<view class="cell" style="flex: 1">机器型号</view>
						<view class="cell" style="flex: 1">成色</view>
						<view class="cell" style="flex: 1">设备新旧</view>
					</view>
					<view class="list-list" v-for="(item, index) in details.machines" :key="index">
						<view class="cell" style="flex: 1">
							{{ item.hostType.label || '' }}
						</view>
						<view class="cell" style="flex: 1">
							{{ item.productName || '' }}
						</view>
						<view class="cell" style="flex: 1">
							{{ item.percentage.label || '' }}
						</view>
						<view class="cell" style="flex: 1">
							{{ item.deviceOn.label || '' }}
						</view>
					</view>
					<view v-if="!details.machines">
						<rf-empty :info="'暂无数据'"></rf-empty>
					</view>
				</view>
			</view>
		</view>
		<view class="button-content">
			<view class="submit" v-if="details.status.value === 'distributed'">
				<view class="submit-button" @click="handleBtnClick('开始出发')">开始出发</view>
			</view>
			<view class="submit" v-if="details.status.value === 'engineer_departure'">
				<view class="submit-button" @click="handleBtnClick('填写安装报告')">填写安装报告</view>
			</view>
			<view class="submit" v-if="details.status.value === 'wait_confirmed_report' || details.status.value === 'to_be_settled' || details.status.value === 'completed'">
				<view class="submit-button" @click="handleBtnClick('查看安装报告')">查看安装报告</view>
			</view>
		</view>
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
		<!-- 添加工单转让popup -->
		<uni-popup ref="transferPopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">选择转让工程师</text>
					<text class="popup-close" @click="closeTransferPopup">×</text>
				</view>
				<view class="engineer-list">
					<view v-for="(engineer, index) in transferEngineers" :key="index" class="engineer-item" @click="selectTransferEngineer(engineer)">
						<text>{{ engineer.name }}</text>
						<text>{{ engineer.mobileNumber }}</text>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import {
	cancelWorkOrder,
	installWorkOrderDetail,
	cancelAcceptCancelOrder,
	goWorkOrder,
	arriveWorkOrder,
	getIgnoreLocation,
	engineerArriveInstall,
	getAllEngineerList,
	transferInstallWorkOrder
} from '@/api/workOrder';
const userInfo = uni.getStorageSync('userInfo');
export default {
	components: {},
	data() {
		return {
			paddingTop: '',
			price: 300,
			disabled: true,
			isLoading: true,
			detailId: null,
			details: {},
			userInfo: userInfo,
			islookRule: false,
			derateAmount: '', // 工程师减免费用
			workerList: [], // 工程师列表
			engineerId: '',
			engineerName: '',
			processList: [
				{ value: 'CREATE', label: '发起报修', desc: 'createdAt' },
				{
					value: 'ENGINEER_RECEIVE',
					label: '工程师接单',
					desc: 'orderReceiveTime'
				},
				{
					value: 'ENGINEER_DEPARTURE',
					label: '工程师出发',
					desc: 'departureTime'
				},
				{
					value: 'ENGINEER_ARRIVE',
					label: '到店维修',
					desc: 'actualArriveTime'
				},
				{
					value: 'WAIT_CONFIRM',
					label: '确认维修报告',
					desc: 'sendReportTime'
				},
				{ value: 'DONE', label: '已完成', desc: 'completedAt' }
			],
			transferEngineers: [] // 可转让的工程师列表
		};
	},
	onShow() {
		if (this.detailId) {
			this.loadData();
		}
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.loadData();
		uni.stopPullDownRefresh();
	},
	created() {},
	onLoad: function ({ id }) {
		if (!id) {
			uni.navigateTo({
				url: `/pages/workOrder/myWorkOrder`
			});
			return;
		}
		this.detailId = id;
		this.loadData();
	},
	methods: {
		openMap() {
			const {
				customer: { address, location }
			} = this.details;
			wx.getLocation({
				type: 'gcj02',
				success: function (res) {
					const latitude = parseFloat(location.latitude) || res.latitude;
					const longitude = parseFloat(location.longitude) || res.longitude;
					wx.openLocation({
						latitude,
						longitude,
						name: address,
						scale: 18,
						success: function (result) {
							console.log(result);
						},
						fail: function (error) {
							uni.showToast({
								title: '打开地图失败',
								icon: 'none'
							});
						}
					});
				},
				fail: function (err) {
					uni.showToast({
						title: '定位失败',
						icon: 'none'
					});
				}
			});
		},
		async loadData() {
			this.isLoading = true;
			try {
				const result = await installWorkOrderDetail(this.detailId);
				if (result.code === 200) {
					this.details = result.data;
					uni.setStorageSync('orderDetails', this.details);
				}
			} catch (err) {
				console.error(err);
			} finally {
				this.isLoading = false;
			}
		},
		// 打开工单转让弹窗
		async handleOrderTransfer() {
			try {
				const result = await getAllEngineerList('1002', {
					pageNumber: 1,
					pageSize: 1000
				});
				if (result.code === 200) {
					this.transferEngineers = result.data.rows || [];
					this.$refs.transferPopup.open();
				}
			} catch (error) {
				uni.showToast({
					title: '获取工程师列表失败',
					icon: 'none'
				});
			}
		},

		// 关闭转让弹窗
		closeTransferPopup() {
			this.$refs.transferPopup.close();
		},

		// 选择转让工程师
		selectTransferEngineer(engineer) {
			uni.showModal({
				title: '提示',
				content: `确定将该工单转让给${engineer.name}吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await transferInstallWorkOrder({
								id: this.detailId,
								engineerId: engineer.id
							});
							if (result.code === 200) {
								uni.showToast({
									title: '转让成功',
									icon: 'success'
								});
								this.closeTransferPopup();
								uni.navigateBack();
							}
						} catch (error) {
							uni.showToast({
								title: '转让失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		// 联系工程师
		callToCustomer() {
			uni.makePhoneCall({
				phoneNumber: this.details.consigneePhone,
				success: () => {
					console.log('拨打电话成功');
				},
				fail: (err) => {
					console.log('拨打电话失败', err);
				}
			});
		},

		// 开始出发
		async startGo() {
			engineerArriveInstall(this.detailId)
				.then((res) => {
					if (res.code === 200) {
						uni.showToast({
							title: '出发成功',
							icon: 'none'
						});
					}
				})
				.catch((err) => {
					console.error(err);
				})
				.finally(() => {
					this.loadData();
				});
			// const _this = this;
			// uni.getLocation({
			// 	type: 'gcj02',
			// 	success: (res) => {
			// 		goWorkOrder({
			// 			id: _this.detailId,
			// 			location: {
			// 				longitude: res.longitude,
			// 				latitude: res.latitude,
			// 				system: 'GCJ_02'
			// 			}
			// 		})
			// 			.then((res) => {
			// 				console.log(res);
			// 			})
			// 			.catch((err) => {
			// 				console.error(err);
			// 			})
			// 			.finally(() => {
			// 				_this.loadData();
			// 			});
			// 	},
			// 	fail: function (err) {
			// 		uni.showToast({
			// 			title: '获取地理位置失败',
			// 			icon: 'none'
			// 		});
			// 		const userId = uni.getStorageSync('userInfo').id;
			// 		getIgnoreLocation().then((res) => {
			// 			if (res.code === 200 && res.data.includes(userId)) {
			// 				goWorkOrder({
			// 					id: _this.detailId,
			// 					location: {
			// 						longitude: '',
			// 						latitude: '',
			// 						system: 'GCJ_02'
			// 					}
			// 				})
			// 					.then((res) => {
			// 						console.log(res);
			// 					})
			// 					.catch((err) => {
			// 						console.error(err);
			// 					})
			// 					.finally(() => {
			// 						_this.loadData();
			// 					});
			// 			}
			// 		});
			// 		// 根据具体错误信息进行处理
			// 	}
			// });
		},
		// 工程师取消工单
		handleCancelWorkOrder() {
			uni.showModal({
				title: '提示',
				content: '取消工单后，将无法恢复哟。\n（接单后，您取消工单需要店铺确认后才可生效。）',
				success: async (res) => {
					if (res.confirm) {
						try {
							this.isLoading = true;
							const result = await cancelWorkOrder(this.detailId);
							console.log(result);
							if (result.code === 200) {
								uni.showToast({
									title: '取消成功',
									icon: 'none',
									duration: 2000,
									success: () => {
										setTimeout(() => {
											uni.$emit('refreshWorkOrderList');
											uni.navigateBack();
										}, 2000);
									}
								});
							} else if (result.code === 406) {
								uni.showToast({
									title: '取消失败',
									icon: 'none'
								});
								return;
							}
						} catch (err) {
							console.error(err);
						} finally {
							this.isLoading = false;
						}
					}
				}
			});
		},
		// 工程师 确认到店
		handleConfirmArrival() {
			console.log('工程师 确认到店');
			const _this = this;
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					arriveWorkOrder({
						id: _this.detailId,
						location: {
							longitude: res.longitude,
							latitude: res.latitude,
							system: 'GCJ_02'
						}
					})
						.then((res) => {
							console.log(res);
						})
						.catch((err) => {
							console.error(err);
						})
						.finally(() => {
							_this.loadData();
						});
				},
				fail: function (err) {
					uni.showToast({
						title: '获取地理位置失败',
						icon: 'none'
					});
					const userId = uni.getStorageSync('userInfo').id;
					getIgnoreLocation().then((res) => {
						console.log(res, '44444444444');
						if (res.code === 200 && res.data.includes(userId)) {
							arriveWorkOrder({
								id: _this.detailId,
								location: {
									longitude: res.longitude,
									latitude: res.latitude,
									system: 'GCJ_02'
								}
							})
								.then((res) => {
									console.log(res);
								})
								.catch((err) => {
									console.error(err);
								})
								.finally(() => {
									_this.loadData();
								});
						}
					});
					// 根据具体错误信息进行处理
				}
			});
		},
		// 工程师 同意取消
		handleAgreeCancel() {
			uni.showModal({
				title: '提示',
				content: '同意取消工单后，该工单将关闭，无法恢复哟。',
				success: async (res) => {
					if (res.confirm) {
						try {
							this.isLoading = true;
							console.log('同意取消');
							const result = await cancelAcceptCancelOrder(this.detailId);
							console.log(result);
							if (result.code === 200) {
								console.log('取消成功');
								uni.showToast({
									title: '取消成功',
									icon: 'none'
									// duration: 2000,
									// success: () => {
									//   setTimeout(() => {
									//     uni.navigateBack();
									//   }, 2000);
									// },
								});
							} else if (result.code === 406) {
								uni.showToast({
									title: '取消失败',
									icon: 'none'
								});
								return;
							}
						} catch (err) {
							console.error(err);
						} finally {
							this.isLoading = false;
							this.loadData();
						}
					}
				}
			});
		},
		// 填写维修报告
		handleFillInRepairReport() {
			uni.navigateTo({
				url: `/pages/workOrder/installReport?type=0`
			});
		},
		// 查看维修报告
		handleCheckRepairReport() {
			uni.navigateTo({
				url: `/pages/workOrder/installDetails?id=` + this.detailId
			});
		},

		handleBtnClick(type) {
			switch (type) {
				case '开始出发':
					this.startGo();
					break;
				case '取消工单':
					this.handleCancelWorkOrder();
					break;
				case '同意取消':
					this.handleAgreeCancel();
					break;
				case '确认到店':
					this.handleConfirmArrival();
					break;
				case '填写安装报告':
					this.handleFillInRepairReport();
					break;
				case '查看安装报告':
					this.handleCheckRepairReport();
					break;
				default:
					break;
			}
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	width: 100%;
	height: 100%;
	background-color: #f5f6f8;
	padding-bottom: 40rpx;
	.head-nav {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 10000;
	}

	.address {
		width: 100%;
		min-height: 130rpx;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx 24rpx;
		box-sizing: border-box;
		position: relative;
		display: flex;
		justify-content: space-between;
		.address-info {
			width: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			.address-title {
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #0c0c0c;
				line-height: 39rpx;
			}

			.address-details {
				margin-top: 12rpx;
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				line-height: 39rpx;
			}
		}
		.btns {
			width: 30%;
			display: flex;
			flex-direction: column;
			align-items: center;
			.btn {
				text-align: center;
				width: 80%;
				border: 1px solid #f2f2f2;
				border-radius: 10rpx;
				padding: 8rpx 12rpx;
				margin-bottom: 10rpx;
			}
			.transfer {
				width: 60%;
				height: 45rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: none !important;
				color: #fff;
				border-radius: 37px;
				font-size: 25rpx;
				margin: 0;
				background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			}
		}
	}

	.des {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx;
		box-sizing: border-box;

		.des-number {
			width: 100%;
			min-height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;

			.count {
				color: #666666;
			}
		}

		.name {
			width: 100%;
			padding: 26rpx 0;

			.name-top {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;

				.name-left {
					color: #323333;
					display: flex;
					align-items: center;

					.staust {
					}
				}

				.name-right {
					color: #666666;
				}
			}

			.phone {
				width: 100%;
				height: 44rpx;
				margin-top: 22rpx;

				/deep/.u-icon {
					float: right;
				}
			}
		}
	}

	.button-content {
		width: 100%;
		height: 155rpx;
		// background: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		padding: 0 20rpx;
		bottom: 0;
		left: 0;
		z-index: 10;
	}

	.time,
	.price {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 0 24rpx;
		box-sizing: border-box;
	}

	.price {
		padding: 24rpx 0 24rpx 0;
	}
	.des-number {
		width: 100%;
		min-height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #323333;

		.count {
			display: flex;
			align-items: center;
			color: #666666;

			input {
				min-width: 200rpx;
				text-align: right;
				margin-right: 4rpx;
				border: 2rpx solid #f6f6f6;
				width: 200rpx;
			}
		}
	}

	.des-boder {
		border-bottom: 1rpx solid #f3f3f3;
	}

	.submit {
		width: 100%;
		height: 210rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.submit-button {
			width: 705rpx;
			height: 80rpx;
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			border-radius: 40rpx;
			font-size: 31rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
.top-box {
	width: 100%;
	// display: flex;
	overflow: auto;
	background: #fff;
	.tui-card {
		flex: 1;
		.list-header {
			width: 100%;
			margin: auto;
			display: flex;
			background: #fff6f3;
			.cell {
				line-height: 80rpx;
				flex: 1;
				text-align: center;
				color: #333;
				font-size: 27rpx;
				padding: 0 8rpx;
			}
		}

		.tui-card-header,
		.tui-card-header-left {
			width: 100%;
			font-size: 60rpx;
			line-height: 80rpx;
			text-align: center;
		}

		.tui-card-header {
			font-size: 40rpx;
			font-weight: bold;
			line-height: 60rpx;
		}

		.tui-card-header-left {
			text-align: left;
			font-size: 28rpx;
			line-height: 80rpx;
			font-weight: bold;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
	}
	.list-list {
		width: 100%;
		margin: auto;
		display: flex;
		align-items: center;
		justify-content: center;
		line-height: 80rpx;
		background: #fff;
		&:nth-child(odd) {
			background-color: rgba(255, 246, 243, 0.4);
		}
		.cell {
			line-height: 80rpx;
			// max-width: 200rpx;
			flex: 1;
			text-align: center;
			color: #666;
			font-size: 27rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			padding: 0 8rpx;
		}

		.cells {
			line-height: 40rpx !important;
			display: -webkit-box;
			-webkit-line-clamp: 3; /* 显示的行数 */
			-webkit-box-orient: vertical; /* 设置垂直布局 */
			white-space: normal !important;
			overflow: hidden; /* 隐藏超出的内容 */
			text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
		}
		.text {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}
::v-deep .u-collapse-item__content__text {
	padding: 0 !important;
}
::v-deep .u-cell__body {
	padding: 20rpx 0 !important;
	font-weight: bold;
}
.transfer-btn {
	width: 160rpx;
	margin: 20rpx 0;
	height: 60rpx;
	line-height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 20rpx;
	font-size: 26rpx;
	color: #fff;
	text-align: center;
	border-radius: 37px;
	background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
	margin-left: auto;

	&:active {
		background-color: #f5f5f5;
	}
}
.popup-content {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 24rpx 24rpx 0 0;

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;

		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
		}

		.popup-close {
			font-size: 40rpx;
			color: #666;
			padding: 10rpx;
		}
	}
}

.engineer-list {
	max-height: 50vh;
	overflow-y: auto;

	.engineer-item {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 0;
		border-bottom: 1px solid #eee;

		&:active {
			background-color: #f5f5f5;
		}
	}
}
</style>

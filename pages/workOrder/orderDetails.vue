<template>
  <view class="main">
    <view class="address">
      <view class="address-title">
        {{ details.customer.name }}
      </view>
      <view class="address-details">
        {{ details.customer.address }}
      </view>
    </view>

    <!--    <view class="progress">-->
    <!--      <view class="progress-title"> 维修进度跟踪 </view>-->
    <!--      <view class="progress-details">-->
    <!--        <u-steps-->
    <!--          :current="getStepByStatus(details.currentProcess)"-->
    <!--          direction="column"-->
    <!--          activeColor="#FF541E"-->
    <!--          inactiveColor="#D9D9D9"-->
    <!--          dot-->
    <!--        >-->
    <!--          <u-steps-item-->
    <!--            :title="item.label"-->
    <!--            v-for="item in processList"-->
    <!--            :key="item.value"-->
    <!--          ></u-steps-item>-->
    <!--        </u-steps>-->
    <!--      </view>-->
    <!--    </view>-->
    <view class="des">
      <view class="des-class">
        <view class="left-item">
          <image
            class="des-image"
            :src="
              details.customerDeviceGroup &&
              details.customerDeviceGroup.deviceGroupImg.url
                ? details.customerDeviceGroup.deviceGroupImg.url
                : require('../../static/images/top.png')
            "
            mode=""></image>
          <view class="des-class-details">
            <view class="model-number">
              {{ details.customerDeviceGroup.deviceGroup.label }}
            </view>
            <view class="model-brand">
              {{ details.brand + details.machine }}
            </view>
            <view class="model-brand">
              {{ details.serType && details.serType.label }}
            </view>
          </view>
        </view>
        <view class="right-item">
          <view class="btn" @click="historyRecord">历史维修记录</view>
          <view class="btn" @click="replacementRecord(details)">
            PM件更换记录
          </view>
        </view>
      </view>
      <view class="dex-title">故障描述:</view>
      <view class="des-text des-boder" style="word-break: break-all">
        {{ details.excDesc }}
      </view>
      <view class="dex-title">故障照片:</view>
      <view class="des-image-list des-boder">
        <image
          v-for="(item, index) in details.excPics"
          @click="previewImage(details.excPics, index)"
          :key="item.key"
          :src="item.url"
          mode=""></image>
      </view>
      <view class="name des-boder">
        <view class="name-top">
          <view class="name-left">
            报修客户：
            <view class="staust">
              <text>{{ details.customerStaff.name }}</text>
            </view>
          </view>
          <view class="name-right">
            <view class="phone" @click="callToCustomer">
              联系客户
              <u-icon name="phone-fill" size="22" color="#FF541E"></u-icon>
            </view>
          </view>
        </view>
      </view>
      <!-- <view class="name des-boder">
        <view class="name-top">
          <view class="name-left">
            接单工程师：<view class="staust">
              <text>{{ details.engineerId.name }}</text>
              <text>(暂未接单)</text>
            </view>
          </view>
          <view class="name-right"> 对方的 </view>
        </view>
        <view class="phone">
          <u-icon name="phone-fill" size="22" color="#FF541E"></u-icon>
        </view>
      </view> -->
      <view class="des-number" v-if="details.expectArriveTime">
        期望工程师上门时间：
        <view class="count">{{ details.expectArriveTime }}</view>
      </view>
      <view class="des-number" v-if="details.prospectArriveTime">
        工程师预计上门时间：
        <view class="count">{{ details.prospectArriveTime }}</view>
      </view>
    </view>

    <view class="time">
      <view class="des-number des-boder">
        工单号：
        <view class="count">{{ details.code }}</view>
      </view>
      <view class="des-number">
        报修发起时间：
        <view class="count">{{ details.createdAt }}</view>
      </view>
    </view>
    <view
      class="price"
      :style="{ paddingBottom: role === 'boss' ? '50rpx' : '0' }">
      <view class="des-number des-boder">
        上门费：
        <view class="count">{{ details.visitPay }}元</view>
      </view>
      <view class="des-number des-boder" v-if="details.longWayVisitPay">
        远程上门费：
        <view class="count">{{ details.longWayVisitPay }}元</view>
      </view>
      <view class="des-number des-boder" v-if="details.repairPay">
        维修诊断费：
        <view class="count">{{ details.repairPay }}元</view>
      </view>
      <!-- <view class="des-number des-boder" v-if="details.actualReplacePay">
        零件更换费：<view class="count">{{ details.actualReplacePay }}元</view>
      </view> -->
      <view class="des-number des-boder" v-if="details.itemPay">
        维修耗材费用：
        <view class="count">{{ details.itemPay }}元</view>
      </view>

      <view class="des-number des-boder" v-if="details.engineerAdditionalPay">
        工程师追加费用：
        <view class="count">{{ details.engineerAdditionalPay }}元</view>
      </view>
      <view class="des-number des-boder" v-if="details.derateAmount">
        工程师减免费用：
        <view class="count">-{{ details.derateAmount }}元</view>
      </view>
      <view class="des-number des-boder" v-if="details.discountAmount">
        会员减免：
        <view class="count">-{{ details.discountAmount }}元</view>
      </view>
      <view class="des-number des-boder" v-if="details.additionalPay">
        客户追加报酬：
        <view class="count">{{ details.additionalPay }}元</view>
      </view>

      <view class="des-number des-boder">
        应付维修费用：
        <view class="count">
          合计
          <text style="color: #ff541e; font-weight: bold">
            ¥{{ details.totalPay }}
          </text>
          元
        </view>
      </view>
      <view class="des-number des-boder" v-if="details.isContracted">
        实付维修费用：
        <view class="count">
          合计
          <text style="color: #ff541e; font-weight: bold">¥0</text>
          元
        </view>
      </view>
      <view
        class="submit"
        v-if="
          details.status.value !== 'close' &&
          details.status.value !== 'completed' &&
          role !== 'boss'
        ">
        <!-- <view class="submit-button" @click="handleWarehouseClick">
          店铺耗材仓库
        </view> -->
        <view class="submit-button" @click="handleOrderTaking">去接单</view>
      </view>
    </view>
    <rfLoading isFullScreen :active="isLoading"></rfLoading>
  </view>
</template>

<script>
import { cancelWorkOrder, getWorkOrderDetail } from "@/api/workOrder";

const userInfo = uni.getStorageSync("userInfo");
export default {
  components: {},
  data() {
    return {
      paddingTop: "",
      price: 300,
      disabled: true,
      isLoading: false,
      detailId: null,
      details: {},
      userInfo: userInfo,
      processList: [
        { value: "CREATE", label: "发起报修" },
        { value: "ENGINEER_RECEIVE", label: "工程师接单" },
        { value: "ENGINEER_DEPARTURE", label: "工程师出发" },
        { value: "ENGINEER_ARRIVE", label: "到店维修" },
        { value: "WAIT_CONFIRM", label: "确认维修报告" },
        { value: "DONE", label: "已完成" },
      ],
      role: "",
    };
  },
  onShow() {
    if (this.detailId) {
      this.loadData();
    }
  },
  created() {},
  onLoad: function ({ id, role }) {
    if (!id) {
      uni.navigateTo({
        url: `/pages/workOrder/pendingOrder`,
      });
      return;
    }
    this.role = role || "";
    this.detailId = id;
    this.loadData();
  },
  methods: {
    async loadData() {
      try {
        if (this.isLoading) return;
        this.isLoading = true;
        const result = await getWorkOrderDetail(this.detailId);
        if (result.code === 200) {
          this.details = result.data;
        }
      } catch (err) {
        console.error(err);
      } finally {
        this.isLoading = false;
      }
    },
    getStepByStatus(status) {
      return this.processList.findIndex((item) => item.value === status);
    },
    previewImage(url, index) {
      uni.previewImage({
        current: index,
        urls: url.map((item) => item.url),
      });
    },
    HandleCancelWorkOrder() {
      uni.showModal({
        title: "提示",
        content:
          "取消工单后，将无法恢复哟。\n（工程师接单后，您取消工单需要工程师确认后才可生效。）",
        success: async (res) => {
          if (res.confirm) {
            try {
              this.isLoading = true;
              const result = await cancelWorkOrder(this.detailId);
              if (result.code === 200) {
                uni.showToast({
                  title: "取消成功",
                  icon: "none",
                  duration: 2000,
                  success: () => {
                    setTimeout(() => {
                      uni.$emit("refreshWorkOrderList");
                      uni.navigateBack();
                    }, 2000);
                  },
                });
              }
            } catch (err) {
              console.error(err);
            } finally {
              this.isLoading = false;
            }
          }
        },
      });
    },
    // 历史维修记录
    historyRecord() {
      console.log("历史维修记录");
      console.log(this.details.deviceGroupId);
      let id = this.details.deviceGroupId;
      uni.navigateTo({
        url: `/pages/workOrder/historyRecord?id=${id}`,
      });
    },
    // PM件更换记录
    replacementRecord(item) {
      let id = item.customerDeviceGroup.id;
      let productInfo = item.brand + "/" + item.machine;
      let deviceGroup = item.customerDeviceGroup.deviceGroup.label;
      uni.navigateTo({
        url: `/pages/workOrder/pm?productInfo=${productInfo}&deviceGroup= ${deviceGroup}&id=${id}`,
      });
    },
    // 处理接单逻辑
    handleOrderTaking() {
      uni.navigateTo({
        url: `/pages/workOrder/confirmOrder?id=${this.detailId}&time=${this.details.expectArriveTime}`,
      });
    },
    // 跳转仓库
    handleWarehouseClick() {
      console.log("仓库");
    },
    callToCustomer() {
      uni.makePhoneCall({
        phoneNumber: this.details.customerStaff.tel,
        success: () => {
          console.log("拨打电话成功");
        },
        fail: (err) => {
          console.log("拨打电话失败", err);
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.main {
  width: 100%;
  height: 100%;
  background-color: #f5f6f8;

  .head-nav {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
  }

  .address {
    width: 100%;
    min-height: 130rpx;
    margin-top: 22rpx;
    background-color: #fff;
    padding: 24rpx 24rpx;
    box-sizing: border-box;

    .address-title {
      font-size: 27rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #0c0c0c;
      line-height: 39rpx;
    }

    .address-details {
      margin-top: 12rpx;
      font-size: 27rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #666666;
      line-height: 39rpx;
    }
  }

  .progress {
    width: 100%;
    margin-top: 22rpx;
    background-color: #fff;
    padding: 24rpx 24rpx 0rpx;
    box-sizing: border-box;

    .progress-title {
      font-size: 27rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #0c0c0c;
      line-height: 39rpx;
    }

    .progress-details {
      width: 100%;
      min-height: 240rpx;
      margin-top: 12rpx;

      /deep/.u-steps {
        height: 200px;
        margin-top: 8rpx;

        .u-steps-item {
          height: 40rpx !important;

          .u-text__value--content {
            font-size: 28rpx !important;
          }

          .u-text__value--main {
            color: #ff541e;
            font-size: 32rpx !important;
            line-height: normal;
          }
        }
      }
    }
  }

  .des {
    width: 100%;
    margin-top: 22rpx;
    background-color: #fff;
    padding: 24rpx;
    box-sizing: border-box;

    .des-class {
      width: 100%;
      padding-bottom: 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left-item {
        width: 70%;
        display: flex;
        align-items: center;
      }
      .right-item {
        width: 30%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .btn {
          text-align: center;
          width: 100%;
          border: 1px solid #f2f2f2;
          border-radius: 10rpx;
          padding: 8rpx 12rpx;
          margin-bottom: 10rpx;
        }
      }
      .des-image {
        width: 154rpx;
        height: 154rpx;
        border-radius: 13rpx;
        margin-right: 16rpx;
      }

      .des-class-details {
        flex: 1;
        height: 154rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 27rpx;
        font-family: PingFang SC;

        .model-number {
          width: 100%;
          font-weight: bold;
          color: #0c0c0c;
          line-height: 40rpx;
        }

        .model-brand {
          width: 100%;
          margin-top: 10rpx;
          font-weight: 500;
          color: #535353;
          line-height: 40rpx;
        }
      }
    }

    .des-number {
      width: 100%;
      min-height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #323333;

      .count {
        color: #666666;
      }
    }

    .look {
      margin-top: 10rpx;
      font-size: 27rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ff541e;
      text-align: right;
    }

    .toast {
      margin-top: 20rpx;
      padding-bottom: 20rpx;
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #323333;
    }

    .dex-title {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #323333;
      margin-top: 20rpx;
    }

    .des-text {
      min-height: 127rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #666666;
    }

    .des-image-list {
      width: 100%;
      height: 206rpx;
      display: flex;
      align-items: center;

      image {
        width: 154rpx;
        height: 154rpx;
        //background: #474747;
        border-radius: 13rpx;
        margin-right: 20rpx;
      }
    }

    .name {
      width: 100%;
      padding: 26rpx 0;

      .name-top {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;

        .name-left {
          color: #323333;
          display: flex;
          align-items: center;

          .staust {
            //color: #cccccc;
          }
        }

        .name-right {
          color: #666666;
        }
      }

      .phone {
        width: 100%;
        height: 44rpx;
        margin-top: 22rpx;

        /deep/.u-icon {
          float: right;
        }
      }
    }
  }

  .time,
  .price {
    width: 100%;
    margin-top: 22rpx;
    background-color: #fff;
    padding: 0 24rpx;
    box-sizing: border-box;
  }

  .des-number {
    width: 100%;
    min-height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #323333;

    .count {
      display: flex;
      align-items: center;
      color: #666666;

      input {
        min-width: 80rpx;
        text-align: right;
        margin-right: 4rpx;
      }
    }

    .des-pay {
      width: 100%;
      padding: 16rpx 0;
      display: flex;
      align-items: center;
      flex-direction: column;

      .add-pay {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .price-change {
        width: 100%;
        height: 40rpx;
        font-size: 27rpx;
        color: #ff541e;
        text-align: right;
      }
    }
  }

  .des-boder {
    border-bottom: 1rpx solid #f3f3f3;
  }

  .submit {
    width: 100%;
    height: 210rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .submit-button {
      width: 705rpx;
      height: 80rpx;
      background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
      font-size: 31rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      &:first-child {
        border-radius: 40px 0 0 40px;
      }
      &:last-child {
        border-radius: 0 40px 40px 0;
      }
      &:first-child:last-child {
        border-radius: 40px;
      }
    }
  }
}
</style>

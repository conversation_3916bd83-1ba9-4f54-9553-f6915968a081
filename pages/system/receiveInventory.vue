<template>
  <!-- 领料单 -->
  <view class="content">
    <view class="cart-list" v-if="goodsList.length > 0">
      <view class="list" v-for="good in goodsList" :key="good.id">
        <view class="check" @click="checkGood(good.id, good.SkuNum)">
          <text
            class="check-icon"
            :class="{
              'active iconfont icon-duihao': isCheck(good.id),
            }"></text>
        </view>
        <view class="goods">
          <view class="thumb" @tap.stop="previewImage(good.skuPicUrl[0].url)">
            <image :src="good.skuPicUrl[0].url" mode=""></image>
          </view>
          <view class="item">
            <view class="title">
              <text class="two-omit">{{ good.invSkuName }}</text>
            </view>
            <view class="attribute">
              <view class="attr">
                <text>{{ good.saleAttrVals[0].val }}</text>
              </view>
            </view>
            <view class="price-num">
              <view class="price">
                <text class="min">￥</text>
                <text class="max">{{ good.saleUnitPrice }}</text>
              </view>
              <view class="num" @tap.stop>
                <text class="add" @tap.stop="changeBuyNumber(good, 'sub')">
                  <text class="iconfont icon-subtract"></text>
                </text>
                <view class="number">
                  <text>{{ good.SkuNum }}</text>
                </view>
                <text class="add" @tap.stop="changeBuyNumber(good, 'add')">
                  <text class="iconfont icon-add"></text>
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 结算 -->
    <view class="close-account">
      <view class="account">
        <view class="btn-calculate" @click="hisApply">
          <text>申领记录</text>
        </view>
        <view class="btn-calculate" @click="subApply">
          <text>确认申领({{ totalCount }})</text>
        </view>
      </view>
    </view>
    <goods-attr
      ref="GoodsAttr"
      :allSaleAttr="allSaleAttr"
      :sku="sku"
      @choose="handleAttrChoose"
      @addCart="addCart"
      :attrMap="attrMap"
      :skuMap="skuMap"
      :tempSku="tempSku"></goods-attr>
    <view v-if="goodsList.length === 0" style="padding-top: 200rpx">
      <rf-empty :info="'暂未查询到耗材商品哦'"></rf-empty>
    </view>
    <!-- <view class="loading" v-if="!isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view> -->
    <view
      class="loading"
      v-if="goodsList.length > 0 && goodsList.length == total">
      —— 到底啦 ——
    </view>
  </view>
</template>

<script>
import { consumablesList, applyWare, applyInventoryList } from "@/api/index";
import GoodsAttr from "./components/GoodsAttr.vue";
export default {
  components: {
    GoodsAttr,
  },
  data() {
    return {
      // 是否到底
      isEnloading: false,
      // 是否加载完成
      isloading: true,
      // 分页参数
      pageNumber: 1,
      goodsList: [],
      checkList: [],
      params: [],
      totalCount: 0,
      // 总数
      total: 0,
      sku: [],
      allSaleAttr: {},
      attrMap: {},
      skuMap: [],
      currentChoose: {},
      show: false,
      // 图片预览
      previewList: [],
    };
  },
  // 滚动到底部
  // onReachBottom() {
  // 	if ((this.pageNumber - 1) * 10 >= this.total) {
  // 		this.isEnloading = true;
  // 		return;
  // 	}
  // 	this.initData(); // 商品列表
  // },
  onLoad(option) {
    this.params = JSON.parse(option.params);
    this.checkList = this.params;
    this.initData();
    this.calcTotalCount();
  },
  methods: {
    previewImage(url) {
      this.previewList = [];
      this.previewList.push(url);
      uni.previewImage({
        urls: this.previewList,
        current: 0,
      });
    },
    checkGood(goodId, skuNum) {
      let found = false;
      for (let i = 0; i < this.checkList.length; i++) {
        if (this.checkList[i].id === goodId) {
          found = true;
          this.checkList.splice(i, 1);
          break;
        }
      }
      if (!found) {
        this.checkList.push({
          id: goodId,
          SkuNum: skuNum,
        });
      }
      this.calcTotalCount();
    },
    async initData() {
      const productIdArr = this.params.map((item) => {
        return item.id;
      });
      this.isloading = true;
      this.$http
        .post(applyInventoryList, {
          pageNumber: 1,
          pageSize: 999,
          skuIds: productIdArr,
        })
        .then((res) => {
          let result = res.data || [];
          result.forEach((item) => {
            const findIndex = this.params.findIndex((it) => item.id === it.id);
            if (findIndex !== -1) {
              item.SkuNum = this.params[findIndex].SkuNum;
            }
          });
          this.goodsList = result;
          this.total = result.length || 0;
        })
        .catch((err) => {
          uni.showToast({ title: err.message || "系统错误", icon: "none" });
        })
        .finally(() => {
          // this.pageNumber++;
          this.isloading = false;
        });
    },
    getGoodsAttr(data) {
      this.sku = data.skuList;
      this.allSaleAttr = {
        ...this.handleSkuAttr(data.skuList),
      };
      this.init(data);
      console.log(data.choosesku);
      setTimeout(() => {
        this.$refs["GoodsAttr"].show(2, data.choosesku.id);
      }, 300);
    },
    init(data) {
      const allSaleAttr = [
        ...Object.keys(this.allSaleAttr).map((item) => this.allSaleAttr[item]),
      ].flat();
      const primeList = this.getPrime(allSaleAttr.length);
      let index = 0;
      const attrMap = {};
      Object.keys(this.allSaleAttr).forEach((key) => {
        this.allSaleAttr[key].forEach((item) => {
          attrMap[key + "@" + item] = primeList[index];
          index++;
        });
      });
      this.attrMap = {
        ...attrMap,
      };
      const skuMap = [];
      const tempInfo = {
        availableNum: [this.sku[0].availableNum, this.sku[0].availableNum],
        saleUnitPrice: data.miniPrice,
        picsUrl: data.picsUrl[0],
      };
      this.sku.forEach((item) => {
        if (item.availableNum < tempInfo.availableNum[0])
          tempInfo.availableNum[0] = item.availableNum;
        if (item.availableNum > tempInfo.availableNum[1])
          tempInfo.availableNum[1] = item.availableNum;
        const target = item.saleAttrVals;
        let result = 1;
        target.forEach((item) => {
          result *= attrMap[item.name + "@" + item.val];
        });
        skuMap.push(result);
      });
      this.skuMap = skuMap;
      this.tempSku = tempInfo;
    },
    handleSkuAttr(skuList) {
      const skuAttr = [];
      skuList.forEach((item) => {
        skuAttr.push(...item.saleAttrVals);
      });
      const result = [];
      skuAttr.forEach((item) => {
        if (result[item.name]) {
          result[item.name].indexOf(item.val) === -1 &&
            result[item.name].push(item.val);
        } else {
          result[item.name] = [item.val];
        }
      });
      return result;
    },
    getPrime(num) {
      const isPrime = (number) => {
        const target = number / 2;
        if (target === 2) return false;
        for (let index = 2; index < target; index++) {
          if (number % index === 0) return false;
        }
        return true;
      };
      const arr = [];
      for (let i = 2; arr.length < num; i++) {
        if (isPrime(i)) arr.push(i);
      }
      return arr;
    },
    addCart() {
      console.log(this.currentChoose);

      if (JSON.stringify(this.currentChoose) == "{}" || !this.currentChoose) {
        uni.showToast({
          title: "请选择商品",
          icon: "error",
        });
        return;
      }
      let goodsList = JSON.parse(JSON.stringify(this.goodsList));
      goodsList.map((ele) => {
        if (ele.id == this.currentChoose.sku.itemId) {
          let saleAttrValsStr = this.currentChoose.sku.saleAttrVals.map(
            (item) => item.val
          );
          ele.saleAttrValsStr = saleAttrValsStr.join(",");
          ele.choosesku = this.currentChoose.sku;
        }
      });
      setTimeout(() => {
        this.$refs["GoodsAttr"].hide();

        this.goodsList = JSON.parse(JSON.stringify(goodsList));
        this.$forceUpdate();
      }, 300);
    },
    async changeBuyNumber(data, type) {
      if (type == "add") {
        data.SkuNum = data.SkuNum + 1;
      } else if (type == "sub") {
        if (data.SkuNum > 1) {
          data.SkuNum = data.SkuNum - 1;
        }
      }
      // 查找并更新checkList中与data.id匹配的条目
      const foundIndex = this.checkList.findIndex(
        (item) => item.id === data.id
      );
      if (foundIndex !== -1) {
        this.checkList[foundIndex].SkuNum = data.SkuNum;
      }
      this.calcTotalCount();
    },
    isCheck(goodId) {
      for (let i = 0; i < this.checkList.length; i++) {
        if (this.checkList[i].id === goodId) {
          return true; // 找到匹配的id，返回false
        }
      }
      return false; // 没有找到匹配的id，返回true
    },
    // 计算总数
    calcTotalCount() {
      if (this.checkList.length === 0) {
        this.totalCount = 0;
        return;
      }
      const matchedGoods = this.goodsList.filter((good) =>
        this.checkList.some((item) => item.id === good.id)
      );
      const unmatchedItems = this.checkList.filter(
        (item) =>
          !matchedGoods.some((matchedGood) => matchedGood.id === item.id)
      );
      const itemsWithSkuNum = [...matchedGoods, ...unmatchedItems];
      this.totalCount = itemsWithSkuNum.reduce(
        (total, item) => total + item.SkuNum,
        0
      );
    },
    handleAttrChoose(val) {
      this.currentChoose = val;
    },
    // 确认申领
    subApply() {
      if (this.checkList.length === 0) return;
      uni.showModal({
        title: "提示",
        content: "确认申领吗？",
        success: (res) => {
          if (res.confirm) {
            const params = this.checkList.map((item) => {
              const target = this.goodsList.find((good) => good.id === item.id);
              return target;
            });
            let detailList = [];
            params.map((ele) => {
              detailList.push({
                num: ele.SkuNum,
                skuId: ele.id,
              });
            });
            this.$http
              .post(applyWare, { detailList: detailList })
              .then((res) => {
                if (res.code === 200) {
                  this.isSuccess = true;
                  this.$mHelper.toast("申领耗材成功，请自行前往仓库提货");
                  this.checkList = []; // 重置选择列表
                  this.goodsList = []; // 重置商品列表
                  this.calcTotalCount();
                  setTimeout(() => {
                    uni.reLaunch({
                      url: `/pages/home/<USER>
                    });
                  }, 300);
                }
              })
              .catch((err) => {
                this.$mHelper.toast(
                  err || err.message || "申领耗材失败，请稍后再试"
                );
              });
          }
        },
      });
    },
    hisApply() {
      uni.redirectTo({
        url: "/pages/system/warehistory",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.content {
  padding: 0 22rpx;
}
/* 购物车列表 */
.cart-list {
  width: 100%;
  padding: 20rpx 0;
  // margin-top: 20rpx;

  .list {
    display: flex;
    height: 185rpx;
    background-color: #ffffff;
    box-shadow: 0 0 20rpx #f6f6f6;
    border-radius: 10rpx;
    margin-top: 20rpx;

    .check {
      display: flex;
      align-items: center;
      width: 10%;
      height: 80%;
      margin-left: 20rpx;

      // background-color: #fff;

      text {
        font-size: 36rpx;
        color: #333333;
      }

      .icon-checked {
        color: #fe3b0f;
        // box-shadow: 0 0 10rpx  #fe3b0f;
      }
    }

    .goods {
      display: flex;
      align-items: center;
      width: 90%;
      height: 100%;

      .thumb {
        display: flex;
        // align-items: center;
        justify-content: center;
        width: 30%;
        height: 100%;
        margin-top: 20rpx;

        image {
          width: 160rpx;
          height: 160rpx;
          border-radius: 10rpx;
        }
      }

      .item {
        padding: 10rpx 0;
        width: 70%;
        height: 100%;

        .title {
          display: flex;
          align-items: center;
          width: 100%;
          height: auto;

          text {
            font-size: 26rpx;
            color: #212121;
          }
        }

        .attribute {
          display: flex;
          align-items: center;
          margin-top: 10rpx;

          .attr {
            display: flex;
            align-items: center;
            padding: 0 20rpx;
            height: 40rpx;
            background-color: #f6f6f6;
            border-radius: 10rpx;

            text {
              font-size: 24rpx;
              color: #333333;
            }

            .more {
              display: flex;
              width: 10rpx;
              height: 10rpx;
              border-left: 2rpx solid #333333;
              border-bottom: 2rpx solid #333333;
              transform: rotate(-45deg);
              margin-left: 10rpx;
            }
          }
        }

        .price-num {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 80rpx;

          .price {
            display: flex;

            .min {
              color: #fe3b0f;
              font-size: 24rpx;
            }

            .max {
              font-size: 28rpx;
              color: #fe3b0f;
              font-weight: bold;
            }
          }

          .num {
            display: flex;
            height: 40rpx;
            margin-right: 20rpx;

            .add {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 60rpx;
              height: 40rpx;
              background-color: #ffffff;

              text {
                color: #212121;
                font-size: 24rpx;
              }
            }

            .number {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 80rpx;
              height: 40rpx;
              background-color: #f6f6f6;
              border-radius: 8rpx;
              text-align: center;

              text {
                font-size: 24rpx;
                color: #212121;
              }
            }
          }
        }
      }
    }
  }
}
.icon-subtract {
  position: relative;
  margin: 0 20rpx;

  &::after {
    content: "";
    display: block;
    width: 25rpx;
    height: 5rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }
}

.icon-add {
  position: relative;
  margin: 0 20rpx;

  &::after {
    content: "";
    display: block;
    width: 25rpx;
    height: 5rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }

  &::before {
    content: "";
    display: block;
    width: 5rpx;
    height: 25rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }
}
.check-icon {
  width: 35rpx;
  height: 35rpx;
  border: 1px solid #999999;
  border-radius: 50%;

  &.active {
    border: 1px solid #e5452f;
    color: #e5452f !important;
    line-height: 30rpx;
  }
}
/* 结算 */
.close-account {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 130rpx;
  background-color: #ffffff;
  border-top: 2rpx solid #f6f6f6;

  .check-total {
    display: flex;
    align-items: center;
    width: 50%;
    height: 100%;

    .check {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40%;
      height: 100%;

      text {
        font-size: 36rpx;
        color: #333333;
      }

      .icon-checked {
        color: #fe3b0f;
        // box-shadow: 0 0 10rpx  #fe3b0f;
      }

      .all {
        font-size: 24rpx;
        margin-left: 10rpx;
      }
    }

    .total {
      display: flex;
      align-items: center;
      width: 60%;
      height: 100%;

      text {
        font-size: 24rpx;
        color: #333333;
      }

      .price {
        font-weight: bold;
        color: #fe3b0f;
      }
    }
  }

  .account {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 4%;
    margin-top: 20rpx;

    .btn-calculate {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      margin: 0 30rpx;
      height: 60rpx;
      background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
      border-radius: 60rpx;

      text {
        color: #ffffff;
        font-size: 24rpx;
      }
    }

    .btn-del {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .attention {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 140rpx;
        height: 60rpx;
        border: 2rpx solid #eeeeee;
        border-radius: 60rpx;
        color: #333333;
        font-size: 24rpx;
        margin-right: 20rpx;
      }

      .del {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100rpx;
        height: 60rpx;
        background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
        border-radius: 60rpx;
        color: #ffffff;
        font-size: 24rpx;
      }
    }
  }
}
.loading {
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  color: #ccc;
  font-size: 24upx;
  width: 100%;
  padding-bottom: 180rpx;
}
</style>

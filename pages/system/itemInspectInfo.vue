<template>
	<view class="main">
		<view class="box1">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view>
		<view class="wrapper">
			<view class="list" v-for="item in machineList" :key="item.articleCode">
				<view class="list-header">
					<view v-if="item.imageFiles" class="thumb" @tap.stop="previewImage(item.imageFiles[0].url)">
						<image :src="item.imageFiles[0].url"></image>
					</view>
					<view class="header-info">
						<view class="info-row">
							<text>物品编号：</text>
							<view>{{ item.articleCode }}</view>
						</view>
						<view class="info-row">
							<text>物品名称：</text>
							<view>{{ item.articleName }}</view>
						</view>
						<view class="info-row">
							<text>OEM编号：</text>
							<view>{{ item.numberOem || '' }}</view>
						</view>
					</view>
				</view>
				<view class="list-item">
					<!-- 				<view class="info-row">
						<text>物品编号：</text>
						<view>{{ item.articleCode }}</view>
					</view>
					<view class="info-row">
						<text>物品名称：</text>
						<view>
							{{ item.articleName }}
						</view>
					</view>
					<view class="info-row">
						<text>OEM编号：</text>
						<view>
							{{ item.numberOem || '' }}
						</view>
					</view> -->
					<view class="info-row">
						<text>制造商名称：</text>
						<view class="info-item">
							{{ item.manufacturerGoodsName || '' }}
						</view>
					</view>
					<view class="info-row">
						<text>制造商编号：</text>
						<view>
							{{ item.manufacturerGoodsCode || '' }}
						</view>
					</view>
					<view class="info-row">
						<text>制造渠道：</text>
						<view>
							<uni-data-select
								:localdata="manufacturerChannelRange"
								v-model="item.manufacturerChannel"
								placeholder="请选择制造渠道"
								:disabled="editType === 'info'"
							></uni-data-select>
						</view>
					</view>
					<view class="info-row">
						<text>储位：</text>
						<view>
							<input type="text" v-model="item.location" :disabled="editType === 'info'" />
						</view>
					</view>
					<!-- TODO:缺少批次号 -->
					<view class="info-row">
						<view class="info-item">
							<text class="label">库存数量：</text>
							<view class="value">
								{{ item.inventoryNum }}
							</view>
						</view>
						<view class="info-item">
							<text class="label" style="width: 160rpx">盘点数量：</text>
							<view class="value">
								<input type="number" v-model="item.stockNum" confirm-type="down" :disabled="editType === 'info'" />
							</view>
						</view>
					</view>
				</view>
				<!-- 	<view v-if="editType !== 'info'" class="info-row action-row">
					<view class="" @click="handleRowSave(item)">保存</view>
				</view> -->
			</view>
		</view>
		<view class="loading" v-if="!isEnloading && machineList.length">
			{{ isloading ? '加载中...' : machineList.length < total ? '下拉加载更多' : '' }}
		</view>
		<view class="loading" v-if="machineList.length > 0 && machineList.length == total && isEnloading">—— 到底啦 ——</view>
		<rf-empty :info="'暂无盘点数据'" v-if="machineList.length === 0"></rf-empty>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="isloading"></rfLoading>
		<view v-if="editType !== 'info'" class="footer">
			<button class="confirm-btn" @click="handleStash">暂存</button>
			<button class="confirm-btn" @click="submitInventory">确认提交</button>
		</view>

		<!-- 选择机型或选配件 -->
		<u-modal :show="showModel" showCancelButton closeOnClickOverlay @close="handleModelCancel" @cancel="handleModelCancel" @confirm="handleConfirm()">
			<view class="slot-content"></view>
		</u-modal>
	</view>
</template>

<script>
import FormQuery from '@/components/formQuery/index';
import { dictTreeByCodeApi } from '@/api/custom';
import { getInventoryDetailApi, inventorySaveApi, machineInventorySubmitApi, inventorySaveDetailApi } from '@/api/system.js';
import { transformFormParams } from '@/utils';
import { brandModelList, itemStoreList } from '@/api/index';
import { logout } from '../../api/login';
export default {
	components: {
		FormQuery
	},
	data() {
		return {
			id: '',
			warehouseId: '',
			machineList: [],
			isloading: false,
			pageNumber: 1,
			pageSize: 10,
			total: 0,
			editType: 'add',
			isEnloading: false,
			normalRange: [
				{
					text: '是',
					value: 1
				},
				{
					text: '否',
					value: 0
				}
			],
			manufacturerChannelRange: [],
			deviceStatusRange: [],
			hostTypeRange: [],
			showModel: false,
			hostType: '',
			selectedSparePart: null, // 操作行
			productList: [],
			showProduct: false,
			tempProductInfo: {
				name: '',
				productId: ''
			},
			formColumns: [
				// {
				// 	dataIndex: 'product',
				// 	title: '机型型号',
				// 	valueType: 'machine'
				// },
				{
					dataIndex: 'articleCode',
					title: '物品编号',
					valueType: 'input'
				},
				// {
				// 	dataIndex: 'articleName',
				// 	title: '物品名称',
				// 	valueType: 'input'
				// },
				{
					dataIndex: 'numberOem',
					title: 'OEM编号',
					valueType: 'input'
				},
				{
					dataIndex: 'manufacturerGoodsCode',
					title: '制造商物品编号',
					valueType: 'input'
				},
				{
					dataIndex: 'manufacturerGoodsName',
					title: '制造商物品名称',
					valueType: 'input'
				},
				{
					dataIndex: 'location',
					title: '储位',
					valueType: 'input'
				},
				{
					dataIndex: 'isTake',
					title: '是否盘点',
					valueType: 'select',
					localdata: [
						{
							text: '是',
							value: true
						},
						{
							text: '否',
							value: false
						}
					]
				}
			],
			searchparms: {},
			flag: false
		};
	},
	onLoad(options) {
		if (options.type === 'add') {
			this.id = '';
		} else {
			this.id = options.id;
		}
		this.warehouseId = options.warehouseId;
		this.getMachineList();
		this.editType = options.type || 'add';
		this.baseData();
	},
	// 触底刷新
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.getMachineList(); // 商品列表
	},
	methods: {
		previewImage(url) {
			uni.previewImage({
				urls: [url]
			});
		},
		search(val) {
			this.searchparms = val;
			this.pageNumber = 1;
			this.machineList = [];
			this.getMachineList();
		},
		handleModelCancel() {
			this.showModel = false;
			this.$nextTick(() => {
				this.tempProductInfo = {
					name: '',
					productId: ''
				};
				this.productList = [];
				this.showProduct = false;
				this.showModel = false;
				this.selectedSparePart = null;
			});
		},
		getMachineList(val) {
			if (val) {
				this.pageNumber = 1;
				this.machineList = [];
			}
			this.isloading = true;
			const params = {
				pageNumber: this.pageNumber,
				pageSize: 10,
				takeStockId: this.id || '',
				warehouseId: this.warehouseId,
				stockType: 0,
				...this.searchparms
			};
			getInventoryDetailApi(params)
				.then((res) => {
					const data = res.data.rows.map((item) => {
						return transformFormParams(item);
					});
					this.machineList = [...this.machineList, ...data];
					this.pageNumber++;
					this.total = parseInt(res.data.total);
				})
				.finally(() => {
					this.isloading = false;
				});
		},
		// 暂存
		handleStash() {
			if (!this.warehouseId) {
				this.$mHelper.toast('请选择仓库');
				return;
			}
			this.isloading = true;
			const params = {
				stockType: 0,
				id: this.id,
				warehouseId: this.warehouseId,
				takeDetails: this.machineList
			};
			inventorySaveDetailApi(params)
				.then((res) => {
					this.$mHelper.toast('暂存成功');
					this.id = res.data;
					this.getMachineList(true);
				})
				.finally(() => {
					this.isloading = false;
				});
		},
		// 提交盘点单
		submitInventory() {
			if (!this.id) {
				uni.showToast({
					title: '请先保存数据',
					icon: 'none'
				});
				return;
			}
			if (this.flag) {
				this.$mHelper.toast('请勿重复提交');
				return;
			}
			this.flag = true;
			const params = {
				stockType: 0,
				warehouseId: this.warehouseId,
				id: this.id,
				takeDetails: this.machineList
			};
			uni.showModal({
				title: '提示',
				content: '确定提交盘点单吗？',
				success: (res) => {
					if (res.confirm) {
						machineInventorySubmitApi(params).then((res) => {
							uni.showToast({
								title: '提交成功',
								icon: 'none'
							});
							this.flag = false;
							setTimeout(() => {
								uni.navigateBack();
							}, 300);
						});
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				},
				complete: () => {
					this.flag = false;
				}
			});
		},
		// 单行保存
		handleRowSave(item) {
			if (item.stockNum === undefined || item.stockNum === null) {
				uni.showToast({
					title: '请选择是否在库',
					icon: 'none'
				});
				return;
			}
			this.isloading = true;
			const params = {
				...item,
				takeStockId: this.id
			};
			inventorySaveApi(params)
				.then((res) => {
					this.id = res.data;
					uni.showToast({
						title: '保存成功',
						icon: 'none'
					});
					this.getMachineList();
				})
				.finally(() => {
					this.isloading = false;
				});
		},
		async baseData() {
			const dictApis = [dictTreeByCodeApi(2200)];
			try {
				const [manufacturerChannelRangeRes] = await Promise.all(dictApis);
				this.manufacturerChannelRange = manufacturerChannelRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.isloading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20rpx;
	.wrapper {
		// padding-bottom: 100rpx;
		margin-top: 20rpx;
	}
	.list {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		.list-header {
			display: flex;
			justify-content: space-between;

			.header-info {
				flex: 1;
				.info-row {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

					text {
						width: 160rpx;
						color: #666;
					}
					view {
						flex: 1;
						overflow-wrap: anywhere;
					}
				}
			}

			.thumb {
				width: 150rpx;
				height: 150rpx;
				flex-shrink: 0;
				border-radius: 8rpx;
				overflow: hidden;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;

				image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}
		.list-item {
			flex: 1;
			.info-row {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				text {
					width: 180rpx;
					color: #666;
				}

				view {
					flex: 1;
					overflow-wrap: anywhere;
					input {
						width: 100%;
						height: 60rpx;
						border: 1px solid #eee;
						border-radius: 6rpx;
						padding: 0 20rpx;
					}
					/deep/ .uni-select {
						border: 1px solid #e5e5e5 !important;

						.uni-select__input-box {
							padding: 0 20rpx;
						}
					}
				}
				.select-btn {
					padding: 12rpx 30rpx;
					border-radius: 6rpx;
					margin-left: 40rpx;
					font-size: 28rpx;
					background-color: #2979ff;
					color: #fff;
					flex: none;
				}
				.info-item {
					flex: 1;
					display: flex;
					align-items: center; // flex-direction: column;

					&.full-width {
						flex: 0 0 100%;
					}

					// .label {
					// 	font-size: 24rpx;
					// 	color: #909399;
					// 	margin-bottom: 4rpx;
					// }

					// .value {
					// 	font-size: 28rpx;
					// 	color: #333;
					// }
				}
			}
		}

		.action-row {
			display: flex;
			justify-content: flex-end;
			margin-top: 30rpx;

			view {
				padding: 12rpx 30rpx;
				border-radius: 6rpx;
				margin-left: 20rpx;
				font-size: 28rpx;

				&:first-child {
					background-color: #f5f5f5;
					color: #666;
				}

				&:last-child {
					background-color: #2979ff;
					color: #fff;
				}
			}
		}
	}
}
.slot-content {
	width: 100%;
	height: 250rpx;
	.container {
		display: flex;
		flex: 1;
		flex-direction: row;
		justify-content: start;
		align-items: baseline;
		padding: 0 !important;
		.title {
			padding: 0 10rpx;
		}
	}
	.machin-content {
		// width: 80%;
		flex: 1;
		display: flex;
		align-items: center;
		padding: 0 22rpx !important;
		border: 1px solid #e5e5e5 !important;
		box-sizing: border-box;
		position: relative;
		// max-height: 40rpx;
		height: 60rpx;

		.pd_inputs {
			border: none !important;
			flex: 1 !important;
		}

		.close_icon {
			z-index: 10000;
			width: 60rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.socrll-check {
			width: 100%;
			position: absolute;
			top: 90rpx;
			left: 0;
			z-index: 3;

			.uni-select__selector-scroll {
				width: 100%;
				height: 150px;
				padding: 4px 22rpx !important;
				box-sizing: border-box;
				background-color: #ffffff;
				border: 1px solid #ebeef5;
				border-radius: 6px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

				.socrll-list {
					height: 150px;
					overflow: hidden;
					overflow-y: scroll;
				}

				.socrll-item {
					display: flex;
					cursor: pointer;
					line-height: 35px;
					font-size: 14px;
					text-align: center;
				}
			}

			.no-socrll-list {
				height: 45px;
				padding: 4px 22rpx !important;
				box-sizing: border-box;
				background-color: #ffffff;
				border: 1px solid #ebeef5;
				border-radius: 6px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
				overflow: hidden;
				overflow-y: scroll;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				color: #999;
			}

			.socrll-popper__arrow {
				filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
				position: absolute;
				top: -4px;
				left: 23%;
				margin-right: 3px;
				border-top-width: 0;
				border: 1px solid #ebeef5;
				border-bottom-color: #ffffff;
				border-right-color: #ffffff;
				width: 18rpx;
				height: 18rpx;
				background-color: #ffffff;
				transform: rotate(45deg);
			}
		}
	}
}
.footer {
	width: 100%;
	padding: 18rpx 24rpx 42rpx;
	display: flex;
	justify-content: space-between;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 100;

	.confirm-btn {
		width: 48%;
		margin: 0;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 40rpx;
		font-size: 28rpx;

		&:first-child {
			background: #fff;
			color: #666;
			border: 1px solid #ddd;
		}

		&:last-child {
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			color: #fff;
		}
	}
}
// .footer {
// 	width: 100%;
// 	padding: 18rpx 0 42rpx;
// 	display: flex;
// 	justify-content: center;
// 	background-color: #fff;
// 	position: fixed;
// 	bottom: 0;
// 	left: 0;
// 	z-index: 100;

// 	.confirm-btn {
// 		width: 80%;
// 		margin: 0;
// 		display: flex;
// 		justify-content: center;
// 		align-items: center;
// 		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
// 		border-radius: 50px;
// 		color: #fff;
// 	}
// }
.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>

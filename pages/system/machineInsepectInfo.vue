<template>
	<view class="main">
		<view class="box1">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view>
		<view style="text-align: center; margin: 10rpx 0; font-size: 26rpx">第{{ pageNumber }}页数据</view>
		<view class="wrapper">
			<view class="list" v-for="item in machineList" :key="item.articleCode">
				<view class="list-item">
					<view class="info-row">
						<text>机器编号：</text>
						<view>{{ item.articleCode }}</view>
					</view>
					<view class="info-row">
						<text>原机器编号：</text>
						<view>
							<input type="text" v-model="item.originCode" :disabled="editType === 'info'" />
						</view>
					</view>
					<view class="info-row">
						<text>主机类型：</text>
						<view>
							<uni-data-select :localdata="hostTypeRange" v-model="item.hostType" placeholder="请选择主机类型" :disabled="editType === 'info'"></uni-data-select>
						</view>
					</view>
					<view class="info-row">
						<text>机型/选配件：</text>
						<view class="info-item">
							{{ item.articleName || '' }}
							<view class="select-btn" @click="selectArticle(item)" v-if="editType !== 'info'">修改</view>
						</view>
					</view>
					<view class="info-row">
						<text>设备新旧</text>
						<view>
							<uni-data-select :localdata="deviceOnRange" v-model="item.deviceOn" placeholder="请选择设备新旧" :disabled="editType === 'info'"></uni-data-select>
						</view>
					</view>
					<view class="info-row">
						<text>设备状态</text>
						<view>
							<uni-data-select :localdata="deviceStatusRange" v-model="item.deviceStatus" placeholder="请选择设备状态" :disabled="editType === 'info'"></uni-data-select>
						</view>
					</view>
					<view class="info-row">
						<text>储位：</text>
						<view>
							<input type="text" v-model="item.location" :disabled="editType === 'info'" />
						</view>
					</view>
					<view class="info-row">
						<text>是否在库：</text>
						<view>
							<uni-data-select :localdata="normalRange" v-model="item.stockNum" placeholder="请选择是否在库" :disabled="editType === 'info'"></uni-data-select>
						</view>
					</view>
				</view>
				<view v-if="editType !== 'info'" class="info-row action-row">
					<!-- <view class="">编辑详情</view> -->
					<view class="" @click="handleRowSave(item)">保存</view>
				</view>
			</view>
		</view>
		<view class="loading" v-if="!isEnloading && machineList.length">
			{{ isloading ? '加载中...' : machineList.length < total ? '下拉加载更多' : '' }}
		</view>
		<view class="loading" v-if="machineList.length > 0 && machineList.length == total && isEnloading">—— 到底啦 ——</view>
		<rf-empty :info="'暂无盘点数据'" v-if="machineList.length === 0"></rf-empty>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="isloading"></rfLoading>
		<view v-if="editType !== 'info'" class="footer">
			<button class="confirm-btn" @click="submitInventory">确认提交</button>
		</view>

		<!-- 选择机型或选配件 -->
		<u-modal :show="showModel" showCancelButton closeOnClickOverlay @close="handleModelCancel" @cancel="handleModelCancel" @confirm="handleConfirm()">
			<view class="slot-content">
				<view v-if="hostType === '2008'" class="container">
					<text class="title">机型：</text>
					<view class="machin-content">
						<input class="pd_inputs" type="text" v-model="tempProductInfo.name" @input="productInput" placeholder="请输入（如: C7780,V80,7502,C1070）" />
						<uni-icons v-if="tempProductInfo.name" type="close" size="17" class="close_icon" color="#999" @click.stop="productClear"></uni-icons>
						<uni-icons :type="showProduct ? 'arrowup' : 'arrowdown'" class="pd_icon" color="#999" size="14"></uni-icons>
						<view class="socrll-check" v-if="showProduct">
							<view class="socrll-popper__arrow"></view>
							<scroll-view class="uni-select__selector-scroll" v-if="productList.length > 0">
								<view class="socrll-list">
									<view class="socrll-item" v-for="(machine, index) in productList" :key="index" @click="sureCheckFn(machine)">{{ machine.brand }}/{{ machine.name }}</view>
								</view>
							</scroll-view>
							<view class="no-socrll-list" v-else>暂无该机型</view>
						</view>
					</view>
				</view>
				<view v-else class="container">
					<text class="title">选配件型号：</text>
					<view class="machin-content">
						<input class="pd_inputs" type="text" v-model="tempProductInfo.name" @input="spareInput" placeholder="请输入（如: FS-541,IC-319）" />
						<uni-icons v-if="tempProductInfo.name" type="close" size="17" class="close_icon" color="#999" @click.stop="productClear"></uni-icons>
						<uni-icons :type="showProduct ? 'arrowup' : 'arrowdown'" class="pd_icon" color="#999" size="14"></uni-icons>
						<view class="socrll-check" v-if="showProduct">
							<view class="socrll-popper__arrow"></view>
							<scroll-view class="uni-select__selector-scroll" v-if="productList.length > 0">
								<view class="socrll-list">
									<view class="socrll-item" v-for="(item, index) in productList" :key="index" @click="sureSpareCheckFn(item)">
										{{ item.modeType }}
									</view>
								</view>
							</scroll-view>
							<view class="no-socrll-list" v-else>暂无该选配件</view>
						</view>
					</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
import FormQuery from '@/components/formQuery/index';
import { dictTreeByCodeApi } from '@/api/custom';
import { getInventoryMachineListApi, inventorySaveApi, machineInventorySubmitApi } from '@/api/system.js';
import { transformFormParams } from '@/utils';
import { brandModelList, itemStoreList } from '@/api/index';
import { logout } from '../../api/login';
export default {
	components: {
		FormQuery
	},
	data() {
		return {
			id: '',
			machineList: [],
			isloading: false,
			pageNumber: 1,
			pageSize: 10,
			total: 0,
			editType: 'add',
			isEnloading: false,
			normalRange: [
				{
					text: '是',
					value: 1
				},
				{
					text: '否',
					value: 0
				}
			],
			deviceOnRange: [],
			deviceStatusRange: [],
			hostTypeRange: [],
			showModel: false,
			hostType: '',
			selectedSparePart: null, // 操作行
			productList: [],
			showProduct: false,
			tempProductInfo: {
				name: '',
				productId: ''
			},
			formColumns: [
				// {
				// 	dataIndex: 'product',
				// 	title: '机型型号',
				// 	valueType: 'machine'
				// },
				{
					dataIndex: 'articleCode',
					title: '机器编号',
					valueType: 'input'
				},
				{
					dataIndex: 'originCode',
					title: '原机器编号',
					valueType: 'input'
				},
				{
					dataIndex: 'location',
					title: '储位',
					valueType: 'input'
				},
				{
					dataIndex: 'isTake',
					title: '是否盘点',
					valueType: 'select',
					localdata: [
						{
							text: '是',
							value: true
						},
						{
							text: '否',
							value: false
						}
					]
				}
			],
			searchparms: {},
			flag: false,
			scrollTop: 0
		};
	},
	onLoad(options) {
		if (options.type === 'add') {
			this.id = '';
			this.getMachineList();
		} else {
			this.id = options.id;
			this.getMachineList();
		}
		this.editType = options.type || 'add';
		this.baseData();
	},
	// 触底刷新
	onReachBottom() {
		if (this.pageNumber * 10 >= this.total) {
			this.isEnloading = true;
			this.$mHelper.toast('已经到底了');
			return;
		}
		this.pageNumber++;
		this.getMachineList().then(() => {
			// 数据加载完成后，将页面滚动到指定位置
			uni.pageScrollTo({
				scrollTop: 200,
				duration: 300 // 滚动动画时长，单位ms
			});
		});
	},
	onPageScroll(e) {
		// 记录当前滚动位置
		this.scrollTop = e.scrollTop;

		// 当滚动到顶部时（scrollTop为0或接近0）自动触发下拉刷新
		if (this.scrollTop <= 0 && this.total > 10) {
			uni.startPullDownRefresh();
		}
	},
	onPullDownRefresh() {
		// 如果正在加载中，则不执行
		if (this.isloading) {
			uni.stopPullDownRefresh();
			return;
		}

		if (this.pageNumber <= 1) {
			// uni.showToast({
			//   title: "已经是第一页了",
			//   icon: "none",

			// });
			this.$mHelper.toast('已经是第一页了');
			this.pageNumber = 1;
			uni.stopPullDownRefresh();
			return;
		}

		this.pageNumber--;
		this.getMachineList();
		// setTimeout(() => {
		uni.stopPullDownRefresh();
		// }, 1000);
	},
	methods: {
		search(val) {
			this.searchparms = val;
			this.pageNumber = 1;
			this.machineList = [];
			this.isEnloading = false;
			this.getMachineList();
		},
		// 选择机型或选配件型号
		selectArticle(item) {
			if (!item.hostType) {
				this.$mHelper.toast('请先选择主机类型');
			}
			this.selectedSparePart = item;
			this.hostType = item.hostType;
			this.showModel = true;
		},
		handleModelCancel() {
			this.showModel = false;
			this.$nextTick(() => {
				this.tempProductInfo = {
					name: '',
					productId: ''
				};
				this.productList = [];
				this.showProduct = false;
				this.showModel = false;
				this.selectedSparePart = null;
			});
		},
		// 确定选择
		handleConfirm() {
			if (!this.tempProductInfo.name) {
				this.$mHelper.toast('请选择机型/选配件');
				return;
			}
			this.selectedSparePart.articleName = this.tempProductInfo.name;
			this.selectedSparePart.productId = this.tempProductInfo.productId;
			this.showModel = false;
			this.$nextTick(() => {
				this.tempProductInfo = {
					name: '',
					productId: ''
				};
				this.productList = [];
				this.showProduct = false;
				this.showModel = false;
				this.selectedSparePart = null;
			});
		},
		getMachineList() {
			this.isloading = true;
			const params = {
				pageNumber: this.pageNumber,
				pageSize: 10,
				takeStockId: this.id || '',
				warehouseId: 0,
				stockType: 1,
				...this.searchparms
			};
			return getInventoryMachineListApi(params)
				.then((res) => {
					const data = res.data.rows.map((item) => {
						return transformFormParams(item);
					});
					this.machineList = data;
					// this.$mHelper.toast(`第${this.pageNumber}页机器数据`);
					this.total = parseInt(res.data.total);
				})
				.finally(() => {
					this.isloading = false;
				});
		},
		// 提交盘点单
		submitInventory() {
			if (!this.id) {
				uni.showToast({
					title: '请先保存数据',
					icon: 'none'
				});
				return;
			}
			if (this.flag) {
				this.$mHelper.toast('请勿重复提交');
				return;
			}
			this.flag = true;
			const params = {
				stockType: 1,
				warehouseId: 0,
				id: this.id
			};
			uni.showModal({
				title: '提示',
				content: '确定提交盘点单吗？',
				success: (res) => {
					if (res.confirm) {
						machineInventorySubmitApi(params).then((res) => {
							uni.showToast({
								title: '提交成功',
								icon: 'none'
							});
							this.flag = false;
							setTimeout(() => {
								uni.navigateBack();
							}, 300);
						});
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				},
				complete: () => {
					this.flag = false;
				}
			});
		},
		// 单行保存
		handleRowSave(item) {
			if (item.stockNum === undefined || item.stockNum === null) {
				uni.showToast({
					title: '请选择是否在库',
					icon: 'none'
				});
				return;
			}
			this.isloading = true;
			const params = {
				...item,
				takeStockId: this.id
			};
			inventorySaveApi(params)
				.then((res) => {
					this.id = res.data;
					uni.showToast({
						title: '保存成功',
						icon: 'none'
					});
					this.getMachineList();
				})
				.finally(() => {
					this.isloading = false;
				});
		},
		async baseData() {
			const dictApis = [dictTreeByCodeApi(1100), dictTreeByCodeApi(6600), dictTreeByCodeApi(2000)];
			try {
				const [deviceOnRangeRes, deviceStatusRangeRes, hostTypeRangeRes] = await Promise.all(dictApis);
				this.deviceOnRange = deviceOnRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));

				this.deviceStatusRange = deviceStatusRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));

				this.hostTypeRange = hostTypeRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.isloading = false;
			}
		},
		sureCheckFn(item) {
			this.tempProductInfo.name = item.name;
			this.tempProductInfo.productId = item.id;
			this.showProduct = false;
		},
		sureSpareCheckFn(item) {
			this.tempProductInfo.name = item.modeType;
			this.tempProductInfo.productId = item.id;
			this.showProduct = false;
		},
		spareInput(val) {
			this.productList = [];
			if (!val.detail.value) {
				this.showProduct = false;
				return;
			}
			if (val.detail.value.length < 2) {
				this.showProduct = false;
				uni.showToast({
					title: '最少输入2位字符',
					icon: 'none'
				});
				return;
			}

			this.$http
				.post(itemStoreList, {
					modeType: this.tempProductInfo.name,
					pageNumber: 1,
					pageSize: 10000,
					type: this.hostType || ''
				})
				.then((res) => {
					this.productList = res.data;
				});
			this.showProduct = true;
		},
		productInput(val) {
			this.productList = [];
			if (!val.detail.value) {
				this.showProduct = false;
				return;
			}
			if (val.detail.value.length < 3) {
				this.showProduct = false;
				uni.showToast({
					title: '最少输入3位字符',
					icon: 'none'
				});
				return;
			}

			this.$http
				.get(brandModelList, {
					name: this.tempProductInfo.name,
					pageNumber: 1,
					pageSize: 10000
				})
				.then((res) => {
					this.productList = res.data.rows;
				});
			this.showProduct = true;
		},
		productClear() {
			uni.hideKeyboard();
			this.tempProductInfo.name = '';
			this.tempProductInfo.productId = '';
			this.showProduct = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20rpx;
	.wrapper {
		// padding-bottom: 100rpx;
		margin-top: 20rpx;
	}
	.list {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;

		.list-item {
			.info-row {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				text {
					width: 180rpx;
					color: #666;
				}

				view {
					flex: 1;

					input {
						width: 100%;
						height: 60rpx;
						border: 1px solid #eee;
						border-radius: 6rpx;
						padding: 0 20rpx;
					}
					/deep/ .uni-select {
						border: 1px solid #e5e5e5 !important;

						.uni-select__input-box {
							padding: 0 20rpx;
						}
					}
				}
				.select-btn {
					padding: 12rpx 30rpx;
					border-radius: 6rpx;
					margin-left: 40rpx;
					font-size: 28rpx;
					background-color: #2979ff;
					color: #fff;
					flex: none;
				}
				.info-item {
					flex: 1;
					display: flex;
					align-items: center; // flex-direction: column;

					&.full-width {
						flex: 0 0 100%;
					}

					// .label {
					// 	font-size: 24rpx;
					// 	color: #909399;
					// 	margin-bottom: 4rpx;
					// }

					// .value {
					// 	font-size: 28rpx;
					// 	color: #333;
					// }
				}
			}
		}

		.action-row {
			display: flex;
			justify-content: flex-end;
			margin-top: 30rpx;

			view {
				padding: 12rpx 30rpx;
				border-radius: 6rpx;
				margin-left: 20rpx;
				font-size: 28rpx;

				&:first-child {
					background-color: #f5f5f5;
					color: #666;
				}

				&:last-child {
					background-color: #2979ff;
					color: #fff;
				}
			}
		}
	}
}
.slot-content {
	width: 100%;
	height: 250rpx;
	.container {
		display: flex;
		flex: 1;
		flex-direction: row;
		justify-content: start;
		align-items: baseline;
		padding: 0 !important;
		.title {
			padding: 0 10rpx;
		}
	}
	.machin-content {
		// width: 80%;
		flex: 1;
		display: flex;
		align-items: center;
		padding: 0 22rpx !important;
		border: 1px solid #e5e5e5 !important;
		box-sizing: border-box;
		position: relative;
		// max-height: 40rpx;
		height: 60rpx;

		.pd_inputs {
			border: none !important;
			flex: 1 !important;
		}

		.close_icon {
			z-index: 10000;
			width: 60rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.socrll-check {
			width: 100%;
			position: absolute;
			top: 90rpx;
			left: 0;
			z-index: 3;

			.uni-select__selector-scroll {
				width: 100%;
				height: 150px;
				padding: 4px 22rpx !important;
				box-sizing: border-box;
				background-color: #ffffff;
				border: 1px solid #ebeef5;
				border-radius: 6px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

				.socrll-list {
					height: 150px;
					overflow: hidden;
					overflow-y: scroll;
				}

				.socrll-item {
					display: flex;
					cursor: pointer;
					line-height: 35px;
					font-size: 14px;
					text-align: center;
				}
			}

			.no-socrll-list {
				height: 45px;
				padding: 4px 22rpx !important;
				box-sizing: border-box;
				background-color: #ffffff;
				border: 1px solid #ebeef5;
				border-radius: 6px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
				overflow: hidden;
				overflow-y: scroll;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				color: #999;
			}

			.socrll-popper__arrow {
				filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
				position: absolute;
				top: -4px;
				left: 23%;
				margin-right: 3px;
				border-top-width: 0;
				border: 1px solid #ebeef5;
				border-bottom-color: #ffffff;
				border-right-color: #ffffff;
				width: 18rpx;
				height: 18rpx;
				background-color: #ffffff;
				transform: rotate(45deg);
			}
		}
	}
}
.footer {
	width: 100%;
	padding: 18rpx 0 42rpx;
	display: flex;
	justify-content: center;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 100;

	.confirm-btn {
		width: 80%;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 50px;
		color: #fff;
	}
}
.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>

<template>
	<view class="main">
		<view class="box1">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view>

		<view class="box2">
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="good in goodsList" :key="good.id">
					<view class="goods">
						<view class="thumb" @tap.stop="previewImage(good.picsUrl[0].url)">
							<image :src="good.picsUrl[0].url" mode=""></image>
						</view>
						<view class="item">
							<view class="title">
								<text class="two-omit">编号：{{ good.machineNum }}</text>
							</view>
							<view class="title">
								<text class="two-omit">原编号：{{ good.originCode || '' }}</text>
							</view>
							<!-- 						<view class="title">
								<text class="two-omit">品牌：{{ good.brand || '' }}</text>
							</view> -->
							<view class="title">
								<text class="two-omit">型号：{{ good.brand ? good.brand + '/' : '' }}{{ good.productName || '' }}</text>
							</view>
							<view class="title" v-if="good.percentage">
								<text class="two-omit">成色：{{ good.percentage.label || '' }}</text>
							</view>
							<view class="title" v-if="good.deviceOn">
								<text class="two-omit">设备新旧：{{ good.deviceOn.label || '' }}</text>
							</view>
							<view class="title" v-if="good.deviceStatus">
								<text class="two-omit">设备状态：{{ good.deviceStatus.label || '' }}</text>
							</view>
							<view class="title" v-if="good.location">
								<text class="two-omit">储位：{{ good.location || '' }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂未查询到机器哦'"></rf-empty>
			</view>
			<view class="loading" v-if="!isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total && isEnloading">—— 到底啦 ——</view>
		</view>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { dictTreeByCodeApi } from '@/api/custom.js';
import { brandSerialList, getMachineWarehouseList } from '@/api/index';
import FormQuery from '@/components/formQuery/index';
export default {
	components: {
		FormQuery
	},
	data() {
		return {
			// 是否到底
			isEnloading: false,
			// 分页参数
			pageNumber: 1,
			// 总数
			total: 0,
			// 是否加载完成
			isloading: true,
			searchparms: {},
			loading: true,
			goodsList: [],
			pagination: {
				pageNumber: 1,
				pageSize: 10,
				total: 0,
				status: ['INVENTORY']
			},
			formColumns: [
				{
					dataIndex: 'productId',
					title: '机器型号',
					valueType: 'machine'
				},
				{
					dataIndex: 'machineNum',
					title: '机器编号',
					valueType: 'input'
				},
				{
					dataIndex: 'originCode',
					title: '原机器编号',
					valueType: 'input'
				},
				{
					dataIndex: 'hostType',
					title: '主机类型',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'deviceStatus',
					title: '设备状态',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'deviceOn',
					title: '设备新旧',
					valueType: 'select',
					localdata: []
				}
			]
		};
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pagination.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.saveFn(); // 商品列表
	},
	onLoad() {
		this.getDataFn();
	},
	onShow() {
		this.initSearchData();
	},
	methods: {
		search(value) {
			this.searchparms = { ...value };
			delete this.searchparms.productId;
			if (this.searchparms.productTreeIdList) {
				this.searchparms.productIds = this.searchparms.productTreeIdList;
				delete this.searchparms.productTreeIdList;
			} else {
				delete this.searchparms.productTreeIdList;
			}
			this.saveFn(1);
		},
		getDataFn() {
			this.loading = true;
			this.saveFn();
		},
		async initSearchData() {
			const dictApis = [dictTreeByCodeApi(2000), dictTreeByCodeApi(1100), dictTreeByCodeApi(6600)];
			try {
				const [hostTypeRes, deviceOnRes, deviceStatusRes] = await Promise.all(dictApis);
				if (hostTypeRes.code !== 200 || deviceOnRes.code !== 200 || deviceStatusRes.code !== 200) {
					throw new Error('数据获取失败');
				}
				this.formColumns[3].localdata = hostTypeRes.data.map((item) => {
					return {
						value: item.value,
						text: item.label
					};
				});
				this.formColumns[4].localdata = deviceStatusRes.data.map((item) => {
					return {
						value: item.value,
						text: item.label
					};
				});
				this.formColumns[5].localdata = deviceOnRes.data.map((item) => {
					return {
						value: item.value,
						text: item.label
					};
				});
			} catch (e) {
				uni.showToast({
					title: e.message || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			}
		},
		previewImage(url) {
			uni.previewImage({
				urls: [url]
			});
		},
		// 查询
		async saveFn(type) {
			if (type) {
				this.goodsList = [];
				this.pagination.pageNumber = 1;
			}
			try {
				this.isloading = true;
				const result = await getMachineWarehouseList({ ...this.pagination, ...this.searchparms });
				if (result.code === 200) {
					this.pagination.pageNumber++;
					this.goodsList = [...this.goodsList, ...result.data.rows];
					this.total = parseInt(result.data.total);
				}
			} catch (e) {
				uni.showToast({
					title: e.message || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.isloading = false;
				this.loading = false;
			}
		}
	}
};
</script>

<style scoped lang="scss">
.page {
	background: #f5f6f8;
	min-height: 100vh;
}
.box2 {
	padding: 0 22rpx;
}

/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;

	.list {
		display: flex;
		// height: 185rpx;
		background-color: #ffffff;
		box-shadow: 0 0 20rpx #f6f6f6;
		border-radius: 10rpx;
		margin-bottom: 20rpx;

		.check {
			display: flex;
			align-items: center;
			width: 10%;
			height: 80%;
			margin-left: 20rpx;

			text {
				font-size: 36rpx;
				color: #333333;
			}
		}

		.goods {
			display: flex;
			align-items: center;
			width: 90%;
			height: 100%;

			.thumb {
				display: flex;
				justify-content: center;
				width: 30%;
				height: 100%;
				margin-top: 20rpx;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				padding: 10rpx 0;
				width: 70%;
				height: 100%;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: auto;

					text {
						font-size: 26rpx;
						color: #212121;
					}
				}

				.attribute {
					display: flex;
					align-items: center;
					margin-top: 10rpx;

					.attr {
						display: flex;
						align-items: center;
						padding: 0 20rpx;
						height: 40rpx;
						background-color: #f6f6f6;
						border-radius: 10rpx;

						text {
							font-size: 24rpx;
							color: #333333;
						}

						.more {
							display: flex;
							width: 10rpx;
							height: 10rpx;
							border-left: 2rpx solid #333333;
							border-bottom: 2rpx solid #333333;
							transform: rotate(-45deg);
							margin-left: 10rpx;
						}
					}
				}

				.price-num {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 80rpx;

					.price {
						display: flex;

						.min {
							color: #fe3b0f;
							font-size: 24rpx;
						}

						.max {
							font-size: 28rpx;
							color: #fe3b0f;
							font-weight: bold;
						}
					}

					.num {
						display: flex;
						height: 40rpx;
						margin-right: 20rpx;

						.add {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 60rpx;
							height: 40rpx;
							background-color: #ffffff;

							text {
								color: #212121;
								font-size: 24rpx;
							}
						}

						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 80rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 8rpx;
							text-align: center;

							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}
}

.background {
	background: #f5f6f8;
}

/deep/.uni-stat__select {
	width: 80%;
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>

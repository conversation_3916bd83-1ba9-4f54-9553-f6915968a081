<template>
	<view class="main">
		<view class="box1">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view>

		<view class="box2">
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="good in goodsList" :key="good.id">
					<view class="goods">
						<view class="thumb" @tap.stop="previewImage(getImageUrl(good))">
							<image :src="getImageUrl(good)" mode=""></image>
						</view>
						<view class="item">
							<view class="title">
								<text class="two-omit">物品名称：{{ good.name }}</text>
							</view>
							<view class="title">
								<text class="two-omit">物品编号：{{ good.code }}</text>
							</view>
							<view class="title">
								<text class="two-omit">OEM编号：{{ good.numberOem || '' }}</text>
							</view>
							<view class="title">
								<text class="two-omit">制造渠道：{{ (good.manufacturerChannel && good.manufacturerChannel.label) || '' }}</text>
							</view>
							<view class="title">
								<text class="two-omit">物品小类：{{ (good.type && good.type.label) || '' }}</text>
							</view>
							<view class="title">
								<text class="two-omit">品牌：{{ good.partBrand || '' }}</text>
							</view>

							<!-- 							<view class="title">
								<text class="two-omit">维修位置：</text>
								<input v-model="good.newLocation" placeholder="请输入新位置" style="border: 1px solid #f5f5f7; padding: 0 10rpx" />
							</view> -->
						</view>
					</view>
					<view class="action-btn" @click="confirmApply(good)">
						<view class="btn">领出维修</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂未查询到相关组件'"></rf-empty>
			</view>
			<view class="loading" v-if="!isEnloading && goodsList.length">
				{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}
			</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total && isEnloading">—— 到底啦 ——</view>
		</view>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from '@/api/custom';
import { brandSerialList, getMachineWarehouseList } from '@/api/index';
import { getRefurbishListApi, addRefurbishApi } from '@/api/system.js';
import FormQuery from '@/components/formQuery/index';
export default {
	components: {
		FormQuery
	},
	data() {
		return {
			// 是否到底
			isEnloading: false,
			// 总数
			total: 0,
			// 是否加载完成
			isloading: true,
			searchparms: {},
			loading: true,
			goodsList: [],
			pagination: {
				pageNumber: 1,
				pageSize: 10,
				total: 0
			},
			formColumns: [
				// {
				// 	dataIndex: 'lastIds',
				// 	title: '适用机型',
				// 	valueType: 'machine'
				// },
				{
					dataIndex: 'name',
					title: '物品名称',
					valueType: 'input',
					placeholder: '物品名称'
				},
				{
					dataIndex: 'code',
					title: '物品编号',
					valueType: 'input',
					placeholder: '物品编号'
				},
				{
					dataIndex: 'numberOem',
					title: 'OEM编号',
					valueType: 'input',
					placeholder: 'OEM编号'
				},
				{
					dataIndex: 'type',
					title: '物品小类',
					valueType: 'select',
					localdata: []
				}

				// {
				// 	dataIndex: 'manufacturerChannel',
				// 	title: '制造渠道',
				// 	valueType: 'select',
				// 	localdata: []
				// }
			]
		};
	},
	computed: {
		getImageUrl() {
			return (good) => {
				return good.imageFiles && good.imageFiles[0] ? good.imageFiles[0].url : '';
			};
		}
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pagination.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.saveFn(); // 商品列表
	},
	onLoad() {
		this.getDataFn();
	},
	onShow() {
		this.loading = true;
		this.saveFn();
	},
	methods: {
		confirmApply(item) {
			// if (!item.newLocation) {
			// 	this.$mHelper.toast('请先填写领出存放位置');
			// 	return;
			// }
			uni.showModal({
				title: '提示',
				content: '确定要将该组件领出维修吗？',
				success: (res) => {
					if (res.confirm) {
						this.loading = true;
						const args = {
							articleCode: item.code
						};
						addRefurbishApi(args)
							.then((res) => {
								uni.navigateBack({
									delta: 1
								});
							})
							.catch((err) => {
								this.$mHelper.toast(err.message || '系统出错啦，请稍后再试！');
							})
							.finally(() => {
								this.loading = false;
							});
					}
				}
			});
		},
		search(value) {
			this.searchparms = { ...value };
			// delete this.searchparms.productId;
			// if (this.searchparms.productTreeIdList) {
			// 	this.searchparms.productIds = this.searchparms.productTreeIdList;
			// 	delete this.searchparms.productTreeIdList;
			// } else {
			// 	delete this.searchparms.productTreeIdList;
			// }
			this.saveFn(1);
		},
		async getDataFn() {
			const dictApis = [dictTreeByCodeApi2(2100, 2101)];
			try {
				this.loading = true;
				const [typeRes] = await Promise.all(dictApis);
				this.formColumns[3].localdata = typeRes.data.map((item) => {
					return {
						text: item.label,
						value: item.value
					};
				});
				// this.formColumns[3].localdata = manufacturerChannelRes.data.map((item) => {
				// 	return {
				// 		text: item.label,
				// 		value: item.value
				// 	};
				// });
				// this.manufacturerChannelList = manufacturerChannelRes.data.map((item) => {
				// 	return {
				// 		text: item.label,
				// 		value: item.value
				// 	};
				// });
			} finally {
				this.loading = false;
			}
		},
		previewImage(url) {
			uni.previewImage({
				urls: [url]
			});
		},
		// 查询
		async saveFn(type) {
			if (type) {
				this.goodsList = [];
				this.pagination.pageNumber = 1;
			}
			try {
				this.isloading = true;
				const result = await getRefurbishListApi({
					...this.pagination,
					...this.searchparms
				});
				if (result.code === 200) {
					this.pagination.pageNumber++;
					this.goodsList = [...this.goodsList, ...result.data.rows];
					this.total = parseInt(result.data.total);
				}
			} catch (e) {
				uni.showToast({
					title: e.message || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.isloading = false;
				this.loading = false;
			}
		}
	}
};
</script>

<style scoped lang="scss">
.page {
	background: #f5f6f8;
	min-height: 100vh;
}
.box2 {
	padding: 0 22rpx;
}

/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;

	.list {
		display: flex;
		flex-direction: column;
		// height: 185rpx;
		background-color: #ffffff;
		box-shadow: 0 0 20rpx #f6f6f6;
		border-radius: 10rpx;
		margin-bottom: 20rpx;

		.goods {
			display: flex;
			align-items: center;
			width: 90%;
			height: 100%;

			.thumb {
				display: flex;
				justify-content: center;
				width: 30%;
				height: 100%;
				margin-top: 20rpx;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				padding: 10rpx 0;
				width: 70%;
				height: 100%;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: auto;

					text {
						font-size: 26rpx;
						color: #212121;
					}
					input {
						width: 50%;
					}
				}
			}
		}
		.action-btn {
			display: flex;
			justify-content: flex-end;
			align-items: center;
			padding: 0 20rpx;
			height: 100rpx;
			border-top: 1rpx solid #f5f5f5;
			.btn {
				width: 160rpx;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
				font-size: 26rpx;
				color: #fff;
				background: #ff6e2e;
				border-radius: 30rpx;
			}
		}
	}
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>

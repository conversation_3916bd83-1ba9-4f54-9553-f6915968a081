<template>
	<view class="detail-container">
		<!-- info -->
		<!-- 		<view class="detail-item">
			<view class="detail-item__row">
				<text class="label">退料单号</text>
				<text class="value">{{ params.code }}</text>
			</view>
			<view class="detail-item__row">
				<text class="label">退料状态</text>
				<text class="value">{{ params.status }}</text>
			</view>
		</view> -->
		<!-- 退料明细列表 -->
		<view class="detail-list">
			<view v-for="(item, index) in infoList" :key="index" class="detail-item">
				<view class="detail-item__row">
					<text class="label">物品编号：</text>
					<text class="value">
						{{ item.itemStore && item.itemStore.articleCode }}
					</text>
				</view>
				<view class="detail-item__row">
					<text class="label">物品名称：</text>
					<text class="value">{{ item.articleName }}</text>
				</view>
				<view class="detail-item__row">
					<text class="label">OEM编号：</text>
					<text class="value">
						{{ item.itemStore && item.itemStore.oemNumber }}
					</text>
				</view>
				<view class="detail-item__row">
					<text class="label">退料数量：</text>
					<text class="value">{{ item.num }}</text>
				</view>
				<view class="detail-item__row">
					<text class="label">入库数量：</text>
					<input
						v-if="editType === 'audit'"
						type="number"
						v-model="item.auditNum"
						class="quantity-input"
						@input="(e) => handleNumChange(e, item)"
						confirm-type="done"
						placeholder="请输入入库数量"
					/>
					<text v-else class="value">{{ item.auditNum }}</text>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view v-if="editType === 'audit'" class="footer">
			<button class="confirm-btn" @click="handleRejectAll">全部驳回</button>
			<button class="confirm-btn" @click="handleApprove">审核通过</button>
		</view>
		<view v-if="infoList && infoList.length === 0" style="padding-top: 200rpx">
			<rf-empty :info="'暂无审核内容'"></rf-empty>
		</view>
		<view class="loading" v-if="infoList.length > 0">—— 到底啦 ——</view>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { getReturnDetailApi, checkReturnApi } from '@/api/system.js';
export default {
	data() {
		return {
			id: '',
			infoList: [],
			params: {},
			editType: 'info',
			loading: true
		};
	},
	onLoad(options) {
		this.id = options.id;
		this.editType = options.status === 'CREATE' ? 'audit' : 'info';
		this.getDetailList();
	},
	methods: {
		async getDetailList() {
			try {
				const res = await getReturnDetailApi(this.id);
				if (res.code === 200) {
					this.infoList = res.data.applyReturnDetailList.map((i) => {
						return {
							...i,
							auditNum: this.editType === 'audit' ? i.num : i.auditNum
						};
					});
					this.params = res.data;
				}
			} catch (error) {
				uni.showToast({
					title: '获取数据失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		handleNumChange(e, item) {
			if (e.target.value > item.num) {
				uni.showToast({
					title: '入库数量不能大于退料数量',
					icon: 'none'
				});
				this.infoList.forEach((i) => {
					if (i.id === item.id) {
						i.auditNum = item.num;
					}
				});
			}
			if (e.target.value < 0) {
				uni.showToast({
					title: '入库数量不能小于0',
					icon: 'none'
				});
				this.infoList.forEach((i) => {
					if (i.id === item.id) {
						i.auditNum = item.num;
					}
				});
			}
		},
		handleRejectAll() {
			uni.showModal({
				title: '确认',
				content: '确定要驳回所有退料申请吗？',
				success: (res) => {
					if (res.confirm) {
						const args = {
							id: this.id,
							status: 'REJECT',
							applyReturnDetails: this.infoList
						};
						checkReturnApi(args).then(() => {
							uni.showToast({
								title: '驳回成功',
								icon: 'success'
							});
							uni.navigateBack();
						});
					}
				}
			});
		},

		handleApprove() {
			uni.showModal({
				title: '确认',
				content: '确定要通过此退料申请吗？',
				success: (res) => {
					if (res.confirm) {
						const isValid = this.infoList.every((item) => {
							return item.auditNum >= 0 && item.auditNum <= item.num;
						});
						if (!isValid) {
							uni.showToast({
								title: '入库数量不能大于退料数量',
								icon: 'none'
							});
							return;
						}
						const args = {
							id: this.id,
							status: 'WAIT_IN_WAREHOUSE',
							applyReturnDetails: this.infoList
						};
						checkReturnApi(args).then(() => {
							uni.showToast({
								title: '审核成功',
								icon: 'success'
							});
							uni.navigateBack();
						});
					}
				}
			});
		}
	}
};
</script>

<style lang="scss">
.detail-container {
	padding: 20rpx;
	// min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.detail-list {
	flex: 1;
}

.detail-item {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;

	&__row {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.label {
			width: 160rpx;
			color: #666;
			font-size: 28rpx;
		}

		.value {
			flex: 1;
			font-size: 28rpx;
		}
	}
}

.quantity-input {
	border: 1px solid #dcdfe6;
	border-radius: 4rpx;
	// padding: 4rpx 12rpx;
	width: 120rpx;
	text-align: center;
}
.footer {
	width: 100%;
	padding: 18rpx 0 42rpx;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	justify-content: center;
	gap: 40rpx;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;
	padding-left: 30rpx;
	padding-right: 30rpx;

	.confirm-btn {
		width: 100%;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 50px;
		color: #fff;
	}
}
.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>

<template>
	<view class="main">
		<view class="box2">
			<!-- 购物车列表 -->
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="good in goodsList" :key="good.id">
					<view class="goods-info">
						<view class="goods">
							<view class="first-row">
								<view class="thumb" @tap.stop="previewImage(good.picUrls[0].url)">
									<image :src="good.picUrls[0].url" mode=""></image>
								</view>
								<view class="first-row-info">
									<view class="info-row">
										<text class="label">拆机单号：</text>
										<view class="value">{{ good.code }}</view>
									</view>
									<view class="info-row">
										<text class="label">零件名称：</text>
										<view class="value">{{ good.partName }}</view>
									</view>
									<view class="info-row">
										<text class="label">OEM编号：</text>
										<view class="value">{{ good.numberOem }}</view>
									</view>
									<view class="info-row">
										<text class="label">机器编号：</text>
										<view class="value">{{ good.machineNum }}</view>
									</view>
									<view class="info-row">
										<text class="label">机型：</text>
										<view class="value">{{ good.productName }}</view>
									</view>
									<!-- 			<view class="info-row">
										<text class="label">制造渠道：</text>
										<view class="value">{{ good.manufacturerChannel.label }}</view>
									</view> -->
								</view>
								<view class="status" :class="{ green: good.status.value === 'PASS', red: good.status.value === 'REJECT', yellow: good.status.value === 'WAIT_APPROVE' }">
									{{ good.status.label }}
								</view>
							</view>

							<view class="item">
								<view class="info-row">
									<text class="label">数量：</text>
									<view class="value">
										{{ good.quantity }}
									</view>
								</view>
								<view class="info-row">
									<text class="label">拆机时间：</text>
									<view class="value">
										{{ good.createdAt }}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂无申请记录'"></rf-empty>
			</view>
			<view class="loading" v-if="!isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total && isEnloading">—— 到底啦 ——</view>
		</view>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="isloading"></rfLoading>
	</view>
</template>

<script>
import { getDisassemblyListApi } from '@/api/system.js';
export default {
	components: {},
	data() {
		return {
			isEnloading: false,
			pageNumber: 1,
			total: 0,
			isloading: true,
			loading: true,
			goodsList: []
		};
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.saveFn(); // 商品列表
	},
	onLoad() {
		this.saveFn(1);
	},
	methods: {
		// 查询
		async saveFn(type) {
			if (type) {
				this.goodsList = [];
				this.pageNumber = 1;
			}
			try {
				this.isloading = true;
				let verify = await getDisassemblyListApi({
					pageNumber: this.pageNumber,
					pageSize: 10,
					engineerId: uni.getStorageSync('userInfo').id || ''
				});
				if (verify.code === 200) {
					this.pageNumber++;

					this.total = +verify.data.total;
					this.goodsList = [...this.goodsList, ...verify.data.rows];
				}
			} catch (e) {
				this.$mHelper.toast(err || err.message || '系统出错啦，请稍后再试');
			} finally {
				this.isloading = false;
			}
		},
		previewImage(url) {
			uni.previewImage({
				urls: [url]
			});
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	width: 100vw;
	// height: 100vh;
	padding: 0 22rpx;
	box-sizing: border-box;
	// background-color: #fff;
}
.page {
	background: #f5f6f8;
	min-height: 100vh;
}

/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;

	.list {
		margin: 20rpx 0;
		.goods-info {
			display: flex;
			// Increase height to accommodate more content
			min-height: 300rpx;
			height: auto;
			background-color: #ffffff;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
			border-radius: 16rpx;

			.check {
				display: flex;
				align-items: flex-start;
				width: 80rpx;
				padding-top: 10rpx;
				margin-left: 20rpx;

				text {
					font-size: 36rpx;
					color: #333333;
				}
			}

			.goods {
				flex: 1;
				padding: 20rpx;
				.first-row {
					display: flex;
					margin-bottom: 20rpx;

					.thumb {
						margin-right: 20rpx;
						image {
							width: 160rpx;
							height: 160rpx;
							border-radius: 10rpx;
						}
					}

					.first-row-info {
						flex: 1;
						display: flex;
						flex-direction: column;
						.info-row {
							width: 100%;
							margin-bottom: 10rpx;
							display: flex;
							.label {
								min-width: 140rpx;
								font-size: 26rpx;
								color: #666;
							}

							.value {
								flex: 1;
								font-size: 26rpx;
								color: #333;
							}
						}
					}
					.status {
						&.green {
							// background: rgba(30, 192, 102, 0.2);
							color: #67c23a;
						}

						&.yellow {
							// background: rgba(255, 168, 0, 0.2);
							color: #ffa800;
						}
						&.red {
							// background: rgba(255, 0, 0, 0.2);
							color: #f56c6c;
						}
					}
				}

				.item {
					width: 100%;

					.info-row {
						display: flex;
						margin-bottom: 16rpx;
						align-items: center;
						.label {
							min-width: 140rpx;
							font-size: 26rpx;
							color: #666;
						}

						.value {
							flex: 1;
							font-size: 26rpx;
							color: #333;
						}

						.action {
							display: flex;
							align-items: center;
							.number {
								width: 100rpx;
								height: 40rpx;
								text-align: center;
								background: #f6f6f6;
								border-radius: 8rpx;
								margin: 0 20rpx;
							}
						}

						textarea {
							width: 100%;
							height: 120rpx;
							background: #f8f8f8;
							border-radius: 8rpx;
							padding: 12rpx;
							font-size: 26rpx;
						}
						.action-btn {
							margin-left: auto;
							padding: 10rpx 20rpx;
							background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
							border-radius: 60rpx;
							font-size: 24rpx;
							color: #fff;
							margin-top: 10rpx;
						}
					}
				}
			}
		}
	}
}

/* 结算 */
.close-account {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 130rpx;
	background-color: #ffffff;
	border-top: 2rpx solid #f6f6f6;
	z-index: 10;

	.account {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-right: 4%;
		margin-top: 20rpx;

		.btn-calculate {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			margin: 0 30rpx;
			height: 60rpx;
			background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
			border-radius: 60rpx;

			text {
				color: #ffffff;
				font-size: 24rpx;
			}
		}
	}
}

.icon-subtract {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.icon-add {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}

	&::before {
		content: '';
		display: block;
		width: 5rpx;
		height: 25rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.check-icon {
	width: 35rpx;
	height: 35rpx;
	border: 1px solid #999999;
	border-radius: 50%;

	&.active {
		border: 1px solid #e5452f;
		color: #e5452f !important;
		line-height: 30rpx;
	}
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>

<template>
	<view class="container">
		<view class="reback-list">
			<view v-for="(item, index) in infoList" :key="index" class="reback-item" @click="createOrder(item.id, 'info')">
				<view class="reback-item__header">
					<text class="order-no">发货单号：{{ item.invoiceCode }}</text>
					<text :class="['status', getStatusClass(item.status && item.status.value)]">
						{{ item.status && item.status.label }}
					</text>
				</view>
				<view class="reback-item__info">
					<text>物流单号：{{ item.logisticsWaybillNumber || '' }}</text>
				</view>
				<view class="reback-item__info">
					<text>收货人姓名：{{ (item.receiver && item.receiver.name) || '' }}</text>
					<text>电话：{{ (item.receiver && item.receiver.mobile) || '' }}</text>
				</view>
				<view class="reback-item__info">
					<text>创建时间：{{ item.createdAt || '' }}</text>
					<view v-if="item.status.value === 'WAIT_RECEIVE' || item.status.value === 'RECEIVING'" class="audit-btn audit">确认收货</view>
					<view v-else class="audit-btn">详情</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" @click="createOrder(false, 'add')">新建发货单</button>
		</view>
		<view class="loading" v-if="!isEnloading && infoList.length">
			{{ loading ? '加载中...' : infoList.length < total ? '上拉加载更多' : '' }}
		</view>
		<view class="loading" v-if="infoList.length > 0 && infoList.length == total && isEnloading">—— 到底啦 ——</view>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { getDeliveryListApi } from '@/api/system.js';
export default {
	data() {
		return {
			infoList: [],
			isEnloading: false,
			loading: true,
			pageNumber: 1,
			total: 0,
			isFirst: true
		};
	},
	onLoad() {},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.getRebackList();
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.getRebackList(true);
		uni.stopPullDownRefresh();
	},
	onShow() {
		this.getRebackList();
	},
	methods: {
		async getRebackList(val) {
			if (val) {
				this.pageNumber = 1;
				this.infoList = [];
			}
			try {
				this.loading = true;
				const args = {
					pageNumber: this.pageNumber,
					pageSize: 10
				};
				const result = await getDeliveryListApi(args);
				if (result.code === 200) {
					this.infoList = [...this.infoList, ...result.data.rows];
					this.pageNumber++;
					this.total = +result.data.total;
				}
			} catch (error) {
				uni.showToast({
					title: '系统出错啦，请稍后再试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 获取状态样式类
		getStatusClass(status) {
			const statusMap = {
				pending: 'status-pending',
				shipped: 'status-approved',
				canceled: 'status-rejected'
			};
			return statusMap[status] || '';
		},

		// 跳转到详情页
		createOrder(id, type) {
			const ids = id || '';
			uni.navigateTo({
				url: `/pages/engineer/sendOutDetail?id=${ids}&editType=${type}`
			});
		}
	}
};
</script>

<style lang="scss">
.container {
	padding: 20rpx;

	.reback-list {
		width: 100%;
	}
}

.reback-item {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

	&__header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;

		.order-no {
			font-weight: bold;
		}
	}

	&__info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 12rpx;
		color: #666;
		font-size: 28rpx;
	}
}

.status {
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
	font-size: 24rpx;

	&-pending {
		// background-color: #f37b1d;
		color: #f37b1d;
	}

	&-approved {
		// background-color: #f0f9eb;
		color: #67c23a;
	}

	&-rejected {
		// background-color: #f4f4f5;
		color: #f56c6c;
	}
	&-in {
		// background-color: #409eff;
		color: #409eff;
	}
}

.audit-btn {
	background-color: #409eff;
	color: #fff;
	font-size: 24rpx;
	padding: 6rpx 20rpx;
	border-radius: 6rpx;

	&:active {
		opacity: 0.8;
	}
}
.audit {
	background-color: #f37b1d;
}
.footer {
	width: 100%;
	padding: 18rpx 0 42rpx;
	display: flex;
	justify-content: center;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;

	.confirm-btn {
		width: 80%;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 50px;
		color: #fff;
	}
}
.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>

<template>
  <view class="container">
    <view class="tab-nav">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-item', { active: currentTab === index }]"
        @click="switchTab(index)">
        {{ tab }}
      </view>
    </view>
    <view class="reback-list">
      <view
        v-for="(item, index) in infoList"
        :key="index"
        class="reback-item"
        @click="goToDetail(item.id, item.status)">
        <view class="reback-item__header">
          <text class="order-no">报销单号：{{ item.code }}</text>
          <text
            :class="[
              'status',
              getStatusClass(item.status && item.status.value),
            ]">
            {{ item.status && item.status.label }}
          </text>
        </view>
        <view class="reback-item__info">
          <text>申请时间：{{ item.createdAt || "" }}</text>
        </view>
        <view class="reback-item__info">
          <text>审核时间：{{ item.auditAt || "" }}</text>
        </view>
        <view class="reback-item__info">
          <text>
            申请金额：
            <text style="color: #f37b1d">
              ￥{{ item.applyAmount || "0.00" }}
            </text>
          </text>
        </view>
        <view class="reback-item__info">
          <text>
            报销金额：
            <text style="color: #f37b1d">
              ￥{{ item.actualAmount || "0.00" }}
            </text>
          </text>
        </view>
        <view v-if="item.remark" class="reback-item__info">
          <text>
            审核备注：
            <text>
              {{ item.remark || "" }}
            </text>
          </text>
        </view>
        <view class="reback-item__info">
          <view
            v-if="
              item.status.value === 'WAIT_APPROVE' ||
              item.status.value === 'REJECT'
            "
            class="audit-btn audit"
            @click.stop="handleCancel(item.id)">
            撤销
          </view>
        </view>
      </view>
    </view>
    <view class="footer">
      <button class="confirm-btn" @click="createBill">申请报销</button>
    </view>
    <view class="loading" v-if="!isEnloading && infoList.length">
      {{
        loading ? "加载中..." : infoList.length < total ? "上拉加载更多" : ""
      }}
    </view>
    <view
      class="loading"
      v-if="infoList.length > 0 && infoList.length == total && isEnloading">
      —— 到底啦 ——
    </view>
    <view v-if="infoList.length === 0" style="margin-top: 200rpx">
      <rf-empty :info="'暂无报账单'"></rf-empty>
    </view>
    <!-- 页面加载-->
    <rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
  </view>
</template>

<script>
import {
  getReimbursementListApi,
  cancelReimbursementApi,
} from "@/api/system.js";
export default {
  data() {
    return {
      infoList: [],
      isEnloading: false,
      loading: false,
      pageNumber: 1,
      total: 0,
      isFirst: true,
      tabs: ["全部", "申请中", "已报销"],
      currentTab: 0,
    };
  },
  onLoad() {},
  // 滚动到底部
  onReachBottom() {
    if ((this.pageNumber - 1) * 10 >= this.total) {
      this.isEnloading = true;
      return;
    }
    this.getRebackList();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.getRebackList(true);
    uni.stopPullDownRefresh();
  },
  onShow() {
    this.getRebackList(true);
  },
  methods: {
    async getRebackList(val) {
      if (val) {
        this.pageNumber = 1;
        this.infoList = [];
      }
      try {
        this.loading = true;
        const args = {
          pageNumber: this.pageNumber,
          pageSize: 10,
          status:
            this.currentTab === 0
              ? null
              : this.currentTab === 1
              ? "WAIT_APPROVE"
              : "PASS",
          userId: uni.getStorageSync("userInfo").id,
        };
        const result = await getReimbursementListApi(args);
        if (result.code === 200) {
          this.infoList = [...this.infoList, ...result.data.rows];
          this.pageNumber++;
          this.total = +result.data.total;
        }
      } catch (error) {
        uni.showToast({
          title: "系统出错啦，请稍后再试",
          icon: "none",
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        WAIT_APPROVE: "status-pending",
        PASS: "status-approved",
        REJECT: "status-rejected",
        CLOSED: "status-rejected",
      };
      return statusMap[status] || "";
    },

    // 跳转到详情页
    goToDetail(id, status) {
      const value = status.value;
      const type = value === "REJECT" ? "edit" : "info";
      uni.navigateTo({
        url: `/pages/engineer/billDetail?id=${id}&editType=${type}`,
      });
    },
    // 取消申请
    handleCancel(id) {
      uni.showModal({
        title: "提示",
        content: "确定要撤销该申请吗？",
        success: async (res) => {
          cancelReimbursementApi(id).then((res) => {
            this.$mHelper.toast("撤销成功");
            this.getRebackList(true);
          });
        },
      });
    },
    // 创建报销单
    createBill() {
      uni.navigateTo({
        url: `/pages/engineer/billDetail?editType=add`,
      });
    },
    switchTab(index) {
      if (this.currentTab === index) return;
      this.currentTab = index;
      this.pageNumber = 1;
      this.infoList = [];
      this.getRebackList();
    },
  },
  onLoad() {
    // this.getRebackList();
  },
};
</script>

<style lang="scss">
.container {
  padding: 20rpx;
  .tab-nav {
    width: 100%;
    display: flex;
    /* background-color: #fff; */
    /* padding: 20rpx 0; */
    margin-bottom: 20rpx;

    .tab-item {
      flex: 1;
      text-align: center;
      font-size: 28rpx;
      color: #666;
      position: relative;
      padding-bottom: 10rpx;

      &.active {
        color: #222222;
        font-weight: bold;

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background: linear-gradient(to right, #ee822f, #f6f6f6);
          /* background-color: #409eff; */
          border-radius: 2rpx;
        }
      }
    }
  }

  .reback-list {
    width: 100%;
  }
}

.reback-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .order-no {
      font-weight: bold;
    }
  }

  &__info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12rpx;
    color: #666;
    font-size: 28rpx;
  }
}

.status {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;

  &-pending {
    color: #f37b1d;
  }

  &-approved {
    color: #67c23a;
  }

  &-rejected {
    color: #f56c6c;
  }
  &-in {
    color: #409eff;
  }
}

.audit-btn {
  background-color: #409eff;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 6rpx;

  &:active {
    opacity: 0.8;
  }
}
.audit {
  background-color: #f37b1d;
  margin-left: auto;
}
.footer {
  width: 100%;
  padding: 18rpx 0 42rpx;
  display: flex;
  justify-content: center;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;

  .confirm-btn {
    width: 80%;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
    border-radius: 50px;
    color: #fff;
  }
}
.loading {
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  color: #ccc;
  font-size: 24upx;
  width: 100%;
  padding-bottom: 210rpx;
}
</style>

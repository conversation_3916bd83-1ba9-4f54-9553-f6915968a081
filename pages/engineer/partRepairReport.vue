<template>
	<view class="main">
		<view class="container">
			<view class="top-info">
				<view class="thumb" @tap.stop="previewImage(getImageUrl(params))">
					<image :src="getImageUrl(params)" mode=""></image>
				</view>
				<view class="item">
					<view class="title">
						<text class="two-omit">物品编号：{{ params.articleCode }}</text>
					</view>
					<view class="title">
						<text class="two-omit">物品名称：{{ params.articleName }}</text>
					</view>
					<view class="title">
						<text class="two-omit">制造渠道：{{ params.manufacturerChannel && params.manufacturerChannel.label }}</text>
					</view>
				</view>
			</view>
			<view class="content">
				<view class="info-item">
					<view class="info-row info-row-column">
						<text class="label">维修描述</text>
						<view class="input">
							<textarea
								v-model="params.description"
								:disabled="editType === 'info'"
								placeholder="请输入问题解决措施"
								confirm-type="done"
								:maxlength="500"
								class="textarea"
							></textarea>
						</view>
					</view>
					<view class="info-row info-row-column">
						<text class="label">维修图片</text>
						<view class="input repair-pics">
							<FileUpload @submit="handleUploadPic" :limit="9" :fileList="picUrls" :show-title="false" :disabled="editType === 'info'"></FileUpload>
						</view>
					</view>
					<view class="info-row info-row-column">
						<view class="add">
							<text class="label">更换零件清单</text>
							<view v-if="editType === 'edit'" class="add-part" @click="handleUsePart">
								<text class="add-icon">+</text>
								<text>换件登记</text>
							</view>
						</view>
						<view class="input">
							<view class="type-content">
								<view v-if="componentRepairReplaces && componentRepairReplaces.length > 0" class="goods" v-for="(list, index) in componentRepairReplaces" :key="list.id">
									<view class="thumb">
										<image :src="list.skuInfo.picUrl[0].url" mode="cover"></image>
									</view>
									<view class="item">
										<view class="goods-name">
											<view class="two-omit">
												<text>{{ list.itemName }}</text>
												<view v-if="editType === 'edit'" class="btn-box">
													<view class="btn delete" @click="deletePart(index)"></view>
												</view>
											</view>
											<view class="source">oem编号：{{ list.oemNumber }}</view>
											<view class="attribute">
												<view class="attr" v-for="(attr, index) in list.skuInfo.saleAttrVals" :key="index">
													<text>{{ attr.val }}</text>
												</view>
											</view>
											<view class="location">
												<SelectCheckBox
													style="width: 100%; height: 100%"
													v-model="list.location"
													:localdata="locationRange"
													data-key="text"
													data-value="value"
													multiple
													:collapse-tags-num="2"
													placeholder="请选择零件更换位置"
													:disabled="editType === 'info'"
												></SelectCheckBox>
											</view>
											<view class="info">
												<!-- 价格 -->
												<view class="goods-price">
													<view class="price">
														<text class="min">￥</text>
														<text class="max">{{ list.saleUnitPrice }}</text>
													</view>
												</view>
												<!-- 使用数量 -->
												<view class="num">
													<text v-if="editType === 'edit'" class="add" @tap.stop="changeBuyNumber(list, 'sub')">
														<text>-</text>
													</text>
													<view class="number">
														<text>{{ list.num }}</text>
													</view>
													<text v-if="editType === 'edit'" class="add" @tap.stop="changeBuyNumber(list, 'add')">
														<text>+</text>
													</text>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="footer" v-if="editType === 'edit'">
			<view class="confirm-btn" @click="handleRepairOk(false)">暂存维修报告</view>
			<view class="confirm-btn" @click="handleRepairOk(true)">提交维修报告</view>
		</view>
		<!-- 选择使用零件 -->
		<u-action-sheet title="确认使用零件" :show="showAction" :round="20" @close="handleCancel" safeAreaInsetBottom="true" closeOnClickOverlay>
			<view class="part-main">
				<view v-if="engineerPartDetailList && engineerPartDetailList.length > 0" class="goods" v-for="list in engineerPartDetailList" :key="list.id">
					<view class="left" @click.stop="checkGoods(list)">
						<view :class="selectParts.some((item) => item.id === list.id) ? 'check active iconfont icon-duihao' : 'check'"></view>
					</view>
					<view class="thumb">
						<image :src="list.skuInfo.picUrl[0].url" mode="cover"></image>
					</view>
					<view class="item">
						<view class="goods-name">
							<text class="two-omit">{{ list.itemName }}</text>
							<view class="source">oem编号：{{ list.oemNumber }}</view>
							<view class="attribute">
								<view class="attr" v-for="(attr, index) in list.skuInfo.saleAttrVals" :key="index">
									<text>{{ attr.val }}</text>
								</view>
							</view>
							<view class="info">
								<!-- 价格 -->
								<view class="goods-price">
									<view class="price">
										<text class="min">￥</text>
										<text class="max">{{ list.saleUnitPrice }}</text>
									</view>
								</view>
								<!-- 使用数量 -->
								<view class="num">
									<!-- 				<text class="add" @tap.stop="changeBuyNumber(list, 'sub')">
										<text>-</text>
									</text> -->
									<view class="number">
										<text>库存数量：{{ list.num }}</text>
									</view>
									<!-- 				<text class="add" @tap.stop="changeBuyNumber(list, 'add')">
										<text>+</text>
									</text> -->
								</view>
							</view>
						</view>
					</view>
				</view>
				<rf-empty :info="'暂无可使用零件！'" v-if="engineerPartDetailList.length === 0"></rf-empty>
			</view>
			<view class="sheet-footer">
				<view class="confirm-btn" @click.stop="confirmedUse">加入更换清单</view>
			</view>
			<!-- 页面加载-->
			<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
		</u-action-sheet>
	</view>
</template>

<script>
import FileUpload from '@/components/file-upload/index.vue';
import SelectCheckBox from '@/components/rf-select/rf-select.vue';
import { getCurrentModelsConsumables, dictTreeByCodeApi } from '@/api/workOrder.js';
import { submitRefurbishApi, getRefurbishDetailApi } from '@/api/system.js';
import { deepClone } from '@/utils/index.js';

export default {
	components: { FileUpload, SelectCheckBox },
	data() {
		return {
			editType: 'edit',
			id: '',
			params: {},
			picUrls: [],
			partList: [],
			componentRepairReplaces: [], // 确认使用零件列表
			engineerPartDetailList: [], // 拥有零件列表
			showAction: false,
			selectParts: [],
			usePartList: [],
			locationRange: [],
			loading: false
		};
	},
	computed: {
		getImageUrl() {
			return (good) => {
				return good.imageFiles && good.imageFiles[0] ? good.imageFiles[0].url : '';
			};
		}
	},
	onLoad(options) {
		this.editType = options.editType || 'edit';
		this.params = JSON.parse(decodeURIComponent(options.params));
		this.getDictTreeByCode();
		this.getReportDetail();
		this.getPartList();
	},
	methods: {
		// 机器修好入库
		handleRepairOk(val) {
			if (!this.params.description) {
				this.$mHelper.toast('请填写维修描述');
				return;
			}
			const contentText = val ? '确认提交维修报告吗' : '确认暂存维修报告';
			console.log(this.params);
			uni.showModal({
				title: '提示',
				content: contentText,
				success: (res) => {
					if (res.confirm) {
						const args = {
							...this.params,
							id: this.params.id,
							picUrls: this.picUrls,
							componentRepairReplaces: this.componentRepairReplaces,
							isCommit: val
						};
						submitRefurbishApi(args).then((res) => {
							uni.showToast({
								title: val ? '提交成功' : '暂存成功',
								icon: 'none'
							});
							setTimeout(() => {
								// uni.redirectTo({
								// 	url: '/pages/engineer/partRepair'
								// });
								uni.navigateBack({
									delta: 1
								});
							}, 300);
						});
					}
				}
			});
		},
		// 获取维修报告详情
		getReportDetail() {
			this.loading = true;
			getRefurbishDetailApi(this.params.id)
				.then((res) => {
					this.params = res.data;
					this.picUrls = res.data.picUrls;
					this.componentRepairReplaces = res.data.componentRepairReplaces;
				})
				.catch((err) => {
					this.$mHelper.toast(err.message || '系统出错啦，请稍后再试');
				})
				.finally(() => {
					this.loading = false;
				});
		},
		previewImage(url) {
			uni.previewImage({
				urls: [url]
			});
		},
		// 获取工程师仓库列表
		getPartList() {
			getCurrentModelsConsumables({}).then((res) => {
				this.engineerPartDetailList = res.data.map((item) => {
					return {
						...item,
						tempNum: item.num,
						itemStoreId: item.id
					};
				});
			});
		},
		checkGoods(goods) {
			let found = false;
			this.selectParts = this.selectParts.filter((item) => {
				if (item.id === goods.id) {
					found = true;
					return false;
				}
				return true;
			});
			if (!found) {
				this.selectParts.push(goods);
			}
		},
		handleUsePart() {
			this.selectParts = [];
			this.selectParts = this.componentRepairReplaces;
			this.showAction = true;
		},
		// 移除使用零件
		deletePart(index) {
			this.componentRepairReplaces.splice(index, 1);
		},
		confirmedUse() {
			this.handleCancel();
			const selectData = deepClone(this.selectParts);
			selectData.forEach((item) => {
				item.num = 1;
			});
			this.componentRepairReplaces = selectData;
		},
		handleCancel() {
			this.showAction = false;
		},
		async changeBuyNumber(data, type) {
			if (type == 'add') {
				if (data.num >= data.tempNum) {
					uni.showToast({
						title: '数量不能超过领取数量',
						icon: 'none'
					});
					return;
				}
				data.num = data.num + 1;
				this.selectParts.forEach((item) => {
					if (item.id === data.id) {
						item.num = data.num;
					}
				});
			} else if (type == 'sub') {
				if (data.num > 1) {
					data.num = data.num - 1;
					this.selectParts.forEach((item) => {
						if (item.id === data.id) {
							item.num = data.num;
						}
					});
				}
			}
		},
		// 维修图片上传
		handleUploadPic(data) {
			this.picUrls = data;
		},
		getDictTreeByCode() {
			dictTreeByCodeApi(11000)
				.then((res) => {
					this.locationRange = res.data.map((item) => {
						return {
							text: item.label,
							value: item.label
						};
					});
				})
				.catch((err) => {
					this.$mHelper.toast(err || '获取零件更换位置失败！');
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	min-height: 100vh;
	background-color: #fff;
	.container {
		flex: 1;
		padding: 30rpx;
		padding-bottom: 150rpx;
	}
	.top-info {
		display: flex;
		align-items: center;
		width: 100%;
		padding: 20rpx;
		background: #f8f8f8;
		border-radius: 12rpx;

		.thumb {
			display: flex;
			justify-content: center;
			width: 30%;
			height: 100%;
			margin-top: 20rpx;

			image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 10rpx;
			}
		}

		.item {
			padding: 10rpx 0;
			width: 70%;
			height: 100%;

			.title {
				display: flex;
				align-items: center;
				width: 100%;
				height: auto;

				text {
					font-size: 26rpx;
					color: #212121;
				}
			}
		}
	}
	.content {
		margin-top: 30rpx;
		width: 100%;
		.info-item {
			.info-row {
				display: flex;
				align-items: center;
				padding: 20rpx 0;
				border-bottom: 1px solid #f5f5f5;

				.label {
					width: 200rpx;
					font-size: 28rpx;
					color: #333;
				}

				.input {
					flex: 1;

					input {
						width: 100%;
						height: 60rpx;
						font-size: 28rpx;
					}
					&.repair-pics {
						padding: 20rpx 0;
					}
				}
			}
			.info-row-column {
				display: flex;
				flex-direction: column;
				align-items: normal;
				.textarea {
					height: 140rpx;
				}
				.add {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;
				}
				.add-part {
					display: flex;
					align-items: center;
					justify-content: center;
					height: 60rpx;
					font-size: 26rpx;
					cursor: pointer;
					color: #fff;
					background: #ff6e2e;
					border-radius: 30rpx;
					padding: 0 20rpx;

					.add-icon {
						font-size: 32rpx;
						margin-right: 10rpx;
					}
				}
			}
		}
	}
	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 130rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #f6f6f6;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		margin-top: 20rpx;
		.confirm-btn {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			margin: 0 30rpx;
			height: 60rpx;
			color: #ffffff;
			font-size: 24rpx;
			background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
			border-radius: 60rpx;
		}
	}
}
.sheet-footer {
	width: 100%;
	padding: 18rpx 0 42rpx;
	display: flex;
	justify-content: center;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;

	.confirm-btn {
		width: 80%;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 50px;
		color: #fff;
	}
}
.part-main {
	width: 100%;
	padding: 20rpx;
	background-color: #f6f6f6;
	max-height: 1000upx;
	min-height: 500upx;
	overflow: auto;
	padding-bottom: 160rpx;
}
.part-main,
.type-content {
	.goods {
		display: flex;
		width: 100%;
		height: 270rpx;
		padding: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;
		margin: 20rpx 0;
		&:first-child {
			margin-top: 0;
		}
		&:last-child {
			margin-bottom: 0;
		}

		.left {
			width: 10%;
			display: flex;
			align-items: center;
			.check {
				width: 35rpx;
				height: 35rpx;
				border: 1px solid #999999;
				border-radius: 50%;

				&.active {
					border: 1px solid #e5452f;
					color: #e5452f !important;
					line-height: 30rpx;
				}
			}
		}

		.thumb {
			display: flex;
			align-items: center;
			width: 30%;
			height: 100%;

			image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 10rpx;
			}
		}
		.item {
			display: flex;
			align-items: center;
			width: 80%;

			.goods-name {
				width: 100%;
				text-align: left;

				.two-omit {
					display: flex;
					justify-content: space-between;
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #0c0c0c;
					line-height: 39rpx;
					margin-top: 20rpx;
					.btn-box {
						width: 45rpx;
						// height: 60rpx;
						display: flex;
						align-items: center;
						justify-content: flex-end;

						.btn {
							width: 25rpx;
							height: 25rpx;
						}

						.delete {
							background: url('@/static/images/icon_delete.png');
							background-size: 100% 100%;
						}
					}
				}
				.source {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 300;
				}
				.desc {
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #535353;
					line-height: 39rpx;
				}

				.attribute {
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					gap: 15rpx;
					margin: 10rpx 0;

					.attr {
						display: flex;
						align-items: center;
						padding: 0 10rpx;
						height: 40rpx;
						background-color: #e8e8e8;
						border-radius: 10rpx;

						text {
							font-size: 24rpx;
							color: #333333;
						}
					}
				}

				.location {
					width: 100%;
					height: 70rpx;
					border: 1rpx solid #949494;
					padding: 0 10rpx;
					border-radius: 10rpx;
					font-size: 24rpx;
					margin: 10rpx 0;
					display: flex;
					align-items: center;
					::v-deep .uni-select {
						height: 50rpx !important;
						font-size: 24rpx !important;
						.uni-select__input-placeholder {
							font-size: 24rpx !important;
						}
					}
				}

				.info {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.goods-price {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						.price {
							text {
								color: #fe3b0f;
								font-size: 24rpx;
							}
							.min {
								font-size: 26rpx;
							}
							.max {
								font-size: 34rpx;
							}
						}
					}
					.num {
						display: flex;
						height: 40rpx;

						.add {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 60rpx;
							height: 40rpx;
							background-color: #ffffff;

							text {
								color: #212121;
								font-size: 40rpx;
							}
						}

						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 160rpx;
							height: 40rpx;
							// background-color: #f6f6f6;
							border-radius: 8rpx;
							text-align: center;

							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}
}
</style>

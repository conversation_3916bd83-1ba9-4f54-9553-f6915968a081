---
description: 
globs: 
alwaysApply: true
---
# Kotlin协程使用规范

## 协程作用域

- 应用级协程：使用`RepairOrderApp.appScope`
- ViewModel协程：使用`viewModelScope`
- 生命周期协程：使用`lifecycleScope`

## 协程上下文

### 调度器使用
- UI操作：使用`Dispatchers.Main`
- 网络请求：使用`Dispatchers.IO`
- 计算密集型：使用`Dispatchers.Default`
- 不阻塞当前线程：使用`Dispatchers.Unconfined`

### 作业管理
- 使用`SupervisorJob`允许子协程失败而不影响其他子协程
- 使用`Job()`创建可取消的作业
- 在组件销毁时取消所有协程

## 异常处理

### 异常处理策略
- 使用`try-catch`捕获协程内异常
- 使用`CoroutineExceptionHandler`处理未捕获异常
- 使用`supervisorScope`隔离异常传播

### 错误恢复
- 使用`withContext(NonCancellable)`执行清理操作
- 使用`retry`函数重试失败操作
- 实现指数退避重试策略

## 协程构建器

### launch
- 用于不返回结果的协程
- 适用于"发射后忘记"的操作
- 例如：发送分析事件、记录日志

### async
- 用于返回结果的协程
- 与`await()`配合使用获取结果
- 适用于并行执行多个操作

### runBlocking
- 仅用于测试或主函数
- 不应在生产代码中使用
- 会阻塞当前线程直到协程完成

## 协程最佳实践

### 取消检查
- 使用`ensureActive()`检查协程是否被取消
- 使用`isActive`属性检查协程状态
- 响应取消信号，避免浪费资源

### 超时处理
- 使用`withTimeout`或`withTimeoutOrNull`设置操作超时
- 为所有网络请求设置合理超时时间
- 处理超时异常并提供用户反馈

### 协程作用域扩展
- 为Activity和Fragment创建协程作用域扩展
- 使用`lifecycleScope.launchWhenStarted`等函数绑定生命周期
- 避免在不活跃时执行UI更新操作


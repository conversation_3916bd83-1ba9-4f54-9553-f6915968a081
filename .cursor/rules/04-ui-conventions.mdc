---
description: 
globs: UI,布局
alwaysApply: false
---
# UI开发规范
请注意审美和排版，符合大公司的审美和标准，这是一个根据苹果官方设计的APP

## UI结构

UI相关代码主要位于以下目录：
- [app/src/main/java/com/example/repairorderapp/ui/](mdc:app/src/main/java/com/example/repairorderapp/ui) - UI组件和Fragment
- [app/src/main/res/layout/](mdc:app/src/main/res/layout) - 布局文件

## 核心UI模块

- [app/src/main/java/com/example/repairorderapp/ui/profile/](mdc:app/src/main/java/com/example/repairorderapp/ui/profile) - 个人资料页面
- [app/src/main/java/com/example/repairorderapp/ui/orders/](mdc:app/src/main/java/com/example/repairorderapp/ui/orders) - 工单列表页面
- [app/src/main/java/com/example/repairorderapp/ui/map/](mdc:app/src/main/java/com/example/repairorderapp/ui/map) - 地图页面
- [app/src/main/java/com/example/repairorderapp/ui/report/](mdc:app/src/main/java/com/example/repairorderapp/ui/report) - 报告页面

## UI开发准则

### 架构模式
- 使用MVVM架构模式
- 使用ViewModel处理UI逻辑
- 使用LiveData或Flow进行数据绑定
- 使用DataBinding减少样板代码

### Fragment使用
- 使用单Activity多Fragment模式
- 使用Navigation Component管理Fragment导航
- 使用共享ViewModel在Fragment间共享数据
- Fragment间通信使用接口或ViewModel，避免直接引用

### 适配器开发
- 使用RecyclerView.Adapter显示列表数据
- 实现DiffUtil.Callback提高列表更新效率
- 使用ViewBinding简化视图绑定
- 使用ListAdapter处理数据变化

### 生命周期管理
- 在适当的生命周期方法中初始化和清理资源
- 使用LifecycleObserver监听生命周期事件
- 在onDestroy中取消协程和异步任务
- 使用onSaveInstanceState保存状态数据

### UI状态管理
- 定义清晰的UI状态类
- 使用密封类(sealed class)表示不同UI状态
- 处理加载、成功、错误和空数据状态
- 提供友好的错误提示和重试机制


---
description: 
globs: 
alwaysApply: true
---
# 工程结构指南

## 应用概述

这是一个维修工单管理Android应用，为工程师提供工单管理、地图导航、报告生成等功能。

## 主要入口点

- [app/src/main/java/com/example/repairorderapp/RepairOrderApp.kt](mdc:app/src/main/java/com/example/repairorderapp/RepairOrderApp.kt) - 应用程序类，提供全局访问点和应用程序级功能
- [app/src/main/java/com/example/repairorderapp/MainActivity.kt](mdc:app/src/main/java/com/example/repairorderapp/MainActivity.kt) - 主活动，包含底部导航和Fragment容器

## 核心目录结构

### 数据层
- [app/src/main/java/com/example/repairorderapp/data/api/](mdc:app/src/main/java/com/example/repairorderapp/data/api) - API接口
- @app/src/main/java/com/example/repairorderapp/network/ - 网络相关类
- [app/src/main/java/com/example/repairorderapp/model/](mdc:app/src/main/java/com/example/repairorderapp/model) - 数据模型类

### 表现层
- [app/src/main/java/com/example/repairorderapp/viewmodel/](mdc:app/src/main/java/com/example/repairorderapp/viewmodel) - ViewModel类
- [app/src/main/java/com/example/repairorderapp/ui/](mdc:app/src/main/java/com/example/repairorderapp/ui) - UI组件和Fragment

### 工具和服务
- [app/src/main/java/com/example/repairorderapp/util/](mdc:app/src/main/java/com/example/repairorderapp/util) - 工具类
- [app/src/main/java/com/example/repairorderapp/service/](mdc:app/src/main/java/com/example/repairorderapp/service) - 后台服务

## 主要依赖库
- Retrofit2 - 网络请求
- Room - 本地数据库
- Kotlin协程 - 异步编程
- 腾讯地图SDK - 地图功能
- Glide - 图片加载
- Signature-pad - 签名面板


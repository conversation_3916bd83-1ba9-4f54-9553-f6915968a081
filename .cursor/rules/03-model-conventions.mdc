---
description: 
globs: 
alwaysApply: false
---
# 数据模型规范

## 模型结构

数据模型主要位于以下目录：
- [app/src/main/java/com/example/repairorderapp/model/](mdc:app/src/main/java/com/example/repairorderapp/model) - 核心数据模型

## 核心模型文件

- [app/src/main/java/com/example/repairorderapp/model/RepairOrder.kt](mdc:app/src/main/java/com/example/repairorderapp/model/RepairOrder.kt) - 维修工单模型
- [app/src/main/java/com/example/repairorderapp/model/Engineer.kt](mdc:app/src/main/java/com/example/repairorderapp/model/Engineer.kt) - 工程师模型
- [app/src/main/java/com/example/repairorderapp/model/RepairReport.kt](mdc:app/src/main/java/com/example/repairorderapp/model/RepairReport.kt) - 维修报告模型
- [app/src/main/java/com/example/repairorderapp/model/MapMarker.kt](mdc:app/src/main/java/com/example/repairorderapp/model/MapMarker.kt) - 地图标记模型

## 模型开发准则

### 数据类设计
- 使用Kotlin数据类(data class)定义模型
- 使用`@Parcelize`注解和`Parcelable`接口使模型可序列化
- 为所有字段提供默认值，避免空指针异常

### 字段命名
- 使用驼峰命名法
- 字段名应明确表示其用途
- 使用有意义的类型，避免过度使用String类型

### 数据验证
- 在setter中添加数据验证逻辑
- 使用扩展函数提供验证方法
- 对于复杂验证，创建专门的验证类

### 关联数据处理
- 关联数据使用ID引用，避免嵌套完整对象
- 批量获取关联数据，避免N+1查询问题
- 设计合理的数据填充控制机制

### 数据转换
- 提供toMap()、fromMap()等转换方法
- 添加toJson()、fromJson()方法支持JSON序列化
- 使用扩展函数实现模型间的转换


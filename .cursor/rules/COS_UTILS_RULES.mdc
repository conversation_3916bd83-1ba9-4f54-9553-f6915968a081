---
description: 
globs: 
alwaysApply: true
---
# COS 云存储工具使用规范

## 简介

COS（Cloud Object Storage）云存储工具是一个统一的文件上传工具，用于将图片等文件上传到腾讯云 COS 存储服务。本工具封装了腾讯云 COS SDK 的复杂操作，提供简单易用的接口。

## 工具位置

- 工具类：`com.example.repairorderapp.util.CosUtils`
- 示例代码：`com.example.repairorderapp.util.CosUtilsExample`

## 主要功能

1. **单个图片上传**：压缩并上传单张图片到 COS 存储
2. **批量图片上传**：压缩并上传多张图片到 COS 存储
3. **COS 服务状态检查**：检查 COS 服务是否已初始化
4. **COS URL 构建**：根据对象路径构建完整的访问 URL

## 使用规范

### 1. 目录命名规范

为保持项目一致性，上传文件时应遵循以下目录命名规范：

| 业务模块 | 目录名称 |
|---------|---------|
| 维修报告 | `repair_report/` |
| 客户商务 | `customer_business/` |
| 工单附件 | `work_order/` |
| 设备信息 | `device_info/` |
| 用户头像 | `user_avatar/` |
| 其他附件 | `attachments/` |

### 2. 图片处理规范

- **最大尺寸**：默认为 1024px，可根据需要调整
- **压缩质量**：默认为 80，可根据需要在 60-90 之间调整
- **文件格式**：统一使用 JPEG 格式存储
- **文件命名**：系统自动使用 UUID 生成唯一文件名

### 3. 错误处理规范

- 所有 API 调用都应使用 try-catch 包裹
- 上传失败时应向用户提供友好的错误提示
- 应记录详细的错误日志以便排查问题
- 对于批量上传，应遵循"全部成功或全部失败"的原则

### 4. 协程使用规范

- 所有上传操作都是挂起函数，必须在协程作用域内调用
- 建议使用 lifecycleScope 或 viewModelScope 作为协程作用域
- 长时间操作应显示进度对话框，避免用户误操作

## 代码示例

### 单张图片上传

```kotlin
lifecycleScope.launch {
    try {
        val result = CosUtils.uploadImage(
            context = requireContext(),
            uri = imageUri,
            directory = "repair_report/"
        )
        
        // 使用上传结果
        val imageUrl = result.url  // 图片URL
        val cosKey = result.key    // COS对象键
        
    } catch (e: Exception) {
        // 处理错误
    }
}
```

### 多张图片上传

```kotlin
lifecycleScope.launch {
    try {
        val results = CosUtils.uploadImages(
            context = requireContext(),
            uris = imageUris,
            directory = "customer_business/"
        )
        
        // 使用上传结果
        val imageUrls = results.map { it.url }
        
    } catch (e: Exception) {
        // 处理错误
    }
}
```

## 注意事项

1. **初始化依赖**：COS 工具依赖于 `RepairOrderApp` 中的初始化，使用前应确保应用已完成初始化
2. **权限要求**：使用前确保应用具有网络访问权限和读取存储权限
3. **内存管理**：图片处理过程中会创建临时文件，工具会自动清理，但应注意内存使用
4. **网络状态**：应在有网络连接的情况下使用，建议添加网络状态检查
5. **文件大小**：虽然工具会自动压缩图片，但仍建议控制原始图片大小，避免处理超大图片

## 迁移指南

对于现有代码中直接使用 COS SDK 的部分，建议按以下步骤迁移到 CosUtils：

1. 识别当前直接使用 COS SDK 的代码段
2. 确定对应的 CosUtils 方法
3. 将代码重构为使用 CosUtils 的形式
4. 确保错误处理逻辑完整
5. 测试新代码功能是否与原代码一致

## 版本历史

- v1.0.0 (2023-05-20): 初始版本，提供基本的图片上传功能 
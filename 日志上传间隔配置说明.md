# 日志上传间隔配置说明

## 📊 **配置概览**

### 🔧 **开发环境 (Debug)**
- **日志上传间隔**: 1分钟 (60000ms)
- **位置日志间隔**: 30秒 (30000ms)
- **WorkManager间隔**: 1分钟
- **日志级别**: DEBUG
- **最大文件大小**: 5MB
- **最大文件数量**: 3个
- **详细日志**: 启用

### 🚀 **生产环境 (Release)**
- **日志上传间隔**: 1小时 (3600000ms) - 可通过API实时调整
- **位置日志间隔**: 5分钟 (300000ms)
- **WorkManager间隔**: 60分钟
- **日志级别**: INFO
- **最大文件大小**: 10MB
- **最大文件数量**: 5个
- **详细日志**: 关闭

## 🎛️ **动态配置机制**

### Android端配置层级
```kotlin
1. 构建时默认配置 (BuildConfigHelper)
   ↓
2. 本地缓存配置 (LogConfig)
   ↓
3. 远程服务器配置 (LogConfigApi)
```

### 配置优先级
1. **远程配置** (最高优先级) - 实时生效
2. **本地缓存配置** - 离线时使用
3. **构建默认配置** - 兜底配置

## 🔌 **后端API实时调整**

### 1. 获取当前配置
```http
GET /logcontrol/config/get
Headers:
  X-Device-Id: {deviceId}
  X-App-Version: {appVersion}

Response:
{
  "id": 1,
  "configName": "production",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 300000,
  "enablePerformanceLog": true,
  "enableCrashLog": true,
  "enableBusinessLog": true,
  "logUploadInterval": 3600000,  // 可调整此值
  "maxLogFileSize": 10485760,
  "maxLogFiles": 5,
  "configVersion": "1.0.0",
  "isActive": true
}
```

### 2. 更新配置 (后端管理接口)
```http
POST /logcontrol/config/update
Content-Type: application/json

{
  "configName": "production",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 300000,
  "enablePerformanceLog": true,
  "enableCrashLog": true,
  "enableBusinessLog": true,
  "logUploadInterval": 1800000,  // 调整为30分钟
  "maxLogFileSize": 10485760,
  "maxLogFiles": 5
}
```

### 3. 激活配置
```http
POST /logcontrol/config/activate/{configId}
```

## 📱 **Android端配置检查**

### 查看当前配置
在测试界面点击 **"显示构建配置信息"** 按钮，可以看到：
- 当前构建类型和环境
- 实际使用的上传间隔
- 所有配置参数的当前值
- 环境特定的配置说明

### 配置更新流程
1. **自动检查**: 应用启动时自动检查配置更新
2. **定期检查**: 每次上传日志时检查配置
3. **手动刷新**: 通过测试界面手动刷新配置

## 🎯 **实际使用场景**

### 开发调试场景
```kotlin
// 开发环境自动配置
logUploadInterval = 60000L      // 1分钟，便于实时调试
locationLogInterval = 30000L    // 30秒，快速验证位置功能
logLevel = "DEBUG"              // 详细日志输出
```

### 生产环境场景
```kotlin
// 生产环境默认配置
logUploadInterval = 3600000L    // 1小时，平衡性能和及时性
locationLogInterval = 300000L   // 5分钟，节省电量和流量
logLevel = "INFO"               // 关键信息日志
```

### 特殊调试场景
通过后端API可以临时调整生产环境配置：
- **紧急调试**: 调整为5-10分钟上传间隔
- **性能测试**: 调整为30分钟间隔
- **问题排查**: 临时启用DEBUG级别日志

## 🔄 **配置更新示例**

### 场景1: 紧急问题排查
```json
{
  "logUploadInterval": 300000,    // 5分钟
  "logLevel": "DEBUG",            // 启用详细日志
  "enablePerformanceLog": true,   // 启用性能日志
  "configVersion": "1.1.0"
}
```

### 场景2: 正常运营优化
```json
{
  "logUploadInterval": 1800000,   // 30分钟
  "logLevel": "INFO",             // 标准日志级别
  "maxLogFileSize": 15728640,     // 增加到15MB
  "configVersion": "1.2.0"
}
```

### 场景3: 节能模式
```json
{
  "logUploadInterval": 7200000,   // 2小时
  "locationLogInterval": 600000,  // 10分钟
  "enablePerformanceLog": false,  // 关闭性能日志
  "configVersion": "1.3.0"
}
```

## 📊 **监控和分析**

### 配置效果监控
- **上传频率**: 监控实际日志上传频率
- **数据量**: 监控每次上传的日志数量
- **网络消耗**: 监控上传产生的流量
- **电池影响**: 监控对设备电量的影响

### 配置优化建议
- **高活跃用户**: 15-30分钟间隔
- **普通用户**: 1-2小时间隔
- **低活跃用户**: 2-4小时间隔
- **特殊设备**: 根据设备性能调整

## 🛠️ **实施步骤**

### 1. 开发环境验证
1. 编译并运行应用
2. 查看日志确认1分钟上传间隔
3. 使用测试界面验证功能

### 2. 后端API部署
1. 部署后端配置管理接口
2. 创建默认生产环境配置
3. 测试配置更新功能

### 3. 生产环境部署
1. 发布Release版本应用
2. 验证1小时默认间隔
3. 通过API调整为合适间隔

### 4. 监控和优化
1. 监控上传频率和数据量
2. 根据实际情况调整配置
3. 持续优化配置策略

通过这套配置机制，可以在开发环境快速调试（1分钟间隔），在生产环境灵活调整（API实时控制），实现最佳的开发体验和生产性能平衡。

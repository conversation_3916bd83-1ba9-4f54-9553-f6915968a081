# 日志配置定向下发 - Android开发指导

## 📋 项目概述

Android端需要支持从服务器获取定向配置，并根据配置动态调整日志行为。主要包括配置获取、本地缓存、配置应用等功能。

## 🎯 核心功能

1. **配置获取**: 从服务器获取用户/设备专属配置
2. **本地缓存**: 缓存配置避免频繁网络请求
3. **配置应用**: 根据配置动态调整日志行为
4. **降级处理**: 网络异常时使用本地默认配置

## 🔧 Android实现

### 1. 数据模型定义

#### 1.1 LogConfig.kt

```kotlin
package com.example.repairorderapp.data.model

import com.google.gson.annotations.SerializedName

/**
 * 日志配置数据类
 */
data class LogConfig(
    @SerializedName("id")
    val id: String? = null,
    
    @SerializedName("configName")
    val configName: String? = null,
    
    @SerializedName("logLevel")
    val logLevel: String = "INFO",
    
    @SerializedName("enableLocationLog")
    val enableLocationLog: Boolean = true,
    
    @SerializedName("locationLogInterval")
    val locationLogInterval: Int = 3000,
    
    @SerializedName("logUploadInterval")
    val logUploadInterval: Int = 3600,
    
    @SerializedName("maxLogFiles")
    val maxLogFiles: Int = 5,
    
    @SerializedName("configVersion")
    val configVersion: String? = null,
    
    @SerializedName("isActive")
    val isActive: Boolean = true
) {
    // 提供默认值的辅助方法
    fun getLogLevelOrDefault(): String = logLevel.takeIf { it.isNotBlank() } ?: "INFO"
    fun getLocationLogIntervalOrDefault(): Int = if (locationLogInterval > 0) locationLogInterval else 3000
    fun getLogUploadIntervalOrDefault(): Int = if (logUploadInterval > 0) logUploadInterval else 3600
    fun getMaxLogFilesOrDefault(): Int = if (maxLogFiles > 0) maxLogFiles else 5
}

/**
 * 配置响应包装类
 */
data class LogConfigResponse(
    val code: Int,
    val message: String,
    val data: LogConfig?
)

/**
 * 配置模板数据类
 */
data class ConfigTemplate(
    @SerializedName("templateName")
    val templateName: String,
    
    @SerializedName("displayName")
    val displayName: String,
    
    @SerializedName("logLevel")
    val logLevel: String,
    
    @SerializedName("enableLocationLog")
    val enableLocationLog: Boolean,
    
    @SerializedName("locationLogInterval")
    val locationLogInterval: Int,
    
    @SerializedName("logUploadInterval")
    val logUploadInterval: Int,
    
    @SerializedName("maxLogFiles")
    val maxLogFiles: Int,
    
    @SerializedName("description")
    val description: String
)
```

### 2. 网络接口定义

#### 2.1 LogConfigApi.kt

```kotlin
package com.example.repairorderapp.network.api

import com.example.repairorderapp.data.model.LogConfig
import com.example.repairorderapp.data.model.LogConfigResponse
import com.example.repairorderapp.data.model.ConfigTemplate
import retrofit2.Response
import retrofit2.http.*

/**
 * 日志配置API接口
 */
interface LogConfigApi {
    
    /**
     * 获取配置（支持用户/设备定向）
     */
    @GET("logcontrol/config/get")
    suspend fun getConfig(
        @Header("X-User-Id") userId: String? = null,
        @Header("X-Device-Id") deviceId: String? = null,
        @Header("X-App-Version") appVersion: String? = null,
        @Header("x-auth-token") authToken: String? = null
    ): Response<LogConfigResponse>
    
    /**
     * 获取配置模板列表
     */
    @GET("logcontrol/config/templates")
    suspend fun getConfigTemplates(
        @Header("x-auth-token") authToken: String? = null
    ): Response<List<ConfigTemplate>>
    
    /**
     * 根据配置名称获取配置
     */
    @GET("logcontrol/config/get-by-name")
    suspend fun getConfigByName(
        @Query("configName") configName: String,
        @Header("x-auth-token") authToken: String? = null
    ): Response<LogConfigResponse>
}
```

### 3. 配置管理器

#### 3.1 RemoteConfigManager.kt

```kotlin
package com.example.repairorderapp.manager

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.repairorderapp.data.model.LogConfig
import com.example.repairorderapp.network.api.LogConfigApi
import com.example.repairorderapp.utils.DeviceUtils
import com.example.repairorderapp.utils.UserManager
import com.google.gson.Gson
import kotlinx.coroutines.*
import java.util.concurrent.TimeUnit

/**
 * 远程配置管理器
 */
class RemoteConfigManager(
    private val context: Context,
    private val logConfigApi: LogConfigApi
) {
    companion object {
        private const val TAG = "RemoteConfigManager"
        private const val PREF_NAME = "log_config"
        private const val KEY_CONFIG = "current_config"
        private const val KEY_CONFIG_VERSION = "config_version"
        private const val KEY_LAST_UPDATE = "last_update_time"
        private const val KEY_UPDATE_INTERVAL = "update_interval"
        
        // 默认更新间隔（毫秒）
        private const val DEFAULT_UPDATE_INTERVAL = 30 * 60 * 1000L // 30分钟
        
        @Volatile
        private var INSTANCE: RemoteConfigManager? = null
        
        fun getInstance(context: Context, api: LogConfigApi): RemoteConfigManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteConfigManager(context.applicationContext, api).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private var currentConfig: LogConfig? = null
    private var configUpdateJob: Job? = null
    
    /**
     * 初始化配置管理器
     */
    fun initialize() {
        Log.d(TAG, "初始化远程配置管理器")
        
        // 加载本地缓存的配置
        loadCachedConfig()
        
        // 启动定期更新
        startPeriodicUpdate()
        
        // 立即检查更新
        checkAndUpdateConfig()
    }
    
    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): LogConfig {
        return currentConfig ?: getDefaultConfig()
    }
    
    /**
     * 手动刷新配置
     */
    fun refreshConfig() {
        Log.d(TAG, "手动刷新配置")
        checkAndUpdateConfig()
    }
    
    /**
     * 检查并更新配置
     */
    private fun checkAndUpdateConfig() {
        scope.launch {
            try {
                val userId = UserManager.getCurrentUserId()
                val deviceId = DeviceUtils.getDeviceId(context)
                val appVersion = DeviceUtils.getAppVersion(context)
                val authToken = UserManager.getAuthToken()
                
                Log.d(TAG, "请求配置: userId=$userId, deviceId=$deviceId")
                
                val response = logConfigApi.getConfig(userId, deviceId, appVersion, authToken)
                
                if (response.isSuccessful) {
                    val configResponse = response.body()
                    if (configResponse?.code == 200 && configResponse.data != null) {
                        val newConfig = configResponse.data
                        Log.d(TAG, "收到新配置: ${gson.toJson(newConfig)}")
                        
                        // 检查配置是否有更新
                        if (isConfigUpdated(newConfig)) {
                            Log.i(TAG, "发现配置更新")
                            updateConfig(newConfig)
                        } else {
                            Log.d(TAG, "配置无变化")
                        }
                    } else {
                        Log.w(TAG, "配置响应异常: ${configResponse?.message}")
                    }
                } else {
                    Log.e(TAG, "获取配置失败: ${response.code()} - ${response.message()}")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "检查配置更新失败", e)
            }
        }
    }
    
    /**
     * 检查配置是否有更新
     */
    private fun isConfigUpdated(newConfig: LogConfig): Boolean {
        val currentVersion = sharedPreferences.getString(KEY_CONFIG_VERSION, "")
        val newVersion = newConfig.configVersion
        
        return currentVersion != newVersion
    }
    
    /**
     * 更新配置
     */
    private fun updateConfig(newConfig: LogConfig) {
        try {
            // 验证配置有效性
            if (!isConfigValid(newConfig)) {
                Log.w(TAG, "配置无效，使用默认配置")
                return
            }
            
            // 保存到本地
            saveConfig(newConfig)
            
            // 更新内存中的配置
            currentConfig = newConfig
            
            // 应用新配置
            applyConfig(newConfig)
            
            Log.i(TAG, "配置更新成功: ${newConfig.configVersion}")
            
        } catch (e: Exception) {
            Log.e(TAG, "更新配置失败", e)
        }
    }
    
    /**
     * 验证配置有效性
     */
    private fun isConfigValid(config: LogConfig): Boolean {
        return config.logLevel.isNotBlank() &&
               config.locationLogInterval > 0 &&
               config.logUploadInterval > 0 &&
               config.maxLogFiles > 0
    }
    
    /**
     * 保存配置到本地
     */
    private fun saveConfig(config: LogConfig) {
        try {
            val configJson = gson.toJson(config)
            sharedPreferences.edit()
                .putString(KEY_CONFIG, configJson)
                .putString(KEY_CONFIG_VERSION, config.configVersion)
                .putLong(KEY_LAST_UPDATE, System.currentTimeMillis())
                .apply()
            
            Log.d(TAG, "配置已保存到本地")
            
        } catch (e: Exception) {
            Log.e(TAG, "保存配置失败", e)
        }
    }
    
    /**
     * 加载本地缓存的配置
     */
    private fun loadCachedConfig() {
        try {
            val configJson = sharedPreferences.getString(KEY_CONFIG, null)
            if (!configJson.isNullOrBlank()) {
                currentConfig = gson.fromJson(configJson, LogConfig::class.java)
                Log.d(TAG, "加载本地配置成功: ${currentConfig?.configVersion}")
            } else {
                Log.d(TAG, "无本地配置，使用默认配置")
                currentConfig = getDefaultConfig()
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载本地配置失败", e)
            currentConfig = getDefaultConfig()
        }
    }
    
    /**
     * 应用配置
     */
    private fun applyConfig(config: LogConfig) {
        try {
            // 更新日志级别
            LogManager.setLogLevel(config.getLogLevelOrDefault())
            
            // 更新位置日志配置
            LocationLogManager.setEnabled(config.enableLocationLog)
            LocationLogManager.setInterval(config.getLocationLogIntervalOrDefault())
            
            // 更新日志上传配置
            LogUploadManager.setUploadInterval(config.getLogUploadIntervalOrDefault())
            LogUploadManager.setMaxLogFiles(config.getMaxLogFilesOrDefault())
            
            Log.i(TAG, "配置应用成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "应用配置失败", e)
        }
    }
    
    /**
     * 启动定期更新
     */
    private fun startPeriodicUpdate() {
        configUpdateJob?.cancel()
        
        configUpdateJob = scope.launch {
            while (isActive) {
                try {
                    delay(DEFAULT_UPDATE_INTERVAL)
                    checkAndUpdateConfig()
                } catch (e: Exception) {
                    Log.e(TAG, "定期更新配置失败", e)
                }
            }
        }
        
        Log.d(TAG, "启动定期配置更新")
    }
    
    /**
     * 获取默认配置
     */
    private fun getDefaultConfig(): LogConfig {
        return LogConfig(
            id = "default",
            configName = "default",
            logLevel = "INFO",
            enableLocationLog = true,
            locationLogInterval = 3000,
            logUploadInterval = 3600,
            maxLogFiles = 5,
            configVersion = "1.0.0",
            isActive = true
        )
    }
    
    /**
     * 销毁管理器
     */
    fun destroy() {
        Log.d(TAG, "销毁远程配置管理器")
        configUpdateJob?.cancel()
        scope.cancel()
    }
}
```

### 4. 配置应用

#### 4.1 LogManager.kt

```kotlin
package com.example.repairorderapp.manager

import android.util.Log

/**
 * 日志管理器
 */
object LogManager {
    
    private const val TAG = "LogManager"
    
    enum class LogLevel(val value: Int) {
        DEBUG(0),
        INFO(1),
        WARN(2),
        ERROR(3)
    }
    
    private var currentLogLevel = LogLevel.INFO
    
    /**
     * 设置日志级别
     */
    fun setLogLevel(level: String) {
        currentLogLevel = when (level.uppercase()) {
            "DEBUG" -> LogLevel.DEBUG
            "INFO" -> LogLevel.INFO
            "WARN" -> LogLevel.WARN
            "ERROR" -> LogLevel.ERROR
            else -> LogLevel.INFO
        }
        Log.i(TAG, "日志级别设置为: $level")
    }
    
    /**
     * 输出Debug日志
     */
    fun d(tag: String, message: String) {
        if (currentLogLevel.value <= LogLevel.DEBUG.value) {
            Log.d(tag, message)
        }
    }
    
    /**
     * 输出Info日志
     */
    fun i(tag: String, message: String) {
        if (currentLogLevel.value <= LogLevel.INFO.value) {
            Log.i(tag, message)
        }
    }
    
    /**
     * 输出Warning日志
     */
    fun w(tag: String, message: String) {
        if (currentLogLevel.value <= LogLevel.WARN.value) {
            Log.w(tag, message)
        }
    }
    
    /**
     * 输出Error日志
     */
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        if (currentLogLevel.value <= LogLevel.ERROR.value) {
            if (throwable != null) {
                Log.e(tag, message, throwable)
            } else {
                Log.e(tag, message)
            }
        }
    }
}
```

#### 4.2 LocationLogManager.kt

```kotlin
package com.example.repairorderapp.manager

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*

/**
 * 位置日志管理器
 */
class LocationLogManager(private val context: Context) {
    
    companion object {
        private const val TAG = "LocationLogManager"
        
        @Volatile
        private var INSTANCE: LocationLogManager? = null
        
        fun getInstance(context: Context): LocationLogManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LocationLogManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        fun setEnabled(enabled: Boolean) {
            getInstance(context).setLocationLogEnabled(enabled)
        }
        
        fun setInterval(interval: Int) {
            getInstance(context).setLocationLogInterval(interval)
        }
    }
    
    private var isEnabled = true
    private var interval = 3000 // 默认3秒
    private var locationJob: Job? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 设置位置日志开关
     */
    fun setLocationLogEnabled(enabled: Boolean) {
        if (isEnabled != enabled) {
            isEnabled = enabled
            Log.i(TAG, "位置日志${if (enabled) "启用" else "禁用"}")
            
            if (enabled) {
                startLocationLogging()
            } else {
                stopLocationLogging()
            }
        }
    }
    
    /**
     * 设置位置日志间隔
     */
    fun setLocationLogInterval(newInterval: Int) {
        if (interval != newInterval && newInterval > 0) {
            interval = newInterval
            Log.i(TAG, "位置日志间隔设置为: ${interval}ms")
            
            // 如果正在运行，重启以应用新间隔
            if (isEnabled && locationJob?.isActive == true) {
                stopLocationLogging()
                startLocationLogging()
            }
        }
    }
    
    /**
     * 启动位置日志记录
     */
    private fun startLocationLogging() {
        stopLocationLogging()
        
        locationJob = scope.launch {
            while (isActive && isEnabled) {
                try {
                    // 获取位置信息并记录
                    recordLocationLog()
                    delay(interval.toLong())
                } catch (e: Exception) {
                    Log.e(TAG, "位置日志记录失败", e)
                }
            }
        }
        
        Log.d(TAG, "位置日志记录已启动")
    }
    
    /**
     * 停止位置日志记录
     */
    private fun stopLocationLogging() {
        locationJob?.cancel()
        locationJob = null
        Log.d(TAG, "位置日志记录已停止")
    }
    
    /**
     * 记录位置日志
     */
    private suspend fun recordLocationLog() {
        // 实际的位置获取和日志记录逻辑
        // 这里只是示例
        Log.d(TAG, "记录位置日志")
    }
}
```

### 5. 应用集成

#### 5.1 Application.kt

```kotlin
package com.example.repairorderapp

import android.app.Application
import com.example.repairorderapp.manager.RemoteConfigManager
import com.example.repairorderapp.network.ApiClient

class MyApplication : Application() {
    
    private lateinit var remoteConfigManager: RemoteConfigManager
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化远程配置管理器
        initRemoteConfigManager()
    }
    
    private fun initRemoteConfigManager() {
        val logConfigApi = ApiClient.getInstance().create(LogConfigApi::class.java)
        remoteConfigManager = RemoteConfigManager.getInstance(this, logConfigApi)
        remoteConfigManager.initialize()
    }
    
    override fun onTerminate() {
        super.onTerminate()
        remoteConfigManager.destroy()
    }
}
```

## 🚀 使用示例

### 1. 获取配置

```kotlin
// 在Activity或Fragment中
class MainActivity : AppCompatActivity() {
    
    private lateinit var configManager: RemoteConfigManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取配置管理器实例
        val api = ApiClient.getInstance().create(LogConfigApi::class.java)
        configManager = RemoteConfigManager.getInstance(this, api)
        
        // 获取当前配置
        val currentConfig = configManager.getCurrentConfig()
        Log.i("MainActivity", "当前配置: ${currentConfig.logLevel}")
        
        // 手动刷新配置
        configManager.refreshConfig()
    }
}
```

### 2. 使用配置化日志

```kotlin
class SomeActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "SomeActivity"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 使用配置化的日志管理器
        LogManager.d(TAG, "Debug信息") // 根据配置决定是否输出
        LogManager.i(TAG, "Info信息")
        LogManager.w(TAG, "Warning信息")
        LogManager.e(TAG, "Error信息")
    }
}
```

## ⚠️ 注意事项

1. **权限申请**: 确保应用有网络访问权限
2. **异常处理**: 网络异常时使用本地缓存配置
3. **性能优化**: 避免频繁的网络请求
4. **数据安全**: 敏感配置信息需要加密存储
5. **版本兼容**: 处理配置格式的版本兼容性

## 📊 测试验证

```kotlin
// 测试配置获取
@Test
fun testConfigFetch() {
    val config = configManager.getCurrentConfig()
    assertNotNull(config)
    assertTrue(config.logLevel.isNotBlank())
}

// 测试配置应用
@Test
fun testConfigApplication() {
    val config = LogConfig(logLevel = "DEBUG")
    LogManager.setLogLevel(config.logLevel)
    // 验证日志级别是否正确应用
}
```

这个Android实现方案提供了完整的配置获取、缓存、应用功能，确保应用能够根据服务器配置动态调整日志行为。
